{"ast": null, "code": "import { chain as $ff5963eb1fccf552$export$e08e3b67e392101e } from \"./chain.mjs\";\nimport { mergeIds as $bdb11010cef70236$export$cd8c9cb68f842629 } from \"./useId.mjs\";\nimport $7jXr9$clsx from \"clsx\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $3ef42575df84b30b$export$9d1611c77c2fe928(...args) {\n  // Start with a base clone of the first argument. This is a lot faster than starting\n  // with an empty object and adding properties as we go.\n  let result = {\n    ...args[0]\n  };\n  for (let i = 1; i < args.length; i++) {\n    let props = args[i];\n    for (let key in props) {\n      let a = result[key];\n      let b = props[key];\n      // Chain events\n      if (typeof a === 'function' && typeof b === 'function' &&\n      // This is a lot faster than a regex.\n      key[0] === 'o' && key[1] === 'n' && key.charCodeAt(2) >= /* 'A' */65 && key.charCodeAt(2) <= /* 'Z' */90) result[key] = (0, $ff5963eb1fccf552$export$e08e3b67e392101e)(a, b);else if ((key === 'className' || key === 'UNSAFE_className') && typeof a === 'string' && typeof b === 'string') result[key] = (0, $7jXr9$clsx)(a, b);else if (key === 'id' && a && b) result.id = (0, $bdb11010cef70236$export$cd8c9cb68f842629)(a, b);else result[key] = b !== undefined ? b : a;\n    }\n  }\n  return result;\n}\nexport { $3ef42575df84b30b$export$9d1611c77c2fe928 as mergeProps };", "map": {"version": 3, "names": ["$3ef42575df84b30b$export$9d1611c77c2fe928", "args", "result", "i", "length", "props", "key", "a", "b", "charCodeAt", "$ff5963eb1fccf552$export$e08e3b67e392101e", "$7jXr9$clsx", "id", "$bdb11010cef70236$export$cd8c9cb68f842629", "undefined"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\mergeProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain} from './chain';\nimport clsx from 'clsx';\nimport {mergeIds} from './useId';\n\ninterface Props {\n  [key: string]: any\n}\n\ntype PropsArg = Props | null | undefined;\n\n// taken from: https://stackoverflow.com/questions/51603250/typescript-3-parameter-list-intersection-type/51604379#51604379\ntype TupleTypes<T> = { [P in keyof T]: T[P] } extends { [key: number]: infer V } ? NullToObject<V> : never;\ntype NullToObject<T> = T extends (null | undefined) ? {} : T;\n\ntype UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n/**\n * Merges multiple props objects together. Event handlers are chained,\n * classNames are combined, and ids are deduplicated - different ids\n * will trigger a side-effect and re-render components hooked up with `useId`.\n * For all other props, the last prop object overrides all previous ones.\n * @param args - Multiple sets of props to merge together.\n */\nexport function mergeProps<T extends PropsArg[]>(...args: T): UnionToIntersection<TupleTypes<T>> {\n  // Start with a base clone of the first argument. This is a lot faster than starting\n  // with an empty object and adding properties as we go.\n  let result: Props = {...args[0]};\n  for (let i = 1; i < args.length; i++) {\n    let props = args[i];\n    for (let key in props) {\n      let a = result[key];\n      let b = props[key];\n\n      // Chain events\n      if (\n        typeof a === 'function' &&\n        typeof b === 'function' &&\n        // This is a lot faster than a regex.\n        key[0] === 'o' &&\n        key[1] === 'n' &&\n        key.charCodeAt(2) >= /* 'A' */ 65 &&\n        key.charCodeAt(2) <= /* 'Z' */ 90\n      ) {\n        result[key] = chain(a, b);\n\n        // Merge classnames, sometimes classNames are empty string which eval to false, so we just need to do a type check\n      } else if (\n        (key === 'className' || key === 'UNSAFE_className') &&\n        typeof a === 'string' &&\n        typeof b === 'string'\n      ) {\n        result[key] = clsx(a, b);\n      } else if (key === 'id' && a && b) {\n        result.id = mergeIds(a, b);\n        // Override others\n      } else {\n        result[key] = b !== undefined ? b : a;\n      }\n    }\n  }\n\n  return result as UnionToIntersection<TupleTypes<T>>;\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;AAmCO,SAASA,0CAAiC,GAAGC,IAAO;EACzD;EACA;EACA,IAAIC,MAAA,GAAgB;IAAC,GAAGD,IAAI,CAAC;EAAE;EAC/B,KAAK,IAAIE,CAAA,GAAI,GAAGA,CAAA,GAAIF,IAAA,CAAKG,MAAM,EAAED,CAAA,IAAK;IACpC,IAAIE,KAAA,GAAQJ,IAAI,CAACE,CAAA,CAAE;IACnB,KAAK,IAAIG,GAAA,IAAOD,KAAA,EAAO;MACrB,IAAIE,CAAA,GAAIL,MAAM,CAACI,GAAA,CAAI;MACnB,IAAIE,CAAA,GAAIH,KAAK,CAACC,GAAA,CAAI;MAElB;MACA,IACE,OAAOC,CAAA,KAAM,cACb,OAAOC,CAAA,KAAM;MACb;MACAF,GAAG,CAAC,EAAE,KAAK,OACXA,GAAG,CAAC,EAAE,KAAK,OACXA,GAAA,CAAIG,UAAU,CAAC,MAAM,SAAU,MAC/BH,GAAA,CAAIG,UAAU,CAAC,MAAM,SAAU,IAE/BP,MAAM,CAACI,GAAA,CAAI,GAAG,IAAAI,yCAAI,EAAEH,CAAA,EAAGC,CAAA,OAGlB,IACL,CAACF,GAAA,KAAQ,eAAeA,GAAA,KAAQ,kBAAiB,KACjD,OAAOC,CAAA,KAAM,YACb,OAAOC,CAAA,KAAM,UAEbN,MAAM,CAACI,GAAA,CAAI,GAAG,IAAAK,WAAG,EAAEJ,CAAA,EAAGC,CAAA,OACjB,IAAIF,GAAA,KAAQ,QAAQC,CAAA,IAAKC,CAAA,EAC9BN,MAAA,CAAOU,EAAE,GAAG,IAAAC,yCAAO,EAAEN,CAAA,EAAGC,CAAA,OAGxBN,MAAM,CAACI,GAAA,CAAI,GAAGE,CAAA,KAAMM,SAAA,GAAYN,CAAA,GAAID,CAAA;IAExC;EACF;EAEA,OAAOL,MAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}