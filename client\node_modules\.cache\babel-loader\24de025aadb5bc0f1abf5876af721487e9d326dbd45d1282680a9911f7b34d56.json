{"ast": null, "code": "import { isIOS as $7R18e$isIOS, getOwnerDocument as $7R18e$getOwnerDocument, runAfterTransition as $7R18e$runAfterTransition } from \"@react-aria/utils\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n// Note that state only matters here for iOS. Non-iOS gets user-select: none applied to the target element\n// rather than at the document level so we just need to apply/remove user-select: none for each pressed element individually\nlet $14c0b72509d70225$var$state = 'default';\nlet $14c0b72509d70225$var$savedUserSelect = '';\nlet $14c0b72509d70225$var$modifiedElementMap = new WeakMap();\nfunction $14c0b72509d70225$export$16a4697467175487(target) {\n  if ((0, $7R18e$isIOS)()) {\n    if ($14c0b72509d70225$var$state === 'default') {\n      const documentObject = (0, $7R18e$getOwnerDocument)(target);\n      $14c0b72509d70225$var$savedUserSelect = documentObject.documentElement.style.webkitUserSelect;\n      documentObject.documentElement.style.webkitUserSelect = 'none';\n    }\n    $14c0b72509d70225$var$state = 'disabled';\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, store the target's original user-select and change to user-select: none\n    // Ignore state since it doesn't apply for non iOS\n    let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n    $14c0b72509d70225$var$modifiedElementMap.set(target, target.style[property]);\n    target.style[property] = 'none';\n  }\n}\nfunction $14c0b72509d70225$export$b0d6fa1ab32e3295(target) {\n  if ((0, $7R18e$isIOS)()) {\n    // If the state is already default, there's nothing to do.\n    // If it is restoring, then there's no need to queue a second restore.\n    if ($14c0b72509d70225$var$state !== 'disabled') return;\n    $14c0b72509d70225$var$state = 'restoring';\n    // There appears to be a delay on iOS where selection still might occur\n    // after pointer up, so wait a bit before removing user-select.\n    setTimeout(() => {\n      // Wait for any CSS transitions to complete so we don't recompute style\n      // for the whole page in the middle of the animation and cause jank.\n      (0, $7R18e$runAfterTransition)(() => {\n        // Avoid race conditions\n        if ($14c0b72509d70225$var$state === 'restoring') {\n          const documentObject = (0, $7R18e$getOwnerDocument)(target);\n          if (documentObject.documentElement.style.webkitUserSelect === 'none') documentObject.documentElement.style.webkitUserSelect = $14c0b72509d70225$var$savedUserSelect || '';\n          $14c0b72509d70225$var$savedUserSelect = '';\n          $14c0b72509d70225$var$state = 'default';\n        }\n      });\n    }, 300);\n  } else if (target instanceof HTMLElement || target instanceof SVGElement)\n    // If not iOS, restore the target's original user-select if any\n    // Ignore state since it doesn't apply for non iOS\n    {\n      if (target && $14c0b72509d70225$var$modifiedElementMap.has(target)) {\n        let targetOldUserSelect = $14c0b72509d70225$var$modifiedElementMap.get(target);\n        let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n        if (target.style[property] === 'none') target.style[property] = targetOldUserSelect;\n        if (target.getAttribute('style') === '') target.removeAttribute('style');\n        $14c0b72509d70225$var$modifiedElementMap.delete(target);\n      }\n    }\n}\nexport { $14c0b72509d70225$export$16a4697467175487 as disableTextSelection, $14c0b72509d70225$export$b0d6fa1ab32e3295 as restoreTextSelection };", "map": {"version": 3, "names": ["$14c0b72509d70225$var$state", "$14c0b72509d70225$var$savedUserSelect", "$14c0b72509d70225$var$modifiedElementMap", "WeakMap", "$14c0b72509d70225$export$16a4697467175487", "target", "$7R18e$isIOS", "documentObject", "$7R18e$getOwnerDocument", "documentElement", "style", "webkitUserSelect", "HTMLElement", "SVGElement", "property", "set", "$14c0b72509d70225$export$b0d6fa1ab32e3295", "setTimeout", "$7R18e$runAfterTransition", "has", "targetOldUserSelect", "get", "getAttribute", "removeAttribute", "delete"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\textSelection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerDocument, isIOS, runAfterTransition} from '@react-aria/utils';\n\n// Safari on iOS starts selecting text on long press. The only way to avoid this, it seems,\n// is to add user-select: none to the entire page. Adding it to the pressable element prevents\n// that element from being selected, but nearby elements may still receive selection. We add\n// user-select: none on touch start, and remove it again on touch end to prevent this.\n// This must be implemented using global state to avoid race conditions between multiple elements.\n\n// There are three possible states due to the delay before removing user-select: none after\n// pointer up. The 'default' state always transitions to the 'disabled' state, which transitions\n// to 'restoring'. The 'restoring' state can either transition back to 'disabled' or 'default'.\n\n// For non-iOS devices, we apply user-select: none to the pressed element instead to avoid possible\n// performance issues that arise from applying and removing user-select: none to the entire page\n// (see https://github.com/adobe/react-spectrum/issues/1609).\ntype State = 'default' | 'disabled' | 'restoring';\n\n// Note that state only matters here for iOS. Non-iOS gets user-select: none applied to the target element\n// rather than at the document level so we just need to apply/remove user-select: none for each pressed element individually\nlet state: State = 'default';\nlet savedUserSelect = '';\nlet modifiedElementMap = new WeakMap<Element, string>();\n\nexport function disableTextSelection(target?: Element): void {\n  if (isIOS()) {\n    if (state === 'default') {\n\n      const documentObject = getOwnerDocument(target);\n      savedUserSelect = documentObject.documentElement.style.webkitUserSelect;\n      documentObject.documentElement.style.webkitUserSelect = 'none';\n    }\n\n    state = 'disabled';\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, store the target's original user-select and change to user-select: none\n    // Ignore state since it doesn't apply for non iOS\n    let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n    modifiedElementMap.set(target, target.style[property]);\n    target.style[property] = 'none';\n  }\n}\n\nexport function restoreTextSelection(target?: Element): void {\n  if (isIOS()) {\n    // If the state is already default, there's nothing to do.\n    // If it is restoring, then there's no need to queue a second restore.\n    if (state !== 'disabled') {\n      return;\n    }\n\n    state = 'restoring';\n\n    // There appears to be a delay on iOS where selection still might occur\n    // after pointer up, so wait a bit before removing user-select.\n    setTimeout(() => {\n      // Wait for any CSS transitions to complete so we don't recompute style\n      // for the whole page in the middle of the animation and cause jank.\n      runAfterTransition(() => {\n        // Avoid race conditions\n        if (state === 'restoring') {\n\n          const documentObject = getOwnerDocument(target);\n          if (documentObject.documentElement.style.webkitUserSelect === 'none') {\n            documentObject.documentElement.style.webkitUserSelect = savedUserSelect || '';\n          }\n\n          savedUserSelect = '';\n          state = 'default';\n        }\n      });\n    }, 300);\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, restore the target's original user-select if any\n    // Ignore state since it doesn't apply for non iOS\n    if (target && modifiedElementMap.has(target)) {\n      let targetOldUserSelect = modifiedElementMap.get(target) as string;\n      let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n\n      if (target.style[property] === 'none') {\n        target.style[property] = targetOldUserSelect;\n      }\n\n      if (target.getAttribute('style') === '') {\n        target.removeAttribute('style');\n      }\n      modifiedElementMap.delete(target);\n    }\n  }\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AA6BA;AACA;AACA,IAAIA,2BAAA,GAAe;AACnB,IAAIC,qCAAA,GAAkB;AACtB,IAAIC,wCAAA,GAAqB,IAAIC,OAAA;AAEtB,SAASC,0CAAqBC,MAAgB;EACnD,IAAI,IAAAC,YAAI,KAAK;IACX,IAAIN,2BAAA,KAAU,WAAW;MAEvB,MAAMO,cAAA,GAAiB,IAAAC,uBAAe,EAAEH,MAAA;MACxCJ,qCAAA,GAAkBM,cAAA,CAAeE,eAAe,CAACC,KAAK,CAACC,gBAAgB;MACvEJ,cAAA,CAAeE,eAAe,CAACC,KAAK,CAACC,gBAAgB,GAAG;IAC1D;IAEAX,2BAAA,GAAQ;EACV,OAAO,IAAIK,MAAA,YAAkBO,WAAA,IAAeP,MAAA,YAAkBQ,UAAA,EAAY;IACxE;IACA;IACA,IAAIC,QAAA,GAAW,gBAAgBT,MAAA,CAAOK,KAAK,GAAG,eAAe;IAC7DR,wCAAA,CAAmBa,GAAG,CAACV,MAAA,EAAQA,MAAA,CAAOK,KAAK,CAACI,QAAA,CAAS;IACrDT,MAAA,CAAOK,KAAK,CAACI,QAAA,CAAS,GAAG;EAC3B;AACF;AAEO,SAASE,0CAAqBX,MAAgB;EACnD,IAAI,IAAAC,YAAI,KAAK;IACX;IACA;IACA,IAAIN,2BAAA,KAAU,YACZ;IAGFA,2BAAA,GAAQ;IAER;IACA;IACAiB,UAAA,CAAW;MACT;MACA;MACA,IAAAC,yBAAiB,EAAE;QACjB;QACA,IAAIlB,2BAAA,KAAU,aAAa;UAEzB,MAAMO,cAAA,GAAiB,IAAAC,uBAAe,EAAEH,MAAA;UACxC,IAAIE,cAAA,CAAeE,eAAe,CAACC,KAAK,CAACC,gBAAgB,KAAK,QAC5DJ,cAAA,CAAeE,eAAe,CAACC,KAAK,CAACC,gBAAgB,GAAGV,qCAAA,IAAmB;UAG7EA,qCAAA,GAAkB;UAClBD,2BAAA,GAAQ;QACV;MACF;IACF,GAAG;EACL,OAAO,IAAIK,MAAA,YAAkBO,WAAA,IAAeP,MAAA,YAAkBQ,UAAA;IAC5D;IACA;IACA;MAAA,IAAIR,MAAA,IAAUH,wCAAA,CAAmBiB,GAAG,CAACd,MAAA,GAAS;QAC5C,IAAIe,mBAAA,GAAsBlB,wCAAA,CAAmBmB,GAAG,CAAChB,MAAA;QACjD,IAAIS,QAAA,GAAW,gBAAgBT,MAAA,CAAOK,KAAK,GAAG,eAAe;QAE7D,IAAIL,MAAA,CAAOK,KAAK,CAACI,QAAA,CAAS,KAAK,QAC7BT,MAAA,CAAOK,KAAK,CAACI,QAAA,CAAS,GAAGM,mBAAA;QAG3B,IAAIf,MAAA,CAAOiB,YAAY,CAAC,aAAa,IACnCjB,MAAA,CAAOkB,eAAe,CAAC;QAEzBrB,wCAAA,CAAmBsB,MAAM,CAACnB,MAAA;MAC5B;IAAA;AAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}