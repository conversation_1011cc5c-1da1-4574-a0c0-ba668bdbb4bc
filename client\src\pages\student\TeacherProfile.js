import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Avatar,
  Divider,
  Button,
  Chip,
  Rating,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  IconButton,
  Tooltip,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  IconButton as MuiIconButton,
  TextField
} from '@mui/material';
import {
  Language as LanguageIcon,
  School as SchoolIcon,
  AttachMoney as MoneyIcon,
  AccessTime as AccessTimeIcon,

  PlayCircleOutline as PlayCircleOutlineIcon,
  ArrowBack as ArrowBackIcon,
  Message as MessageIcon,
  CalendarMonth as CalendarMonthIcon,
  Verified as VerifiedIcon,
  Close as CloseIcon,
  Send as SendIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import Layout from '../../components/Layout';
import AvailableHoursTable from '../../components/AvailableHoursTable';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';

const TeacherProfile = () => {
  const { id } = useParams();
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser, token } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isRtl = i18n.language === 'ar';

  const [teacher, setTeacher] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);


  const [availableSlots, setAvailableSlots] = useState([]);
  const [loadingSlots, setLoadingSlots] = useState(false);

  useEffect(() => {
    const fetchTeacherDetails = async () => {
      try {
        setLoading(true);
        const { data } = await axios.get(`/teachers/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (data.success) {
          setTeacher(data.data);
        }
      } catch (error) {
        console.error('Error fetching teacher details:', error);
        setError(t('teacherDetails.errorFetching'));
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      fetchTeacherDetails();
    }
  }, [id, t, token]);

  // Fetch available slots
  useEffect(() => {
    const fetchAvailableSlots = async () => {
      if (!id || !token) return;

      try {
        setLoadingSlots(true);

        // Build URL with student timezone if available
        let url = `/teachers/${id}/available-slots`;
        if (studentProfile?.timezone) {
          url += `?student_timezone=${encodeURIComponent(studentProfile.timezone)}`;
        }

        const { data } = await axios.get(url, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (data.success) {
          setAvailableSlots(data.data);
        } else {
          console.error('Error in API response:', data.message);
          setAvailableSlots([]);
        }
      } catch (error) {
        console.error('Error fetching available slots:', error);
        setAvailableSlots([]);
      } finally {
        setLoadingSlots(false);
      }
    };

    fetchAvailableSlots();
  }, [id, token, t, studentProfile?.timezone]);

  const [showChat, setShowChat] = useState(false);
  const [chatId, setChatId] = useState(null);
  const [chatLoading, setChatLoading] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const { socket } = useSocket();

  const handleStartChat = async () => {
    if (!currentUser || !token) {
      navigate('/login', { state: { from: `/student/teacher/${id}` } });
      return;
    }

    try {
      setChatLoading(true);
      const { data } = await axios.get(`/chat/conversation/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setChatId(data.data.id);
        setShowChat(true);
      } else {
        console.error('Failed to start chat:', data.message);
      }
    } catch (error) {
      console.error('Error starting chat:', error);
    } finally {
      setChatLoading(false);
    }
  };

  const handleCloseChat = () => {
    setShowChat(false);
    setChatMessages([]);
    setMessageInput('');
  };

  const handleSendClick = () => {
    if (messageInput.trim()) {
      handleSendMessage(messageInput);
      setMessageInput('');
    }
  };

  const handleSendMessage = useCallback((content) => {
    if (!socket || !chatId || !content.trim() || !currentUser) return;

    // Create a temporary message with a unique ID
    const tempId = `temp-${Date.now()}`;

    // Get current timestamp in seconds
    const timestamp = Math.floor(Date.now() / 1000);

    const tempMessage = {
      id: tempId,
      conversation_id: chatId,
      sender_id: currentUser.id,
      content,
      created_at: timestamp,
      is_read: false,
      is_temp: true
    };

    // Add to local messages immediately
    setChatMessages(prev => [...prev, tempMessage]);

    // Send to server
    socket.emit('send_message', {
      chatId: chatId,
      content,
      tempId,
      timestamp
    });
  }, [socket, chatId, id, currentUser]);

  // Listen for new messages
  useEffect(() => {
    if (!socket || !chatId) return;

    // Listen for new messages
    socket.on('new_message', (message) => {
      // Convert MySQL timestamp to Unix timestamp
      if (message.created_at && typeof message.created_at === 'string') {
        message.created_at = Math.floor(new Date(message.created_at.replace(' ', 'T')).getTime() / 1000);
      }

      // Add message to messages list if it's for the current chat
      if (message.conversation_id === chatId) {
        setChatMessages(prev => [...prev, message]);
        // Mark message as read
        socket.emit('mark_messages_read', { chatId: chatId });
      }
    });

    // Listen for message sent confirmation
    socket.on('message_sent', ({ success, message, tempId }) => {
      if (success) {
        // Convert MySQL timestamp to Unix timestamp
        if (message.created_at && typeof message.created_at === 'string') {
          message.created_at = Math.floor(new Date(message.created_at.replace(' ', 'T')).getTime() / 1000);
        }

        setChatMessages(prev => prev.map(msg =>
          msg.id === tempId ? { ...message } : msg
        ));
      } else {
        // Remove temporary message if sending failed
        setChatMessages(prev => prev.filter(msg => msg.id !== tempId));
      }
    });

    return () => {
      socket.off('new_message');
      socket.off('message_sent');
    };
  }, [socket, chatId]);

  // Fetch chat messages when chat is opened
  useEffect(() => {
    if (!socket || !chatId || !showChat) return;

    setChatLoading(true);
    socket.emit('get_chat_messages', { chatId: chatId }, (response) => {
      if (response.success) {
        // Convert all MySQL timestamps to Unix timestamps
        const convertedMessages = response.messages.map(msg => {
          if (msg.created_at && typeof msg.created_at === 'string') {
            return {
              ...msg,
              created_at: Math.floor(new Date(msg.created_at.replace(' ', 'T')).getTime() / 1000)
            };
          }
          return msg;
        });
        setChatMessages(convertedMessages);

        // Mark messages as read
        socket.emit('mark_messages_read', { chatId: chatId });
      } else {
        console.error('Error fetching messages:', response.error);
      }
      setChatLoading(false);
    });
  }, [socket, chatId, showChat]);

  const handleBookLesson = () => {
    if (!currentUser || !token) {
      navigate('/login', { state: { from: `/student/teacher/${id}` } });
      return;
    }

    // Navigate to booking page
    navigate(`/student/book/${id}`);
  };

  const handleBookSlot = async (slot, date) => {
    if (!currentUser || !token) {
      navigate('/login', { state: { from: `/student/teacher/${id}` } });
      return;
    }

    try {
      const { data } = await axios.post('/bookings', {
        teacher_id: id,
        slot_id: slot.id,
        date: date
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        // Show success message
        alert(t('booking.bookingSuccess'));

        // Refresh available slots
        fetchAvailableSlots();
      } else {
        alert(data.message || t('booking.bookingFailed'));
      }
    } catch (error) {
      console.error('Error booking slot:', error);
      alert(error.response?.data?.message || t('booking.bookingFailed'));
    }
  };

  const fetchAvailableSlots = async () => {
    if (!id || !token) return;

    try {
      setLoadingSlots(true);

      // Build URL with student timezone if available
      let url = `/teachers/${id}/available-slots`;
      if (studentProfile?.timezone) {
        url += `?student_timezone=${encodeURIComponent(studentProfile.timezone)}`;
      }

      const { data } = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setAvailableSlots(data.data);
      } else {
        console.error('Error in API response:', data.message);
        setAvailableSlots([]);
      }
    } catch (error) {
      console.error('Error fetching available slots:', error);
      setAvailableSlots([]);
    } finally {
      setLoadingSlots(false);
    }
  };





  // Function to check if a URL is a local video file
  const isLocalVideoFile = (url) => {
    if (!url) return false;
    return url.startsWith('/uploads/') && (
      url.endsWith('.mp4') ||
      url.endsWith('.webm') ||
      url.endsWith('.ogg')
    );
  };

  // Function to check if a URL is a YouTube video
  const isYoutubeVideo = (url) => {
    if (!url) return false;
    return url.includes('youtube.com') || url.includes('youtu.be');
  };

  // Function to extract YouTube video ID from URL
  const getYoutubeVideoId = (url) => {
    if (!url) return null;

    try {
      // Regular expressions to match different YouTube URL formats
      const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
      const match = url.match(regExp);

      if (match && match[2] && match[2].length === 11) {
        return match[2];
      }

      // If the URL is already a video ID (11 characters)
      if (url.length === 11 && /^[a-zA-Z0-9_-]{11}$/.test(url)) {
        return url;
      }

      console.log('Could not extract YouTube video ID from:', url);
      return null;
    } catch (error) {
      console.error('Error extracting YouTube video ID:', error);
      return null;
    }
  };

  const content = (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
          <CircularProgress />
        </Box>
      ) : error || !teacher ? (
        <Box>
          <Alert severity="error">{error || t('teacherDetails.teacherNotFound')}</Alert>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              startIcon={<ArrowBackIcon />}
              component={Link}
              to="/student/find-teacher"
            >
              {t('teacherDetails.backToSearch')}
            </Button>
          </Box>
        </Box>
      ) : (
        <>
          <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
            {/* Back Button */}
            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-start' }}>
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                component={Link}
                to="/student/find-teacher"
              >
                {t('teacherDetails.backToSearch')}
              </Button>
            </Box>

          {/* Teacher Header */}
          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Avatar
                  src={teacher.profile_picture_url ? (
                    teacher.profile_picture_url.startsWith('http')
                      ? teacher.profile_picture_url
                      : `https://allemnionline.com${teacher.profile_picture_url}`
                  ) : ''}
                  alt={teacher.full_name}
                  sx={{
                    width: { xs: 120, md: 150 },
                    height: { xs: 120, md: 150 },
                    mb: 2,
                    border: '4px solid',
                    borderColor: 'primary.main'
                  }}
                />
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h5" component="h1" sx={{ fontWeight: 'bold', mr: 1 }}>
                    {teacher.full_name}
                  </Typography>
                  {teacher.is_verified && (
                    <Tooltip title={t('search.verifiedTeacher')}>
                      <VerifiedIcon color="primary" />
                    </Tooltip>
                  )}
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Rating value={teacher.average_rating} precision={0.5} readOnly />
                  <Typography variant="body2" sx={{ ml: 1 }}>
                    ({teacher.review_count} {t('teacherDetails.reviews')})
                  </Typography>
                </Box>
                <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1 }}>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<MessageIcon />}
                    onClick={handleStartChat}
                    fullWidth
                  >
                    {t('chat.startChat')}
                  </Button>
                  <Button
                    variant="contained"
                    color="secondary"
                    startIcon={<CalendarMonthIcon />}
                    onClick={handleBookLesson}
                    fullWidth
                  >
                    {t('teacherDetails.bookLesson')}
                  </Button>
                </Box>
              </Grid>

              <Grid item xs={12} md={9}>
                <Grid container spacing={3}>
                  {/* Subjects */}
                  <Grid item xs={12}>
                    <Box>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <SchoolIcon color="primary" />
                        {t('teacherDetails.teachingSubjects')}
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 4 }}>
                        {teacher.subjects.map((subject, index) => (
                          <Chip
                            key={index}
                            label={subject}
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    </Box>
                  </Grid>



                  {/* Languages */}
                  <Grid item xs={12} md={6}>
                    <Box>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <LanguageIcon color="primary" />
                        {t('teacherDetails.languages')}
                      </Typography>
                      <Box sx={{ ml: 4 }}>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                          <strong>{t('teacherDetails.nativeLanguage')}:</strong> {teacher.native_language}
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                          <strong>{t('teacherDetails.teachingLanguages')}:</strong>
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 2 }}>
                          {teacher.teaching_languages.map((language, index) => (
                            <Chip
                              key={index}
                              label={language}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>
                    </Box>
                  </Grid>

                  {/* Qualifications and Experience */}
                  <Grid item xs={12} md={6}>
                    <Box>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <AccessTimeIcon color="primary" />
                        {t('teacherDetails.experience')}
                      </Typography>
                      <Box sx={{ ml: 4 }}>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                          {t('teacherDetails.yearsOfExperience', { years: teacher.teaching_experience })}
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                          <strong>{t('teacherDetails.qualifications')}:</strong>
                        </Typography>
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {teacher.qualifications}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>

                  {/* Price */}
                  <Grid item xs={12} md={6}>
                    <Box>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <MoneyIcon color="primary" />
                        {t('teacherDetails.paymentInfo')}
                      </Typography>
                      <Box sx={{ ml: 4 }}>
                        <Typography variant="body1" sx={{ mb: 0.5, fontWeight: 'bold', fontSize: '1.2rem', color: 'primary.main' }}>
                          ${teacher.price_per_lesson}/hr
                        </Typography>
                        {teacher.trial_lesson_price && (
                          <Typography variant="body2" color="text.secondary" sx={{ ml: 0.2 }}>
                            Trial: ${teacher.trial_lesson_price}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Paper>

          {/* Introduction Video Section */}
          {teacher.intro_video_url && (
            <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
              <Typography variant="h5" gutterBottom sx={{ borderBottom: 1, borderColor: 'divider', pb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <PlayCircleOutlineIcon color="primary" />
                {t('teacherDetails.introVideo')}
              </Typography>
              <Box sx={{ mt: 2, position: 'relative', paddingTop: '56.25%', width: '100%' }}>
                {isYoutubeVideo(teacher.intro_video_url) ? (
                  // YouTube video embed
                  <iframe
                    src={`https://www.youtube.com/embed/${getYoutubeVideoId(teacher.intro_video_url)}`}
                    title="YouTube video player"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      borderRadius: '8px'
                    }}
                  />
                ) : isLocalVideoFile(teacher.intro_video_url) ? (
                  // Local video file
                  <video
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      borderRadius: '8px'
                    }}
                    src={`https://allemnionline.com${teacher.intro_video_url}`}
                    controls
                    preload="metadata"
                  />
                ) : (
                  // External video URL (not YouTube)
                  <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', textAlign: 'center' }}>
                    <Typography variant="body1">
                      <a href={teacher.intro_video_url} target="_blank" rel="noopener noreferrer">
                        {t('teacherDetails.openVideo')}
                      </a>
                    </Typography>
                  </Box>
                )}
              </Box>
            </Paper>
          )}

          {/* Available Hours Section */}
          {teacher.available_hours && (() => {
            try {
              const parsedHours = typeof teacher.available_hours === 'string'
                ? JSON.parse(teacher.available_hours)
                : teacher.available_hours;
              return (
                <AvailableHoursTable
                  availableHours={parsedHours}
                  loading={loading}
                  showStats={true}
                />
              );
            } catch (error) {
              console.error('Error parsing available hours:', error);
              return null;
            }
          })()}



          {/* Reviews Section */}
          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
            <Typography variant="h5" gutterBottom sx={{ borderBottom: 1, borderColor: 'divider', pb: 1 }}>
              {t('teacherDetails.studentReviews')}
            </Typography>
            {teacher.reviews && teacher.reviews.length > 0 ? (
              <List>
                {teacher.reviews.map((review) => (
                  <ListItem key={review.id} alignItems="flex-start" sx={{ borderBottom: '1px solid', borderColor: 'divider', py: 2 }}>
                    <ListItemAvatar>
                      <Avatar src={review.student_profile_picture} alt={review.student_name}>
                        {review.student_name.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="subtitle1" component="span">
                            {review.student_name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {format(new Date(review.created_at), 'PPP', { locale: isRtl ? ar : enUS })}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Rating value={review.rating} readOnly size="small" sx={{ mb: 1 }} />
                          <Typography variant="body2" component="p" sx={{ mb: 2 }}>
                            {review.comment}
                          </Typography>

                          {/* Teacher Reply */}
                          {review.reply_text && (
                            <Box sx={{
                              bgcolor: 'primary.light',
                              p: 2,
                              borderRadius: 1,
                              mt: 2,
                              border: '1px solid',
                              borderColor: 'primary.main'
                            }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 'bold' }}>
                                  {t('reviews.teacherReply')}:
                                </Typography>
                              </Box>
                              <Typography variant="body2" sx={{ color: 'primary.dark' }}>
                                {review.reply_text}
                              </Typography>
                              {review.reply_created_at && (
                                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                                  {format(new Date(review.reply_created_at), 'PPP', { locale: isRtl ? ar : enUS })}
                                </Typography>
                              )}
                            </Box>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box sx={{ py: 4, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  {t('teacherDetails.noReviews')}
                </Typography>
              </Box>
            )}
          </Paper>



          {/* Chat Dialog */}
          <Dialog
            open={showChat}
            onClose={handleCloseChat}
            maxWidth="md"
            fullWidth
            PaperProps={{
              sx: {
                height: '80vh',
                maxHeight: '700px',
                display: 'flex',
                flexDirection: 'column'
              }
            }}
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar
                    src={teacher.profile_picture_url ? (
                      teacher.profile_picture_url.startsWith('http')
                        ? teacher.profile_picture_url
                        : `https://allemnionline.com${teacher.profile_picture_url}`
                    ) : ''}
                    alt={teacher.full_name}
                    sx={{ mr: 1 }}
                  >
                    {teacher.full_name.charAt(0)}
                  </Avatar>
                  <Typography variant="h6">{teacher.full_name}</Typography>
                </Box>
                <IconButton onClick={handleCloseChat} size="small">
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ p: 0, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
              {chatLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
                    {chatMessages.length === 0 ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography color="text.secondary">{t('chat.startConversation')}</Typography>
                      </Box>
                    ) : (
                      chatMessages.map((message) => (
                        <Box
                          key={message.id}
                          sx={{
                            display: 'flex',
                            justifyContent: message.sender_id === currentUser?.id ? 'flex-end' : 'flex-start',
                            mb: 2
                          }}
                        >
                          {message.sender_id !== currentUser?.id && (
                            <Avatar
                              src={teacher.profile_picture_url ? (
                                teacher.profile_picture_url.startsWith('http')
                                  ? teacher.profile_picture_url
                                  : `https://allemnionline.com${teacher.profile_picture_url}`
                              ) : ''}
                              alt={teacher.full_name}
                              sx={{ mr: 1, width: 32, height: 32 }}
                            >
                              {teacher.full_name.charAt(0)}
                            </Avatar>
                          )}
                          <Box
                            sx={{
                              maxWidth: '70%',
                              p: 2,
                              bgcolor: message.sender_id === currentUser?.id ? 'primary.main' : 'grey.100',
                              color: message.sender_id === currentUser?.id ? 'white' : 'text.primary',
                              borderRadius: 2,
                              position: 'relative'
                            }}
                          >
                            <Typography variant="body1">{message.content}</Typography>
                            <Typography
                              variant="caption"
                              sx={{
                                display: 'block',
                                textAlign: message.sender_id === currentUser?.id ? 'right' : 'left',
                                mt: 0.5,
                                color: message.sender_id === currentUser?.id ? 'rgba(255,255,255,0.7)' : 'text.secondary'
                              }}
                            >
                              {new Date(message.created_at * 1000).toLocaleTimeString()}
                            </Typography>
                          </Box>
                        </Box>
                      ))
                    )}
                  </Box>
                  <Divider />
                  <Box sx={{ p: 2, backgroundColor: 'background.default' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <TextField
                        fullWidth
                        placeholder={t('chat.typeMessage')}
                        variant="outlined"
                        size="small"
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendClick();
                          }
                        }}
                        sx={{ mr: 1 }}
                      />
                      <IconButton
                        color="primary"
                        onClick={handleSendClick}
                        disabled={!messageInput.trim()}
                      >
                        <SendIcon />
                      </IconButton>
                    </Box>
                  </Box>
                </Box>
              )}
            </DialogContent>
          </Dialog>
          </ProfileCompletionAlert>
        </>
      )}
    </Container>
  );

  return <Layout>{content}</Layout>;
};

export default TeacherProfile;
