{"ast": null, "code": "/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */function $cc38e7bd3fc7b213$export$2bb74740c4e19def(node, checkForOverflow) {\n  if (!node) return false;\n  let style = window.getComputedStyle(node);\n  let isScrollable = /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n  if (isScrollable && checkForOverflow) isScrollable = node.scrollHeight !== node.clientHeight || node.scrollWidth !== node.clientWidth;\n  return isScrollable;\n}\nexport { $cc38e7bd3fc7b213$export$2bb74740c4e19def as isScrollable };", "map": {"version": 3, "names": ["$cc38e7bd3fc7b213$export$2bb74740c4e19def", "node", "checkForOverflow", "style", "window", "getComputedStyle", "isScrollable", "test", "overflow", "overflowX", "overflowY", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\isScrollable.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport function isScrollable(node: Element | null, checkForOverflow?: boolean): boolean {\n  if (!node) {\n    return false;\n  }\n  let style = window.getComputedStyle(node);\n  let isScrollable = /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n\n  if (isScrollable && checkForOverflow) {\n    isScrollable = node.scrollHeight !== node.clientHeight || node.scrollWidth !== node.clientWidth;\n  }\n\n  return isScrollable;\n}\n"], "mappings": "AAAA;;;;;;;;;;GAYO,SAASA,0CAAaC,IAAoB,EAAEC,gBAA0B;EAC3E,IAAI,CAACD,IAAA,EACH,OAAO;EAET,IAAIE,KAAA,GAAQC,MAAA,CAAOC,gBAAgB,CAACJ,IAAA;EACpC,IAAIK,YAAA,GAAe,gBAAgBC,IAAI,CAACJ,KAAA,CAAMK,QAAQ,GAAGL,KAAA,CAAMM,SAAS,GAAGN,KAAA,CAAMO,SAAS;EAE1F,IAAIJ,YAAA,IAAgBJ,gBAAA,EAClBI,YAAA,GAAeL,IAAA,CAAKU,YAAY,KAAKV,IAAA,CAAKW,YAAY,IAAIX,IAAA,CAAKY,WAAW,KAAKZ,IAAA,CAAKa,WAAW;EAGjG,OAAOR,YAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}