{"ast": null, "code": "class a extends Map {\n  constructor(t) {\n    super();\n    this.factory = t;\n  }\n  get(t) {\n    let e = super.get(t);\n    return e === void 0 && (e = this.factory(t), this.set(t, e)), e;\n  }\n}\nexport { a as DefaultMap };", "map": {"version": 3, "names": ["a", "Map", "constructor", "t", "factory", "get", "e", "set", "DefaultMap"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/utils/default-map.js"], "sourcesContent": ["class a extends Map{constructor(t){super();this.factory=t}get(t){let e=super.get(t);return e===void 0&&(e=this.factory(t),this.set(t,e)),e}}export{a as DefaultMap};\n"], "mappings": "AAAA,MAAMA,CAAC,SAASC,GAAG;EAACC,WAAWA,CAACC,CAAC,EAAC;IAAC,KAAK,CAAC,CAAC;IAAC,IAAI,CAACC,OAAO,GAACD,CAAC;EAAA;EAACE,GAAGA,CAACF,CAAC,EAAC;IAAC,IAAIG,CAAC,GAAC,KAAK,CAACD,GAAG,CAACF,CAAC,CAAC;IAAC,OAAOG,CAAC,KAAG,KAAK,CAAC,KAAGA,CAAC,GAAC,IAAI,CAACF,OAAO,CAACD,CAAC,CAAC,EAAC,IAAI,CAACI,GAAG,CAACJ,CAAC,EAACG,CAAC,CAAC,CAAC,EAACA,CAAC;EAAA;AAAC;AAAC,SAAON,CAAC,IAAIQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}