{"ast": null, "code": "import t, { createContext as r, useContext as c } from \"react\";\nlet e = r(!1);\nfunction a() {\n  return c(e);\n}\nfunction l(o) {\n  return t.createElement(e.Provider, {\n    value: o.force\n  }, o.children);\n}\nexport { l as ForcePortalRoot, a as usePortalRoot };", "map": {"version": 3, "names": ["t", "createContext", "r", "useContext", "c", "e", "a", "l", "o", "createElement", "Provider", "value", "force", "children", "ForcePortalRoot", "usePortalRoot"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/internal/portal-force-root.js"], "sourcesContent": ["import t,{createContext as r,useContext as c}from\"react\";let e=r(!1);function a(){return c(e)}function l(o){return t.createElement(e.Provider,{value:o.force},o.children)}export{l as ForcePortalRoot,a as usePortalRoot};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,IAAIC,CAAC,GAACH,CAAC,CAAC,CAAC,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAOF,CAAC,CAACC,CAAC,CAAC;AAAA;AAAC,SAASE,CAACA,CAACC,CAAC,EAAC;EAAC,OAAOR,CAAC,CAACS,aAAa,CAACJ,CAAC,CAACK,QAAQ,EAAC;IAACC,KAAK,EAACH,CAAC,CAACI;EAAK,CAAC,EAACJ,CAAC,CAACK,QAAQ,CAAC;AAAA;AAAC,SAAON,CAAC,IAAIO,eAAe,EAACR,CAAC,IAAIS,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}