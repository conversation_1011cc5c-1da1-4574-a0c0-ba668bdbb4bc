{"ast": null, "code": "var _jsxFileName = \"D:\\\\xampp\\\\htdocs\\\\allemnionline\\\\client\\\\src\\\\components\\\\WeeklyBookingsTable.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Box, Typography, Paper, Button, Chip, useTheme, alpha, CircularProgress, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, IconButton } from '@mui/material';\nimport { Visibility as ViewIcon, Cancel as CancelIcon, AccessTime as AccessTimeIcon, RadioButtonUnchecked as RadioButtonUncheckedIcon, CheckCircle as CheckCircleIcon, SelfImprovement as RestIcon } from '@mui/icons-material';\nimport { format, addDays } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport moment from 'moment-timezone';\nimport { formatDateInStudentTimezone } from '../utils/timezone';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WeeklyBookingsTable = ({\n  bookings,\n  loading = false,\n  currentWeekStart,\n  daysOfWeek,\n  onViewDetails,\n  onCancelBooking,\n  studentProfile,\n  formatBookingTime,\n  getStatusColor,\n  isTeacherView = false,\n  availableHours = null,\n  onTakeBreak = null,\n  weeklyBreaks = []\n}) => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  // State for break dialog\n  const [breakDialogOpen, setBreakDialogOpen] = React.useState(false);\n  const [selectedBreakSlot, setSelectedBreakSlot] = React.useState(null);\n\n  // Handle take break\n  const handleTakeBreak = (day, timeSlot, isFirstHalf) => {\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const slotMinute = isFirstHalf ? 0 : 30;\n    setSelectedBreakSlot({\n      day,\n      date,\n      hour: timeSlot.hour,\n      minute: slotMinute,\n      isFirstHalf,\n      timeSlot\n    });\n    setBreakDialogOpen(true);\n  };\n  const confirmTakeBreak = () => {\n    if (selectedBreakSlot && onTakeBreak) {\n      onTakeBreak(selectedBreakSlot);\n    }\n    setBreakDialogOpen(false);\n    setSelectedBreakSlot(null);\n  };\n\n  // Check if a half-hour slot is in the past (considering teacher's timezone)\n  const isHalfHourSlotInPast = (day, timeSlot, isFirstHalf) => {\n    try {\n      const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n      const date = addDays(currentWeekStart, dayIndex);\n      const slotMinute = isFirstHalf ? 0 : 30;\n\n      // Create the slot start datetime\n      const slotStartTime = new Date(date);\n      slotStartTime.setHours(timeSlot.hour, slotMinute, 0, 0);\n\n      // Get current time in teacher's timezone using the same method as the table\n      let currentTime;\n      if (studentProfile && studentProfile.timezone) {\n        // Use teacher's timezone\n        const currentTimeStr = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n        currentTime = new Date(currentTimeStr);\n      } else {\n        // Fallback to browser local time\n        currentTime = new Date();\n      }\n\n      // A slot is considered \"past\" if its start time has already passed\n      // This means the current half-hour slot is also considered past\n      return slotStartTime <= currentTime;\n    } catch (error) {\n      console.error('Error checking if slot is in past:', error);\n      return false;\n    }\n  };\n\n  // Check if a specific half-hour slot is available\n  const isHalfHourSlotAvailable = (day, timeSlot, isFirstHalf) => {\n    if (!availableHours || !isTeacherView) return false;\n    const dayKey = day.toLowerCase();\n    const daySlots = availableHours[dayKey];\n    if (!daySlots || !Array.isArray(daySlots)) return false;\n\n    // Check if this slot is taken as a break\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const slotMinute = isFirstHalf ? 0 : 30;\n\n    // Check if this slot matches any break from the backend\n    // Backend now sends UTC datetimes, so we need to convert them to teacher's timezone\n    // and check if they match this slot\n\n    // Create the current slot datetime in teacher's timezone\n    const slotDateTime = new Date(date);\n    slotDateTime.setHours(timeSlot.hour, slotMinute, 0, 0);\n    console.log('🔍 FRONTEND CHECK: Checking if slot is break');\n    console.log('  📅 Slot Date:', date.toISOString().split('T')[0]);\n    console.log('  ⏰ Slot Hour:', timeSlot.hour, 'Minute:', slotMinute);\n    console.log('  📅 Slot DateTime:', slotDateTime.toISOString());\n\n    // Check if any break matches this slot\n    const isBreakSlot = weeklyBreaks.some(breakData => {\n      if (typeof breakData === 'string') {\n        // Old format - ignore for now\n        return false;\n      }\n      if (breakData && breakData.datetime) {\n        // New format - UTC datetime from backend\n        const breakUtcDateTime = new Date(breakData.datetime);\n\n        // Convert break UTC time to teacher's timezone\n        if (studentProfile && studentProfile.timezone) {\n          const teacherTimezone = studentProfile.timezone;\n          const breakInTeacherTz = formatDateInStudentTimezone(breakData.datetime, teacherTimezone, 'YYYY-MM-DD HH:mm:ss');\n          const [breakDate, breakTime] = breakInTeacherTz.split(' ');\n          const [breakHour, breakMinute] = breakTime.split(':').map(Number);\n\n          // Check if this break matches the current slot\n          const slotDateStr = date.toISOString().split('T')[0];\n          const isDateMatch = breakDate === slotDateStr;\n          const isTimeMatch = breakHour === timeSlot.hour && breakMinute === slotMinute;\n          console.log('    🔍 Checking break:', breakData.datetime);\n          console.log('      🌍 UTC:', breakUtcDateTime.toISOString());\n          console.log('      🏠 Teacher TZ:', breakInTeacherTz);\n          console.log('      📅 Date match:', isDateMatch, `(${breakDate} vs ${slotDateStr})`);\n          console.log('      ⏰ Time match:', isTimeMatch, `(${breakHour}:${breakMinute} vs ${timeSlot.hour}:${slotMinute})`);\n          return isDateMatch && isTimeMatch;\n        }\n      }\n      return false;\n    });\n    console.log('  ❓ Is break slot?', isBreakSlot);\n    if (weeklyBreaks.includes(breakKey)) {\n      console.log('  🚫 SLOT IS BREAK - hiding slot');\n      return false; // Slot is taken as break\n    }\n\n    // Calculate the exact 30-minute slot we're checking (using existing slotMinute)\n    const slotStartMinutes = timeSlot.hour * 60 + slotMinute;\n    const slotEndMinutes = slotStartMinutes + 30;\n\n    // Handle edge case for last half hour of the day (23:30-24:00)\n    if (timeSlot.hour === 23 && !isFirstHalf) {\n      // For 23:30-24:00, check if any available slot covers 23:30\n      return daySlots.some(slot => {\n        const [startTime, endTime] = slot.split('-');\n        const [startHour, startMinute] = startTime.split(':').map(Number);\n        let [endHour, endMinute] = endTime.split(':').map(Number);\n\n        // Handle 24:00 or 00:00 as end time\n        if (endHour === 0 || endHour === 24) {\n          endHour = 24;\n          endMinute = 0;\n        }\n        const availableStartMinutes = startHour * 60 + startMinute;\n        const availableEndMinutes = endHour * 60 + endMinute;\n\n        // Check if 23:30 is covered\n        return slotStartMinutes >= availableStartMinutes && slotStartMinutes < availableEndMinutes;\n      });\n    }\n\n    // Check if this specific 30-minute slot is in the available hours\n    return daySlots.some(slot => {\n      const [startTime, endTime] = slot.split('-');\n      const [startHour, startMinute] = startTime.split(':').map(Number);\n      let [endHour, endMinute] = endTime.split(':').map(Number);\n\n      // Handle 24:00 or 00:00 as end time (next day)\n      if (endHour === 0) {\n        endHour = 24;\n        endMinute = 0;\n      }\n      const availableStartMinutes = startHour * 60 + startMinute;\n      const availableEndMinutes = endHour * 60 + endMinute;\n\n      // Check if the 30-minute slot fits within the available time range\n      return slotStartMinutes >= availableStartMinutes && slotEndMinutes <= availableEndMinutes;\n    });\n  };\n\n  // Define time slots - full hours from 00:00 to 23:00\n  const timeSlots = [];\n  for (let hour = 0; hour < 24; hour++) {\n    const startTime = `${hour.toString().padStart(2, '0')}:00`;\n    const midTime = `${hour.toString().padStart(2, '0')}:30`;\n    const endTime = hour < 23 ? `${(hour + 1).toString().padStart(2, '0')}:00` : '00:00';\n    timeSlots.push({\n      key: `${startTime}-${endTime}`,\n      label: startTime,\n      midLabel: midTime,\n      hour,\n      minute: 0,\n      // Include both half-hour slots for this hour\n      firstHalf: `${startTime}-${midTime}`,\n      secondHalf: hour < 23 ? `${midTime}-${endTime}` : '23:30-00:00'\n    });\n  }\n\n  // Define days of the week\n  const defaultDaysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n  const daysOfWeekData = daysOfWeek ? daysOfWeek.map(day => ({\n    key: day,\n    label: t(`days.${day}`)\n  })) : defaultDaysOfWeek.map(day => ({\n    key: day,\n    label: t(`days.${day}`)\n  }));\n\n  // Get abbreviated day names for mobile\n  const getAbbreviatedDayName = dayKey => {\n    const abbreviations = {\n      sunday: t('days.sundayShort') || 'Sun',\n      monday: t('days.mondayShort') || 'Mon',\n      tuesday: t('days.tuesdayShort') || 'Tue',\n      wednesday: t('days.wednesdayShort') || 'Wed',\n      thursday: t('days.thursdayShort') || 'Thu',\n      friday: t('days.fridayShort') || 'Fri',\n      saturday: t('days.saturdayShort') || 'Sat'\n    };\n    return abbreviations[dayKey] || dayKey.substring(0, 3);\n  };\n\n  // Get meeting status based on current time\n  const getMeetingStatus = booking => {\n    const meetingStartTime = new Date(booking.datetime);\n    const meetingEndTime = new Date(booking.datetime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));\n    const now = new Date();\n    if (booking.status === 'cancelled') {\n      return 'cancelled';\n    }\n    if (now >= meetingStartTime && now < meetingEndTime) {\n      return 'ongoing';\n    }\n    if (now >= meetingEndTime) {\n      return 'completed';\n    }\n    return 'scheduled';\n  };\n\n  // Get background color based on meeting status\n  const getBookingBackgroundColor = booking => {\n    const status = getMeetingStatus(booking);\n    switch (status) {\n      case 'ongoing':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.85)} 0%, ${alpha(theme.palette.success.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.95)} 0%, ${alpha(theme.palette.success.dark, 1)} 100%)`\n        };\n      case 'completed':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.85)} 0%, ${alpha(theme.palette.grey[700], 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.grey[500], 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.95)} 0%, ${alpha(theme.palette.grey[700], 1)} 100%)`\n        };\n      case 'cancelled':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.85)} 0%, ${alpha(theme.palette.error.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.95)} 0%, ${alpha(theme.palette.error.dark, 1)} 100%)`\n        };\n      default:\n        // scheduled\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.85)} 0%, ${alpha(theme.palette.info.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.info.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.95)} 0%, ${alpha(theme.palette.info.dark, 1)} 100%)`\n        };\n    }\n  };\n\n  // Find all bookings for specific day and time slot\n  const findBookingsForSlot = (day, timeSlot) => {\n    if (!bookings || !currentWeekStart) return [];\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const dateStr = format(date, 'yyyy-MM-dd');\n    return bookings.filter(booking => {\n      // Get booking date and time in student timezone\n      let bookingDate, bookingHour, bookingMinute;\n      if (studentProfile && studentProfile.timezone) {\n        // Use student timezone for both date and time\n        const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n        const [datePart, timePart] = formattedDateTime.split(' ');\n        bookingDate = datePart;\n        const [hourStr, minuteStr] = timePart.split(':');\n        bookingHour = parseInt(hourStr);\n        bookingMinute = parseInt(minuteStr);\n      } else {\n        // Fallback to browser local time\n        const bookingDateTime = new Date(booking.datetime);\n        bookingDate = format(bookingDateTime, 'yyyy-MM-dd');\n        bookingHour = bookingDateTime.getHours();\n        bookingMinute = bookingDateTime.getMinutes();\n      }\n\n      // Check if booking matches this date\n      const isDateMatch = bookingDate === dateStr;\n      if (!isDateMatch) return false;\n\n      // Calculate booking start and end times in minutes from midnight\n      const duration = parseInt(booking.duration) || 25;\n      const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n      const bookingEndMinutes = bookingStartMinutes + duration;\n\n      // Calculate slot start and end times in minutes from midnight\n      const slotStartMinutes = timeSlot.hour * 60;\n      const slotEndMinutes = slotStartMinutes + 60;\n\n      // For 50-minute lessons, show in both starting hour and next hour if it spans\n      if (duration === 50) {\n        // Check if booking starts in this slot\n        const startsInThisSlot = bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotEndMinutes;\n\n        // Check if booking extends into this slot from previous hour\n        const extendsIntoThisSlot = bookingStartMinutes < slotStartMinutes && bookingEndMinutes > slotStartMinutes;\n        return startsInThisSlot || extendsIntoThisSlot;\n      } else {\n        // For 25-minute lessons, show in the exact 30-minute slot\n        const slotMiddle = slotStartMinutes + 30;\n\n        // Check if booking is in first half (00-30) or second half (30-60)\n        if (bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle) {\n          // First half booking\n          return bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle;\n        } else if (bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes) {\n          // Second half booking\n          return bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes;\n        }\n      }\n      return false;\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this);\n  }\n  if (!bookings || bookings.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 3,\n        mb: 4,\n        borderRadius: 2,\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: t('bookings.noBookings')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 3,\n    sx: {\n      mb: 4,\n      borderRadius: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n        p: 3,\n        color: 'white',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        flexWrap: 'wrap',\n        gap: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 'bold',\n            mb: 1\n          },\n          children: [\"\\uD83D\\uDCC5 \", t('bookings.weeklyTitle')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            opacity: 0.9\n          },\n          children: t('bookings.weeklyDescription')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        overflow: 'auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '60px repeat(7, minmax(80px, 1fr))',\n            sm: '80px repeat(7, minmax(100px, 1fr))',\n            md: '120px repeat(7, minmax(120px, 1fr))'\n          },\n          bgcolor: alpha(theme.palette.primary.main, 0.05),\n          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          position: 'sticky',\n          top: 0,\n          zIndex: 10\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: {\n              xs: 1.5,\n              sm: 2,\n              md: 2.5\n            },\n            minHeight: {\n              xs: '60px',\n              sm: '75px',\n              md: '90px'\n            },\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 'bold',\n              color: theme.palette.text.secondary,\n              fontSize: {\n                xs: '0.8rem',\n                sm: '0.9rem',\n                md: '1rem'\n              }\n            },\n            children: [\"\\u23F0 \", t('teacher.time')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), daysOfWeekData.map((day, index) => {\n          // Calculate the date for this day\n          const dayDate = currentWeekStart ? addDays(currentWeekStart, index) : new Date();\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: {\n                xs: 1.5,\n                sm: 2,\n                md: 2.5\n              },\n              minHeight: {\n                xs: '60px',\n                sm: '75px',\n                md: '90px'\n              },\n              textAlign: 'center',\n              borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n              '&:last-child': {\n                borderRight: 'none'\n              },\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'center',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 'bold',\n                color: theme.palette.primary.main,\n                fontSize: {\n                  xs: '0.8rem',\n                  sm: '1rem',\n                  md: '1.2rem'\n                },\n                lineHeight: 1.2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                component: \"span\",\n                sx: {\n                  display: {\n                    xs: 'none',\n                    md: 'block'\n                  }\n                },\n                children: day.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"span\",\n                sx: {\n                  display: {\n                    xs: 'none',\n                    sm: 'block',\n                    md: 'none'\n                  }\n                },\n                children: day.label.length > 6 ? day.label.substring(0, 6) : day.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"span\",\n                sx: {\n                  display: {\n                    xs: 'block',\n                    sm: 'none'\n                  }\n                },\n                children: getAbbreviatedDayName(day.key)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                display: 'block',\n                color: theme.palette.text.secondary,\n                fontSize: {\n                  xs: '0.7rem',\n                  sm: '0.8rem',\n                  md: '0.9rem'\n                },\n                mt: 0.5\n              },\n              children: format(dayDate, 'MMM d', {\n                locale: isRtl ? ar : enUS\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)]\n          }, day.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: timeSlots.map((timeSlot, index) => {\n          const isHourStart = timeSlot.minute === 0;\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: {\n                xs: '60px repeat(7, minmax(80px, 1fr))',\n                sm: '80px repeat(7, minmax(100px, 1fr))',\n                md: '120px repeat(7, minmax(120px, 1fr))'\n              },\n              borderBottom: `1px solid ${alpha(theme.palette.divider, isHourStart ? 0.3 : 0.1)}`,\n              bgcolor: index % 4 < 2 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',\n              '&:hover': {\n                bgcolor: alpha(theme.palette.primary.main, 0.05)\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: {\n                  xs: 0.5,\n                  sm: 1,\n                  md: 1.5\n                },\n                display: 'grid',\n                placeItems: 'center',\n                borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                bgcolor: isHourStart ? alpha(theme.palette.primary.main, 0.05) : 'transparent',\n                position: 'relative'\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  gap: 0.5,\n                  position: 'absolute',\n                  left: '50%',\n                  transform: 'translateX(-50%)',\n                  top: timeSlot.minute === 0 ? '-8px' : 'calc(50% + 20px)'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: theme.palette.primary.main,\n                    fontSize: {\n                      xs: '0.6rem',\n                      sm: '0.7rem',\n                      md: '0.8rem'\n                    }\n                  },\n                  children: timeSlot.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this), daysOfWeekData.map(day => {\n              const allBookingsInSlot = findBookingsForSlot(day.key, timeSlot);\n\n              // Filter out extended bookings from previous hour\n              // Filter out extended bookings from previous hour\n              const bookingsInSlot = allBookingsInSlot.filter(booking => {\n                // Get booking start time to determine if it's extended from previous hour\n                let bookingHour = 0;\n                let bookingMinute = 0;\n                if (studentProfile && studentProfile.timezone) {\n                  const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n                  const [, timePart] = formattedDateTime.split(' ');\n                  const [hourStr, minuteStr] = timePart.split(':');\n                  bookingHour = parseInt(hourStr);\n                  bookingMinute = parseInt(minuteStr);\n                } else {\n                  const bookingDate = new Date(booking.datetime);\n                  bookingHour = bookingDate.getHours();\n                  bookingMinute = bookingDate.getMinutes();\n                }\n                const duration = parseInt(booking.duration) || 25;\n                const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n                const currentSlotStartMinutes = timeSlot.hour * 60;\n\n                // Only show bookings that start in this slot or earlier in the same hour\n                // Don't show bookings that extend from previous hour\n                if (duration === 50) {\n                  const extendsIntoThisSlot = bookingStartMinutes < currentSlotStartMinutes;\n                  return !extendsIntoThisSlot; // Filter out extended bookings\n                }\n                return true; // Show all 25-minute bookings\n              });\n\n              // Deduplicate overlapping bookings that share the exact same start time (datetime)\n              // If a non-cancelled booking and a cancelled booking overlap, keep the non-cancelled one\n              // so the student sees the active reservation instead of the cancelled one.\n              const visibleBookingsMap = bookingsInSlot.reduce((acc, curr) => {\n                const key = curr.datetime; // ISO string coming from backend\n                const existing = acc[key];\n                if (!existing) {\n                  acc[key] = curr; // first occurrence\n                } else {\n                  // If the existing booking is cancelled and the new one is not, replace it\n                  if (existing.status === 'cancelled' && curr.status !== 'cancelled') {\n                    acc[key] = curr;\n                  }\n                  // If both have same non-cancelled status keep the earlier inserted one\n                  // otherwise, leave as is.\n                }\n                return acc;\n              }, {});\n              const visibleBookings = Object.values(visibleBookingsMap);\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: {\n                    xs: 0.2,\n                    sm: 0.4,\n                    md: 0.6\n                  },\n                  minHeight: {\n                    xs: '80px',\n                    sm: '100px',\n                    md: '120px'\n                  },\n                  // Increased height for better content fit\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                  '&:last-child': {\n                    borderRight: 'none'\n                  },\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '4px',\n                    bgcolor: theme.palette.primary.main,\n                    zIndex: 0,\n                    borderRadius: '2px',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                    pointerEvents: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: 0,\n                    right: 0,\n                    height: '2px',\n                    bgcolor: alpha(theme.palette.divider, 0.3),\n                    // More transparent\n                    zIndex: 0,\n                    // Lower z-index to stay behind bookings\n                    borderRadius: '1px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 23\n                }, this), visibleBookings.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: visibleBookings.map((booking, bookingIndex) => {\n                    const bookingColors = getBookingBackgroundColor(booking);\n                    const isFullLesson = parseInt(booking.duration) === 50;\n\n                    // Get booking start time to determine position\n                    let bookingMinute = 0;\n                    let bookingHour = 0;\n                    if (studentProfile && studentProfile.timezone) {\n                      const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n                      const [, timePart] = formattedDateTime.split(' ');\n                      const [hourStr, minuteStr] = timePart.split(':');\n                      bookingHour = parseInt(hourStr);\n                      bookingMinute = parseInt(minuteStr);\n                    } else {\n                      const bookingDate = new Date(booking.datetime);\n                      bookingHour = bookingDate.getHours();\n                      bookingMinute = bookingDate.getMinutes();\n                    }\n\n                    // For full lessons, determine which half of this slot the booking occupies\n                    // For half lessons, show in correct half based on start time\n                    let isFirstHalf = bookingMinute === 0;\n\n                    // For full lessons, determine the visual representation\n                    if (isFullLesson) {\n                      // Calculate if this booking spans into the next hour\n                      const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n                      const bookingEndMinutes = bookingStartMinutes + 50; // 50-minute lesson\n                      const currentSlotStartMinutes = timeSlot.hour * 60;\n                      const currentSlotEndMinutes = currentSlotStartMinutes + 60;\n\n                      // Check if booking starts in this slot\n                      const startsInThisSlot = bookingStartMinutes >= currentSlotStartMinutes && bookingStartMinutes < currentSlotEndMinutes;\n                      if (startsInThisSlot) {\n                        // Booking starts in this slot\n                        const extendsToNextHour = bookingEndMinutes > currentSlotEndMinutes;\n                        if (extendsToNextHour) {\n                          // This is a cross-hour booking, show it spanning from current position to next hour\n                          isFirstHalf = 'spanning'; // Special case for spanning bookings\n                        } else {\n                          // Regular full lesson within one hour - don't override first half bookings\n                          isFirstHalf = bookingMinute === 0 ? null : 'secondHalfFull';\n                        }\n                      }\n                    }\n                    return /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '90%',\n                        height: (() => {\n                          if (isFirstHalf === 'spanning') {\n                            // For cross-hour bookings, extend from second half to next hour\n                            return '100%'; // Extend to cover both halves visually\n                          }\n                          if (isFirstHalf === 'secondHalfFull') {\n                            // Full lesson starting from second half - only show in second half\n                            return '40%';\n                          }\n                          return isFullLesson ? '95%' : '40%';\n                        })(),\n                        position: 'absolute',\n                        top: (() => {\n                          if (isFirstHalf === 'spanning') {\n                            // Start from the second half of current slot\n                            return '50%';\n                          }\n                          if (isFirstHalf === 'secondHalfFull') {\n                            // Position in second half only\n                            return '55%';\n                          }\n                          return isFullLesson ? '2.5%' : isFirstHalf ? '5%' : '55%';\n                        })(),\n                        left: '5%',\n                        zIndex: (() => {\n                          if (isFirstHalf === 'spanning') {\n                            return 100; // Very high z-index for spanning bookings\n                          }\n                          return 10 + bookingIndex; // Stacked z-index for multiple bookings\n                        })(),\n                        borderRadius: 1,\n                        background: bookingColors.background,\n                        display: 'flex',\n                        flexDirection: 'column',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        gap: {\n                          xs: 0.2,\n                          sm: 0.3,\n                          md: 0.4\n                        },\n                        p: {\n                          xs: 0.2,\n                          sm: 0.3,\n                          md: 0.4\n                        },\n                        cursor: 'pointer',\n                        border: bookingColors.border,\n                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                        '&:hover': {\n                          background: bookingColors.hoverBackground,\n                          transform: 'translateY(-1px)',\n                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n                        },\n                        transition: 'all 0.2s ease-in-out',\n                        overflow: 'hidden'\n                      },\n                      onClick: () => onViewDetails(booking),\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          color: 'white',\n                          fontWeight: 'bold',\n                          fontSize: {\n                            xs: '0.6rem',\n                            sm: '0.7rem',\n                            md: '0.75rem'\n                          },\n                          textAlign: 'center',\n                          lineHeight: 1.1,\n                          overflow: 'hidden',\n                          textOverflow: 'ellipsis',\n                          display: '-webkit-box',\n                          WebkitLineClamp: 1,\n                          WebkitBoxOrient: 'vertical',\n                          maxWidth: '100%',\n                          textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n                          mb: {\n                            xs: 0.1,\n                            sm: 0.2,\n                            md: 0.2\n                          }\n                        },\n                        children: isTeacherView ? booking.student_name : booking.teacher_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 785,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          gap: {\n                            xs: 0.2,\n                            sm: 0.3,\n                            md: 0.3\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          size: \"small\",\n                          variant: \"contained\",\n                          onClick: e => {\n                            e.stopPropagation();\n                            onViewDetails(booking);\n                          },\n                          sx: {\n                            minWidth: 'auto',\n                            width: {\n                              xs: 18,\n                              sm: 22,\n                              md: 24\n                            },\n                            height: {\n                              xs: 16,\n                              sm: 20,\n                              md: 22\n                            },\n                            p: 0,\n                            bgcolor: 'rgba(255, 255, 255, 0.2)',\n                            color: 'white',\n                            borderRadius: 0.5,\n                            '&:hover': {\n                              bgcolor: 'rgba(255, 255, 255, 0.3)'\n                            }\n                          },\n                          children: /*#__PURE__*/_jsxDEV(ViewIcon, {\n                            sx: {\n                              fontSize: {\n                                xs: '0.6rem',\n                                sm: '0.7rem',\n                                md: '0.8rem'\n                              }\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 827,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 807,\n                          columnNumber: 35\n                        }, this), booking.status === 'scheduled' && onCancelBooking && !isTeacherView && /*#__PURE__*/_jsxDEV(Button, {\n                          size: \"small\",\n                          variant: \"contained\",\n                          onClick: e => {\n                            e.stopPropagation();\n                            onCancelBooking(booking);\n                          },\n                          sx: {\n                            minWidth: 'auto',\n                            width: {\n                              xs: 18,\n                              sm: 22,\n                              md: 24\n                            },\n                            height: {\n                              xs: 16,\n                              sm: 20,\n                              md: 22\n                            },\n                            p: 0,\n                            bgcolor: 'rgba(244, 67, 54, 0.8)',\n                            color: 'white',\n                            borderRadius: 0.5,\n                            '&:hover': {\n                              bgcolor: 'rgba(244, 67, 54, 1)'\n                            }\n                          },\n                          children: /*#__PURE__*/_jsxDEV(CancelIcon, {\n                            sx: {\n                              fontSize: {\n                                xs: '0.6rem',\n                                sm: '0.7rem',\n                                md: '0.8rem'\n                              }\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 850,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 830,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 806,\n                        columnNumber: 33\n                      }, this)]\n                    }, `${booking.id}-${bookingIndex}`, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 31\n                    }, this);\n                  })\n                }, void 0, false) :\n                /*#__PURE__*/\n                // Empty slot - show two halves for 30-minute slots\n                _jsxDEV(Box, {\n                  sx: {\n                    width: '90%',\n                    height: {\n                      xs: '65px',\n                      sm: '85px',\n                      md: '105px'\n                    },\n                    borderRadius: 1.5,\n                    border: `1px solid ${alpha(theme.palette.grey[300], 0.5)}`,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    overflow: 'hidden',\n                    zIndex: 1\n                  },\n                  children: [(() => {\n                    const firstHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, true);\n                    const firstHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, true);\n                    return /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        flex: 1,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        borderBottom: `1px solid ${alpha(theme.palette.grey[300], 0.3)}`,\n                        bgcolor: firstHalfInPast ? alpha(theme.palette.grey[600], 0.15) : firstHalfAvailable ? alpha(theme.palette.success.main, 0.08) : alpha(theme.palette.grey[100], 0.3),\n                        transition: 'all 0.2s ease'\n                      },\n                      children: firstHalfInPast ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                        title: `${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.pastSlot', 'Past Time')}`,\n                        arrow: true,\n                        children: /*#__PURE__*/_jsxDEV(RadioButtonUncheckedIcon, {\n                          sx: {\n                            color: theme.palette.grey[600],\n                            fontSize: {\n                              xs: '0.7rem',\n                              sm: '0.8rem',\n                              md: '0.9rem'\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 891,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 890,\n                        columnNumber: 35\n                      }, this) : firstHalfAvailable ? /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 0.5\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: `${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.availableSlot', 'Available')}`,\n                          arrow: true,\n                          children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                            sx: {\n                              color: theme.palette.success.main,\n                              fontSize: {\n                                xs: '0.9rem',\n                                sm: '1rem',\n                                md: '1.1rem'\n                              },\n                              cursor: 'pointer'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 901,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 900,\n                          columnNumber: 37\n                        }, this), onTakeBreak && /*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: t('bookings.takeBreak', 'Take Break'),\n                          arrow: true,\n                          children: /*#__PURE__*/_jsxDEV(IconButton, {\n                            size: \"small\",\n                            onClick: () => handleTakeBreak(day.key, timeSlot, true),\n                            sx: {\n                              color: theme.palette.warning.main,\n                              fontSize: {\n                                xs: '0.6rem',\n                                sm: '0.7rem',\n                                md: '0.8rem'\n                              },\n                              padding: '2px',\n                              '&:hover': {\n                                bgcolor: alpha(theme.palette.warning.main, 0.1)\n                              }\n                            },\n                            children: /*#__PURE__*/_jsxDEV(RestIcon, {\n                              sx: {\n                                fontSize: 'inherit'\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 923,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 911,\n                            columnNumber: 41\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 910,\n                          columnNumber: 39\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 899,\n                        columnNumber: 35\n                      }, this) : /*#__PURE__*/_jsxDEV(RadioButtonUncheckedIcon, {\n                        sx: {\n                          color: alpha(theme.palette.grey[400], 0.5),\n                          fontSize: {\n                            xs: '0.7rem',\n                            sm: '0.8rem',\n                            md: '0.9rem'\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 929,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 876,\n                      columnNumber: 31\n                    }, this);\n                  })(), (() => {\n                    const secondHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, false);\n                    const secondHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, false);\n                    return /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        flex: 1,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        bgcolor: secondHalfInPast ? alpha(theme.palette.grey[600], 0.15) : secondHalfAvailable ? alpha(theme.palette.success.main, 0.08) : alpha(theme.palette.grey[100], 0.3),\n                        transition: 'all 0.2s ease'\n                      },\n                      children: secondHalfInPast ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                        title: `${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.pastSlot', 'Past Time')}`,\n                        arrow: true,\n                        children: /*#__PURE__*/_jsxDEV(RadioButtonUncheckedIcon, {\n                          sx: {\n                            color: theme.palette.grey[600],\n                            fontSize: {\n                              xs: '0.7rem',\n                              sm: '0.8rem',\n                              md: '0.9rem'\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 960,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 959,\n                        columnNumber: 35\n                      }, this) : secondHalfAvailable ? /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 0.5\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: `${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.availableSlot', 'Available')}`,\n                          arrow: true,\n                          children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                            sx: {\n                              color: theme.palette.success.main,\n                              fontSize: {\n                                xs: '0.9rem',\n                                sm: '1rem',\n                                md: '1.1rem'\n                              },\n                              cursor: 'pointer'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 970,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 969,\n                          columnNumber: 37\n                        }, this), onTakeBreak && /*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: t('bookings.takeBreak', 'Take Break'),\n                          arrow: true,\n                          children: /*#__PURE__*/_jsxDEV(IconButton, {\n                            size: \"small\",\n                            onClick: () => handleTakeBreak(day.key, timeSlot, false),\n                            sx: {\n                              color: theme.palette.warning.main,\n                              fontSize: {\n                                xs: '0.6rem',\n                                sm: '0.7rem',\n                                md: '0.8rem'\n                              },\n                              padding: '2px',\n                              '&:hover': {\n                                bgcolor: alpha(theme.palette.warning.main, 0.1)\n                              }\n                            },\n                            children: /*#__PURE__*/_jsxDEV(RestIcon, {\n                              sx: {\n                                fontSize: 'inherit'\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 992,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 980,\n                            columnNumber: 41\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 979,\n                          columnNumber: 39\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 968,\n                        columnNumber: 35\n                      }, this) : /*#__PURE__*/_jsxDEV(RadioButtonUncheckedIcon, {\n                        sx: {\n                          color: alpha(theme.palette.grey[400], 0.5),\n                          fontSize: {\n                            xs: '0.7rem',\n                            sm: '0.8rem',\n                            md: '0.9rem'\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 998,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 946,\n                      columnNumber: 31\n                    }, this);\n                  })()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 25\n                }, this)]\n              }, `${day.key}-${timeSlot.key}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 21\n              }, this);\n            })]\n          }, timeSlot.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: breakDialogOpen,\n      onClose: () => setBreakDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          textAlign: 'center',\n          pb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(RestIcon, {\n          sx: {\n            fontSize: '2rem',\n            color: 'warning.main',\n            mb: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1027,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          children: t('bookings.takeBreakTitle', 'أخذ هذا الوقت راحة')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        sx: {\n          textAlign: 'center',\n          py: 3\n        },\n        children: selectedBreakSlot && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              mb: 2\n            },\n            children: t('bookings.takeBreakMessage', 'هل تريد أخذ هذا الوقت راحة؟')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: alpha(theme.palette.warning.main, 0.1),\n              p: 2,\n              borderRadius: 2,\n              border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                color: 'warning.main',\n                mb: 1\n              },\n              children: [\"\\uD83D\\uDCC5 \", format(selectedBreakSlot.date, 'EEEE, MMM d, yyyy', {\n                locale: isRtl ? ar : enUS\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: [\"\\uD83D\\uDD50 \", (() => {\n                const startHour = selectedBreakSlot.hour;\n                const startMinute = selectedBreakSlot.minute;\n                const endMinute = startMinute + 30;\n                const endHour = endMinute >= 60 ? startHour + 1 : startHour;\n                const finalEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;\n                return `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${finalEndMinute.toString().padStart(2, '0')}`;\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              mt: 2,\n              color: 'text.secondary'\n            },\n            children: t('bookings.takeBreakNote', 'سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1062,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1033,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          justifyContent: 'center',\n          pb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setBreakDialogOpen(false),\n          variant: \"outlined\",\n          sx: {\n            minWidth: 100\n          },\n          children: t('common.cancel', 'إلغاء')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1070,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: confirmTakeBreak,\n          variant: \"contained\",\n          color: \"warning\",\n          sx: {\n            minWidth: 100,\n            ml: 2\n          },\n          children: t('common.confirm', 'موافق')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1069,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1020,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 414,\n    columnNumber: 5\n  }, this);\n};\n_s(WeeklyBookingsTable, \"y7GCCAmpHR9qQkxYNIue10eOkJE=\", false, function () {\n  return [useTranslation, useTheme];\n});\n_c = WeeklyBookingsTable;\nexport default WeeklyBookingsTable;\nvar _c;\n$RefreshReg$(_c, \"WeeklyBookingsTable\");", "map": {"version": 3, "names": ["React", "useTranslation", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Chip", "useTheme", "alpha", "CircularProgress", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "Visibility", "ViewIcon", "Cancel", "CancelIcon", "AccessTime", "AccessTimeIcon", "RadioButton<PERSON><PERSON><PERSON>ed", "RadioButtonUncheckedIcon", "CheckCircle", "CheckCircleIcon", "SelfImprovement", "RestIcon", "format", "addDays", "ar", "enUS", "moment", "formatDateInStudentTimezone", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WeeklyBookingsTable", "bookings", "loading", "currentWeekStart", "daysOfWeek", "onViewDetails", "onCancelBooking", "studentProfile", "formatBookingTime", "getStatusColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "availableHours", "onTakeBreak", "weeklyBreaks", "_s", "t", "i18n", "theme", "isRtl", "language", "breakDialogOpen", "setBreakDialogOpen", "useState", "selectedBreakSlot", "setSelectedBreakSlot", "handleTakeBreak", "day", "timeSlot", "isFirstHalf", "dayIndex", "daysOfWeekData", "findIndex", "d", "key", "date", "slotMinute", "hour", "minute", "confirmTakeBreak", "isHalfHourSlotInPast", "slotStartTime", "Date", "setHours", "currentTime", "timezone", "currentTimeStr", "toISOString", "error", "console", "isHalfHourSlotAvailable", "<PERSON><PERSON><PERSON>", "toLowerCase", "daySlots", "Array", "isArray", "slotDateTime", "log", "split", "isBreakSlot", "some", "breakData", "datetime", "breakUtcDateTime", "teacherTimezone", "breakInTeacherTz", "breakDate", "breakTime", "breakHour", "breakMinute", "map", "Number", "slotDateStr", "isDateMatch", "isTimeMatch", "includes", "<PERSON><PERSON><PERSON>", "slotStartMinutes", "slotEndMinutes", "slot", "startTime", "endTime", "startHour", "startMinute", "endHour", "endMinute", "availableStartMinutes", "availableEndMinutes", "timeSlots", "toString", "padStart", "midTime", "push", "label", "midLabel", "firstHalf", "secondHalf", "defaultDaysOfWeek", "getAbbreviatedDayName", "abbreviations", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "substring", "getMeetingStatus", "booking", "meetingStartTime", "meetingEndTime", "setMinutes", "getMinutes", "parseInt", "duration", "now", "status", "getBookingBackgroundColor", "background", "palette", "success", "main", "dark", "border", "hoverBackground", "grey", "info", "findBookingsForSlot", "dateStr", "filter", "bookingDate", "bookingHour", "bookingMinute", "formattedDateTime", "datePart", "timePart", "hourStr", "minuteStr", "bookingDateTime", "getHours", "bookingStartMinutes", "bookingEndMinutes", "startsInThisSlot", "extendsIntoThisSlot", "slotMiddle", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "elevation", "p", "mb", "borderRadius", "textAlign", "variant", "color", "primary", "alignItems", "flexWrap", "gap", "fontWeight", "opacity", "overflow", "gridTemplateColumns", "xs", "sm", "md", "bgcolor", "borderBottom", "position", "top", "zIndex", "minHeight", "borderRight", "text", "secondary", "fontSize", "index", "dayDate", "flexDirection", "lineHeight", "component", "mt", "locale", "isHourStart", "divider", "placeItems", "left", "transform", "allBookingsInSlot", "bookingsInSlot", "currentSlotStartMinutes", "visibleBookingsMap", "reduce", "acc", "curr", "existing", "visibleBookings", "Object", "values", "right", "height", "boxShadow", "pointerEvents", "bookingIndex", "bookingColors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentSlotEndMinutes", "extendsToNextHour", "width", "cursor", "transition", "onClick", "textOverflow", "WebkitLineClamp", "WebkitBoxOrient", "max<PERSON><PERSON><PERSON>", "textShadow", "student_name", "teacher_name", "size", "e", "stopPropagation", "min<PERSON><PERSON><PERSON>", "id", "firstHalfAvailable", "firstHalfInPast", "flex", "title", "arrow", "warning", "padding", "secondHalfAvailable", "secondHalfInPast", "open", "onClose", "fullWidth", "pb", "py", "finalEndMinute", "ml", "_c", "$RefreshReg$"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/WeeklyBookingsTable.js"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Chip,\n  useTheme,\n  alpha,\n  CircularProgress,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  IconButton\n} from '@mui/material';\nimport {\n  Visibility as ViewIcon,\n  Cancel as CancelIcon,\n  AccessTime as AccessTimeIcon,\n  RadioButtonUnchecked as RadioButtonUncheckedIcon,\n  CheckCircle as CheckCircleIcon,\n  SelfImprovement as RestIcon\n} from '@mui/icons-material';\nimport { format, addDays } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport moment from 'moment-timezone';\nimport { formatDateInStudentTimezone } from '../utils/timezone';\n\nconst WeeklyBookingsTable = ({\n  bookings,\n  loading = false,\n  currentWeekStart,\n  daysOfWeek,\n  onViewDetails,\n  onCancelBooking,\n  studentProfile,\n  formatBookingTime,\n  getStatusColor,\n  isTeacherView = false,\n  availableHours = null,\n  onTakeBreak = null,\n  weeklyBreaks = []\n}) => {\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  // State for break dialog\n  const [breakDialogOpen, setBreakDialogOpen] = React.useState(false);\n  const [selectedBreakSlot, setSelectedBreakSlot] = React.useState(null);\n\n  // Handle take break\n  const handleTakeBreak = (day, timeSlot, isFirstHalf) => {\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const slotMinute = isFirstHalf ? 0 : 30;\n\n    setSelectedBreakSlot({\n      day,\n      date,\n      hour: timeSlot.hour,\n      minute: slotMinute,\n      isFirstHalf,\n      timeSlot\n    });\n    setBreakDialogOpen(true);\n  };\n\n  const confirmTakeBreak = () => {\n    if (selectedBreakSlot && onTakeBreak) {\n      onTakeBreak(selectedBreakSlot);\n    }\n    setBreakDialogOpen(false);\n    setSelectedBreakSlot(null);\n  };\n\n  // Check if a half-hour slot is in the past (considering teacher's timezone)\n  const isHalfHourSlotInPast = (day, timeSlot, isFirstHalf) => {\n    try {\n      const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n      const date = addDays(currentWeekStart, dayIndex);\n\n      const slotMinute = isFirstHalf ? 0 : 30;\n\n      // Create the slot start datetime\n      const slotStartTime = new Date(date);\n      slotStartTime.setHours(timeSlot.hour, slotMinute, 0, 0);\n\n      // Get current time in teacher's timezone using the same method as the table\n      let currentTime;\n      if (studentProfile && studentProfile.timezone) {\n        // Use teacher's timezone\n        const currentTimeStr = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n        currentTime = new Date(currentTimeStr);\n      } else {\n        // Fallback to browser local time\n        currentTime = new Date();\n      }\n\n      // A slot is considered \"past\" if its start time has already passed\n      // This means the current half-hour slot is also considered past\n      return slotStartTime <= currentTime;\n\n    } catch (error) {\n      console.error('Error checking if slot is in past:', error);\n      return false;\n    }\n  };\n\n  // Check if a specific half-hour slot is available\n  const isHalfHourSlotAvailable = (day, timeSlot, isFirstHalf) => {\n    if (!availableHours || !isTeacherView) return false;\n\n    const dayKey = day.toLowerCase();\n    const daySlots = availableHours[dayKey];\n\n    if (!daySlots || !Array.isArray(daySlots)) return false;\n\n    // Check if this slot is taken as a break\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const slotMinute = isFirstHalf ? 0 : 30;\n\n    // Check if this slot matches any break from the backend\n    // Backend now sends UTC datetimes, so we need to convert them to teacher's timezone\n    // and check if they match this slot\n\n    // Create the current slot datetime in teacher's timezone\n    const slotDateTime = new Date(date);\n    slotDateTime.setHours(timeSlot.hour, slotMinute, 0, 0);\n\n    console.log('🔍 FRONTEND CHECK: Checking if slot is break');\n    console.log('  📅 Slot Date:', date.toISOString().split('T')[0]);\n    console.log('  ⏰ Slot Hour:', timeSlot.hour, 'Minute:', slotMinute);\n    console.log('  📅 Slot DateTime:', slotDateTime.toISOString());\n\n    // Check if any break matches this slot\n    const isBreakSlot = weeklyBreaks.some(breakData => {\n      if (typeof breakData === 'string') {\n        // Old format - ignore for now\n        return false;\n      }\n\n      if (breakData && breakData.datetime) {\n        // New format - UTC datetime from backend\n        const breakUtcDateTime = new Date(breakData.datetime);\n\n        // Convert break UTC time to teacher's timezone\n        if (studentProfile && studentProfile.timezone) {\n          const teacherTimezone = studentProfile.timezone;\n          const breakInTeacherTz = formatDateInStudentTimezone(breakData.datetime, teacherTimezone, 'YYYY-MM-DD HH:mm:ss');\n          const [breakDate, breakTime] = breakInTeacherTz.split(' ');\n          const [breakHour, breakMinute] = breakTime.split(':').map(Number);\n\n          // Check if this break matches the current slot\n          const slotDateStr = date.toISOString().split('T')[0];\n          const isDateMatch = breakDate === slotDateStr;\n          const isTimeMatch = breakHour === timeSlot.hour && breakMinute === slotMinute;\n\n          console.log('    🔍 Checking break:', breakData.datetime);\n          console.log('      🌍 UTC:', breakUtcDateTime.toISOString());\n          console.log('      🏠 Teacher TZ:', breakInTeacherTz);\n          console.log('      📅 Date match:', isDateMatch, `(${breakDate} vs ${slotDateStr})`);\n          console.log('      ⏰ Time match:', isTimeMatch, `(${breakHour}:${breakMinute} vs ${timeSlot.hour}:${slotMinute})`);\n\n          return isDateMatch && isTimeMatch;\n        }\n      }\n\n      return false;\n    });\n\n    console.log('  ❓ Is break slot?', isBreakSlot);\n\n    if (weeklyBreaks.includes(breakKey)) {\n      console.log('  🚫 SLOT IS BREAK - hiding slot');\n      return false; // Slot is taken as break\n    }\n\n    // Calculate the exact 30-minute slot we're checking (using existing slotMinute)\n    const slotStartMinutes = timeSlot.hour * 60 + slotMinute;\n    const slotEndMinutes = slotStartMinutes + 30;\n\n    // Handle edge case for last half hour of the day (23:30-24:00)\n    if (timeSlot.hour === 23 && !isFirstHalf) {\n      // For 23:30-24:00, check if any available slot covers 23:30\n      return daySlots.some(slot => {\n        const [startTime, endTime] = slot.split('-');\n        const [startHour, startMinute] = startTime.split(':').map(Number);\n        let [endHour, endMinute] = endTime.split(':').map(Number);\n\n        // Handle 24:00 or 00:00 as end time\n        if (endHour === 0 || endHour === 24) {\n          endHour = 24;\n          endMinute = 0;\n        }\n\n        const availableStartMinutes = startHour * 60 + startMinute;\n        const availableEndMinutes = endHour * 60 + endMinute;\n\n        // Check if 23:30 is covered\n        return slotStartMinutes >= availableStartMinutes &&\n               slotStartMinutes < availableEndMinutes;\n      });\n    }\n\n    // Check if this specific 30-minute slot is in the available hours\n    return daySlots.some(slot => {\n      const [startTime, endTime] = slot.split('-');\n      const [startHour, startMinute] = startTime.split(':').map(Number);\n      let [endHour, endMinute] = endTime.split(':').map(Number);\n\n      // Handle 24:00 or 00:00 as end time (next day)\n      if (endHour === 0) {\n        endHour = 24;\n        endMinute = 0;\n      }\n\n      const availableStartMinutes = startHour * 60 + startMinute;\n      const availableEndMinutes = endHour * 60 + endMinute;\n\n      // Check if the 30-minute slot fits within the available time range\n      return slotStartMinutes >= availableStartMinutes && slotEndMinutes <= availableEndMinutes;\n    });\n  };\n\n  // Define time slots - full hours from 00:00 to 23:00\n  const timeSlots = [];\n  for (let hour = 0; hour < 24; hour++) {\n    const startTime = `${hour.toString().padStart(2, '0')}:00`;\n    const midTime = `${hour.toString().padStart(2, '0')}:30`;\n    const endTime = hour < 23 ? `${(hour + 1).toString().padStart(2, '0')}:00` : '00:00';\n    timeSlots.push({\n      key: `${startTime}-${endTime}`,\n      label: startTime,\n      midLabel: midTime,\n      hour,\n      minute: 0,\n      // Include both half-hour slots for this hour\n      firstHalf: `${startTime}-${midTime}`,\n      secondHalf: hour < 23 ? `${midTime}-${endTime}` : '23:30-00:00'\n    });\n  }\n\n  // Define days of the week\n  const defaultDaysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n  const daysOfWeekData = daysOfWeek ? daysOfWeek.map(day => ({\n    key: day,\n    label: t(`days.${day}`)\n  })) : defaultDaysOfWeek.map(day => ({\n    key: day,\n    label: t(`days.${day}`)\n  }));\n\n  // Get abbreviated day names for mobile\n  const getAbbreviatedDayName = (dayKey) => {\n    const abbreviations = {\n      sunday: t('days.sundayShort') || 'Sun',\n      monday: t('days.mondayShort') || 'Mon',\n      tuesday: t('days.tuesdayShort') || 'Tue',\n      wednesday: t('days.wednesdayShort') || 'Wed',\n      thursday: t('days.thursdayShort') || 'Thu',\n      friday: t('days.fridayShort') || 'Fri',\n      saturday: t('days.saturdayShort') || 'Sat'\n    };\n    return abbreviations[dayKey] || dayKey.substring(0, 3);\n  };\n\n  // Get meeting status based on current time\n  const getMeetingStatus = (booking) => {\n    const meetingStartTime = new Date(booking.datetime);\n    const meetingEndTime = new Date(booking.datetime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));\n    const now = new Date();\n\n    if (booking.status === 'cancelled') {\n      return 'cancelled';\n    }\n\n    if (now >= meetingStartTime && now < meetingEndTime) {\n      return 'ongoing';\n    }\n\n    if (now >= meetingEndTime) {\n      return 'completed';\n    }\n\n    return 'scheduled';\n  };\n\n  // Get background color based on meeting status\n  const getBookingBackgroundColor = (booking) => {\n    const status = getMeetingStatus(booking);\n\n    switch (status) {\n      case 'ongoing':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.85)} 0%, ${alpha(theme.palette.success.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.95)} 0%, ${alpha(theme.palette.success.dark, 1)} 100%)`\n        };\n      case 'completed':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.85)} 0%, ${alpha(theme.palette.grey[700], 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.grey[500], 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.95)} 0%, ${alpha(theme.palette.grey[700], 1)} 100%)`\n        };\n      case 'cancelled':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.85)} 0%, ${alpha(theme.palette.error.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.95)} 0%, ${alpha(theme.palette.error.dark, 1)} 100%)`\n        };\n      default: // scheduled\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.85)} 0%, ${alpha(theme.palette.info.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.info.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.95)} 0%, ${alpha(theme.palette.info.dark, 1)} 100%)`\n        };\n    }\n  };\n\n  // Find all bookings for specific day and time slot\n  const findBookingsForSlot = (day, timeSlot) => {\n    if (!bookings || !currentWeekStart) return [];\n\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const dateStr = format(date, 'yyyy-MM-dd');\n\n    return bookings.filter(booking => {\n      // Get booking date and time in student timezone\n      let bookingDate, bookingHour, bookingMinute;\n\n      if (studentProfile && studentProfile.timezone) {\n        // Use student timezone for both date and time\n        const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n        const [datePart, timePart] = formattedDateTime.split(' ');\n        bookingDate = datePart;\n\n        const [hourStr, minuteStr] = timePart.split(':');\n        bookingHour = parseInt(hourStr);\n        bookingMinute = parseInt(minuteStr);\n      } else {\n        // Fallback to browser local time\n        const bookingDateTime = new Date(booking.datetime);\n        bookingDate = format(bookingDateTime, 'yyyy-MM-dd');\n        bookingHour = bookingDateTime.getHours();\n        bookingMinute = bookingDateTime.getMinutes();\n      }\n\n      // Check if booking matches this date\n      const isDateMatch = bookingDate === dateStr;\n      if (!isDateMatch) return false;\n\n      // Calculate booking start and end times in minutes from midnight\n      const duration = parseInt(booking.duration) || 25;\n      const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n      const bookingEndMinutes = bookingStartMinutes + duration;\n\n      // Calculate slot start and end times in minutes from midnight\n      const slotStartMinutes = timeSlot.hour * 60;\n      const slotEndMinutes = slotStartMinutes + 60;\n\n      // For 50-minute lessons, show in both starting hour and next hour if it spans\n      if (duration === 50) {\n        // Check if booking starts in this slot\n        const startsInThisSlot = bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotEndMinutes;\n\n        // Check if booking extends into this slot from previous hour\n        const extendsIntoThisSlot = bookingStartMinutes < slotStartMinutes && bookingEndMinutes > slotStartMinutes;\n\n        return startsInThisSlot || extendsIntoThisSlot;\n      } else {\n        // For 25-minute lessons, show in the exact 30-minute slot\n        const slotMiddle = slotStartMinutes + 30;\n\n        // Check if booking is in first half (00-30) or second half (30-60)\n        if (bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle) {\n          // First half booking\n          return bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle;\n        } else if (bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes) {\n          // Second half booking\n          return bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes;\n        }\n      }\n\n      return false;\n    });\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  if (!bookings || bookings.length === 0) {\n    return (\n      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2, textAlign: 'center' }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          {t('bookings.noBookings')}\n        </Typography>\n      </Paper>\n    );\n  }\n\n  return (\n    <Paper elevation={3} sx={{ mb: 4, borderRadius: 2 }}>\n      {/* Header */}\n      <Box sx={{\n        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n        p: 3,\n        color: 'white',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        flexWrap: 'wrap',\n        gap: 2\n      }}>\n        <Box>\n          <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 1 }}>\n            📅 {t('bookings.weeklyTitle')}\n          </Typography>\n          <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n            {t('bookings.weeklyDescription')}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Calendar Table */}\n      <Box sx={{ overflow: 'auto' }}>\n        {/* Days Header */}\n        <Box sx={{\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '60px repeat(7, minmax(80px, 1fr))',\n            sm: '80px repeat(7, minmax(100px, 1fr))',\n            md: '120px repeat(7, minmax(120px, 1fr))',\n          },\n          bgcolor: alpha(theme.palette.primary.main, 0.05),\n          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          position: 'sticky',\n          top: 0,\n          zIndex: 10\n        }}>\n          <Box sx={{\n            p: { xs: 1.5, sm: 2, md: 2.5 },\n            minHeight: { xs: '60px', sm: '75px', md: '90px' },\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`\n          }}>\n            <Typography variant=\"body2\" sx={{\n              fontWeight: 'bold',\n              color: theme.palette.text.secondary,\n              fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' }\n            }}>\n              ⏰ {t('teacher.time')}\n            </Typography>\n          </Box>\n          {daysOfWeekData.map((day, index) => {\n            // Calculate the date for this day\n            const dayDate = currentWeekStart ? addDays(currentWeekStart, index) : new Date();\n            \n            return (\n              <Box\n                key={day.key}\n                sx={{\n                  p: { xs: 1.5, sm: 2, md: 2.5 },\n                  minHeight: { xs: '60px', sm: '75px', md: '90px' },\n                  textAlign: 'center',\n                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                  '&:last-child': { borderRight: 'none' },\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center',\n                  alignItems: 'center'\n                }}\n              >\n                <Typography variant=\"h6\" sx={{\n                  fontWeight: 'bold',\n                  color: theme.palette.primary.main,\n                  fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' },\n                  lineHeight: 1.2\n                }}>\n                  {/* Day name */}\n                  <Box component=\"span\" sx={{ display: { xs: 'none', md: 'block' } }}>\n                    {day.label}\n                  </Box>\n                  <Box component=\"span\" sx={{ display: { xs: 'none', sm: 'block', md: 'none' } }}>\n                    {day.label.length > 6 ? day.label.substring(0, 6) : day.label}\n                  </Box>\n                  <Box component=\"span\" sx={{ display: { xs: 'block', sm: 'none' } }}>\n                    {getAbbreviatedDayName(day.key)}\n                  </Box>\n                </Typography>\n                \n                {/* Date */}\n                <Typography variant=\"caption\" sx={{\n                  display: 'block',\n                  color: theme.palette.text.secondary,\n                  fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' },\n                  mt: 0.5\n                }}>\n                  {format(dayDate, 'MMM d', { locale: isRtl ? ar : enUS })}\n                </Typography>\n              </Box>\n            );\n          })}\n        </Box>\n\n        {/* Time Slots Grid */}\n        <Box>\n          {timeSlots.map((timeSlot, index) => {\n            const isHourStart = timeSlot.minute === 0;\n            return (\n              <Box\n                key={timeSlot.key}\n                sx={{\n                  display: 'grid',\n                  gridTemplateColumns: {\n                    xs: '60px repeat(7, minmax(80px, 1fr))',\n                    sm: '80px repeat(7, minmax(100px, 1fr))',\n                    md: '120px repeat(7, minmax(120px, 1fr))',\n                  },\n                  borderBottom: `1px solid ${alpha(theme.palette.divider, isHourStart ? 0.3 : 0.1)}`,\n                  bgcolor: index % 4 < 2 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',\n                  '&:hover': {\n                    bgcolor: alpha(theme.palette.primary.main, 0.05)\n                  }\n                }}\n              >\n                {/* Time Labels */}\n                <Box sx={{\n                  p: { xs: 0.5, sm: 1, md: 1.5 },\n                  display: 'grid',\n                  placeItems: 'center',\n                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                  bgcolor: isHourStart ? alpha(theme.palette.primary.main, 0.05) : 'transparent',\n                  position: 'relative'\n                }}>\n                  <Box sx={{ \n                    display: 'flex', \n                    flexDirection: 'column', \n                    alignItems: 'center', \n                    gap: 0.5,\n                    position: 'absolute',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    top: timeSlot.minute === 0 ? '-8px' : 'calc(50% + 20px)'\n                  }}\n                  >\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        fontWeight: 'bold',\n                        color: theme.palette.primary.main,\n                        fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' }\n                      }}\n                    >\n                      {timeSlot.label}\n                    </Typography>\n                  </Box>\n                </Box>\n\n                {/* Day Cells */}\n                {daysOfWeekData.map((day) => {\n                  const allBookingsInSlot = findBookingsForSlot(day.key, timeSlot);\n\n                  // Filter out extended bookings from previous hour\n                  // Filter out extended bookings from previous hour\n          const bookingsInSlot = allBookingsInSlot.filter(booking => {\n                    // Get booking start time to determine if it's extended from previous hour\n                    let bookingHour = 0;\n                    let bookingMinute = 0;\n\n                    if (studentProfile && studentProfile.timezone) {\n                      const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n                      const [, timePart] = formattedDateTime.split(' ');\n                      const [hourStr, minuteStr] = timePart.split(':');\n                      bookingHour = parseInt(hourStr);\n                      bookingMinute = parseInt(minuteStr);\n                    } else {\n                      const bookingDate = new Date(booking.datetime);\n                      bookingHour = bookingDate.getHours();\n                      bookingMinute = bookingDate.getMinutes();\n                    }\n\n                    const duration = parseInt(booking.duration) || 25;\n                    const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n                    const currentSlotStartMinutes = timeSlot.hour * 60;\n\n                    // Only show bookings that start in this slot or earlier in the same hour\n                    // Don't show bookings that extend from previous hour\n                    if (duration === 50) {\n                      const extendsIntoThisSlot = bookingStartMinutes < currentSlotStartMinutes;\n                      return !extendsIntoThisSlot; // Filter out extended bookings\n                    }\n\n                    return true; // Show all 25-minute bookings\n                  });\n\n                  // Deduplicate overlapping bookings that share the exact same start time (datetime)\n                  // If a non-cancelled booking and a cancelled booking overlap, keep the non-cancelled one\n                  // so the student sees the active reservation instead of the cancelled one.\n                  const visibleBookingsMap = bookingsInSlot.reduce((acc, curr) => {\n                    const key = curr.datetime; // ISO string coming from backend\n                    const existing = acc[key];\n\n                    if (!existing) {\n                      acc[key] = curr; // first occurrence\n                    } else {\n                      // If the existing booking is cancelled and the new one is not, replace it\n                      if (existing.status === 'cancelled' && curr.status !== 'cancelled') {\n                        acc[key] = curr;\n                      }\n                      // If both have same non-cancelled status keep the earlier inserted one\n                      // otherwise, leave as is.\n                    }\n                    return acc;\n                  }, {});\n\n                  const visibleBookings = Object.values(visibleBookingsMap);\n\n                  return (\n                    <Box\n                      key={`${day.key}-${timeSlot.key}`}\n                      sx={{\n                        p: { xs: 0.2, sm: 0.4, md: 0.6 },\n                        minHeight: { xs: '80px', sm: '100px', md: '120px' }, // Increased height for better content fit\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                        '&:last-child': { borderRight: 'none' },\n                        position: 'relative'\n                      }}\n                    >\n                      {/* Continuous hour line */}\n                      <Box\n                        sx={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          right: 0,\n                          height: '4px',\n                          bgcolor: theme.palette.primary.main,\n                          zIndex: 0,\n                          borderRadius: '2px',\n                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                          pointerEvents: 'none'\n                        }}\n                      />\n                      {/* Lighter half-hour line */}\n                      <Box\n                        sx={{\n                          position: 'absolute',\n                          top: '50%',\n                          left: 0,\n                          right: 0,\n                          height: '2px',\n                          bgcolor: alpha(theme.palette.divider, 0.3), // More transparent\n                          zIndex: 0, // Lower z-index to stay behind bookings\n                          borderRadius: '1px'\n                        }}\n                      />\n\n                      {visibleBookings.length > 0 ? (\n                        <>\n                          {visibleBookings.map((booking, bookingIndex) => {\n                            const bookingColors = getBookingBackgroundColor(booking);\n                            const isFullLesson = parseInt(booking.duration) === 50;\n\n                            // Get booking start time to determine position\n                            let bookingMinute = 0;\n                            let bookingHour = 0;\n                            if (studentProfile && studentProfile.timezone) {\n                              const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n                              const [, timePart] = formattedDateTime.split(' ');\n                              const [hourStr, minuteStr] = timePart.split(':');\n                              bookingHour = parseInt(hourStr);\n                              bookingMinute = parseInt(minuteStr);\n                            } else {\n                              const bookingDate = new Date(booking.datetime);\n                              bookingHour = bookingDate.getHours();\n                              bookingMinute = bookingDate.getMinutes();\n                            }\n\n                            // For full lessons, determine which half of this slot the booking occupies\n                            // For half lessons, show in correct half based on start time\n                            let isFirstHalf = bookingMinute === 0;\n\n                            // For full lessons, determine the visual representation\n                            if (isFullLesson) {\n                              // Calculate if this booking spans into the next hour\n                              const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n                              const bookingEndMinutes = bookingStartMinutes + 50; // 50-minute lesson\n                              const currentSlotStartMinutes = timeSlot.hour * 60;\n                              const currentSlotEndMinutes = currentSlotStartMinutes + 60;\n\n                              // Check if booking starts in this slot\n                              const startsInThisSlot = bookingStartMinutes >= currentSlotStartMinutes && bookingStartMinutes < currentSlotEndMinutes;\n\n                              if (startsInThisSlot) {\n                                // Booking starts in this slot\n                                const extendsToNextHour = bookingEndMinutes > currentSlotEndMinutes;\n                                if (extendsToNextHour) {\n                                  // This is a cross-hour booking, show it spanning from current position to next hour\n                                  isFirstHalf = 'spanning'; // Special case for spanning bookings\n                                } else {\n                                  // Regular full lesson within one hour - don't override first half bookings\n                                  isFirstHalf = bookingMinute === 0 ? null : 'secondHalfFull';\n                                }\n                              }\n                            }\n\n                            return (\n                              <Box\n                                key={`${booking.id}-${bookingIndex}`}\n                                sx={{\n                                  width: '90%',\n                                  height: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      // For cross-hour bookings, extend from second half to next hour\n                                      return '100%'; // Extend to cover both halves visually\n                                    }\n                                    if (isFirstHalf === 'secondHalfFull') {\n                                      // Full lesson starting from second half - only show in second half\n                                      return '40%';\n                                    }\n\n                                    return isFullLesson ? '95%' : '40%';\n                                  })(),\n                                  position: 'absolute',\n                                  top: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      // Start from the second half of current slot\n                                      return '50%';\n                                    }\n                                    if (isFirstHalf === 'secondHalfFull') {\n                                      // Position in second half only\n                                      return '55%';\n                                    }\n\n                                    return isFullLesson ? '2.5%' : (isFirstHalf ? '5%' : '55%');\n                                  })(),\n                                  left: '5%',\n                                  zIndex: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      return 100; // Very high z-index for spanning bookings\n                                    }\n\n                                    return 10 + bookingIndex; // Stacked z-index for multiple bookings\n                                  })(),\n                                  borderRadius: 1,\n                                  background: bookingColors.background,\n                                  display: 'flex',\n                                  flexDirection: 'column',\n                                  alignItems: 'center',\n                                  justifyContent: 'center',\n                                  gap: { xs: 0.2, sm: 0.3, md: 0.4 },\n                                  p: { xs: 0.2, sm: 0.3, md: 0.4 },\n                                  cursor: 'pointer',\n                                  border: bookingColors.border,\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                                  '&:hover': {\n                                    background: bookingColors.hoverBackground,\n                                    transform: 'translateY(-1px)',\n                                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                                  },\n                                  transition: 'all 0.2s ease-in-out',\n                                  overflow: 'hidden'\n                                }}\n                                onClick={() => onViewDetails(booking)}\n                              >\n\n\n                                <Typography\n                                  variant=\"body2\"\n                                  sx={{\n                                    color: 'white',\n                                    fontWeight: 'bold',\n                                    fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.75rem' },\n                                    textAlign: 'center',\n                                    lineHeight: 1.1,\n                                    overflow: 'hidden',\n                                    textOverflow: 'ellipsis',\n                                    display: '-webkit-box',\n                                    WebkitLineClamp: 1,\n                                    WebkitBoxOrient: 'vertical',\n                                    maxWidth: '100%',\n                                    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n                                    mb: { xs: 0.1, sm: 0.2, md: 0.2 }\n                                  }}\n                                >\n                                  {isTeacherView ? booking.student_name : booking.teacher_name}\n                                </Typography>\n\n                                <Box sx={{ display: 'flex', gap: { xs: 0.2, sm: 0.3, md: 0.3 } }}>\n                                  <Button\n                                    size=\"small\"\n                                    variant=\"contained\"\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      onViewDetails(booking);\n                                    }}\n                                    sx={{\n                                      minWidth: 'auto',\n                                      width: { xs: 18, sm: 22, md: 24 },\n                                      height: { xs: 16, sm: 20, md: 22 },\n                                      p: 0,\n                                      bgcolor: 'rgba(255, 255, 255, 0.2)',\n                                      color: 'white',\n                                      borderRadius: 0.5,\n                                      '&:hover': {\n                                        bgcolor: 'rgba(255, 255, 255, 0.3)',\n                                      }\n                                    }}\n                                  >\n                                    <ViewIcon sx={{ fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' } }} />\n                                  </Button>\n                                  {booking.status === 'scheduled' && onCancelBooking && !isTeacherView && (\n                                    <Button\n                                      size=\"small\"\n                                      variant=\"contained\"\n                                      onClick={(e) => {\n                                        e.stopPropagation();\n                                        onCancelBooking(booking);\n                                      }}\n                                      sx={{\n                                        minWidth: 'auto',\n                                        width: { xs: 18, sm: 22, md: 24 },\n                                        height: { xs: 16, sm: 20, md: 22 },\n                                        p: 0,\n                                        bgcolor: 'rgba(244, 67, 54, 0.8)',\n                                        color: 'white',\n                                        borderRadius: 0.5,\n                                        '&:hover': {\n                                          bgcolor: 'rgba(244, 67, 54, 1)',\n                                        }\n                                      }}\n                                    >\n                                      <CancelIcon sx={{ fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' } }} />\n                                    </Button>\n                                  )}\n                                </Box>\n                              </Box>\n                            );\n                          })}\n                        </>\n                      ) : (\n                        // Empty slot - show two halves for 30-minute slots\n                        <Box sx={{\n                          width: '90%',\n                          height: { xs: '65px', sm: '85px', md: '105px' },\n                          borderRadius: 1.5,\n                          border: `1px solid ${alpha(theme.palette.grey[300], 0.5)}`,\n                          display: 'flex',\n                          flexDirection: 'column',\n                          overflow: 'hidden',\n                          zIndex: 1\n                        }}>\n                          {/* First Half (00-30 minutes) */}\n                          {(() => {\n                            const firstHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, true);\n                            const firstHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, true);\n\n                            return (\n                              <Box sx={{\n                                flex: 1,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                borderBottom: `1px solid ${alpha(theme.palette.grey[300], 0.3)}`,\n                                bgcolor: firstHalfInPast\n                                  ? alpha(theme.palette.grey[600], 0.15)\n                                  : firstHalfAvailable\n                                    ? alpha(theme.palette.success.main, 0.08)\n                                    : alpha(theme.palette.grey[100], 0.3),\n                                transition: 'all 0.2s ease'\n                              }}>\n                                {firstHalfInPast ? (\n                                  <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.pastSlot', 'Past Time')}`} arrow>\n                                    <RadioButtonUncheckedIcon\n                                      sx={{\n                                        color: theme.palette.grey[600],\n                                        fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                      }}\n                                    />\n                                  </Tooltip>\n                                ) : firstHalfAvailable ? (\n                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                                    <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.availableSlot', 'Available')}`} arrow>\n                                      <CheckCircleIcon\n                                        sx={{\n                                          color: theme.palette.success.main,\n                                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },\n                                          cursor: 'pointer'\n                                        }}\n                                      />\n                                    </Tooltip>\n                                    {onTakeBreak && (\n                                      <Tooltip title={t('bookings.takeBreak', 'Take Break')} arrow>\n                                        <IconButton\n                                          size=\"small\"\n                                          onClick={() => handleTakeBreak(day.key, timeSlot, true)}\n                                          sx={{\n                                            color: theme.palette.warning.main,\n                                            fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },\n                                            padding: '2px',\n                                            '&:hover': {\n                                              bgcolor: alpha(theme.palette.warning.main, 0.1)\n                                            }\n                                          }}\n                                        >\n                                          <RestIcon sx={{ fontSize: 'inherit' }} />\n                                        </IconButton>\n                                      </Tooltip>\n                                    )}\n                                  </Box>\n                                ) : (\n                                  <RadioButtonUncheckedIcon\n                                    sx={{\n                                      color: alpha(theme.palette.grey[400], 0.5),\n                                      fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                    }}\n                                  />\n                                )}\n                              </Box>\n                            );\n                          })()}\n\n                          {/* Second Half (30-60 minutes) */}\n                          {(() => {\n                            const secondHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, false);\n                            const secondHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, false);\n\n                            return (\n                              <Box sx={{\n                                flex: 1,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                bgcolor: secondHalfInPast\n                                  ? alpha(theme.palette.grey[600], 0.15)\n                                  : secondHalfAvailable\n                                    ? alpha(theme.palette.success.main, 0.08)\n                                    : alpha(theme.palette.grey[100], 0.3),\n                                transition: 'all 0.2s ease'\n                              }}>\n                                {secondHalfInPast ? (\n                                  <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.pastSlot', 'Past Time')}`} arrow>\n                                    <RadioButtonUncheckedIcon\n                                      sx={{\n                                        color: theme.palette.grey[600],\n                                        fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                      }}\n                                    />\n                                  </Tooltip>\n                                ) : secondHalfAvailable ? (\n                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                                    <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.availableSlot', 'Available')}`} arrow>\n                                      <CheckCircleIcon\n                                        sx={{\n                                          color: theme.palette.success.main,\n                                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },\n                                          cursor: 'pointer'\n                                        }}\n                                      />\n                                    </Tooltip>\n                                    {onTakeBreak && (\n                                      <Tooltip title={t('bookings.takeBreak', 'Take Break')} arrow>\n                                        <IconButton\n                                          size=\"small\"\n                                          onClick={() => handleTakeBreak(day.key, timeSlot, false)}\n                                          sx={{\n                                            color: theme.palette.warning.main,\n                                            fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },\n                                            padding: '2px',\n                                            '&:hover': {\n                                              bgcolor: alpha(theme.palette.warning.main, 0.1)\n                                            }\n                                          }}\n                                        >\n                                          <RestIcon sx={{ fontSize: 'inherit' }} />\n                                        </IconButton>\n                                      </Tooltip>\n                                    )}\n                                  </Box>\n                                ) : (\n                                  <RadioButtonUncheckedIcon\n                                    sx={{\n                                      color: alpha(theme.palette.grey[400], 0.5),\n                                      fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                    }}\n                                  />\n                                )}\n                              </Box>\n                            );\n                          })()}\n                        </Box>\n                      )}\n                    </Box>\n                  );\n                })}\n              </Box>\n            );\n          })}\n        </Box>\n      </Box>\n\n      {/* Take Break Dialog */}\n      <Dialog\n        open={breakDialogOpen}\n        onClose={() => setBreakDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>\n          <RestIcon sx={{ fontSize: '2rem', color: 'warning.main', mb: 1 }} />\n          <Typography variant=\"h6\" component=\"div\">\n            {t('bookings.takeBreakTitle', 'أخذ هذا الوقت راحة')}\n          </Typography>\n        </DialogTitle>\n\n        <DialogContent sx={{ textAlign: 'center', py: 3 }}>\n          {selectedBreakSlot && (\n            <Box>\n              <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                {t('bookings.takeBreakMessage', 'هل تريد أخذ هذا الوقت راحة؟')}\n              </Typography>\n\n              <Box sx={{\n                bgcolor: alpha(theme.palette.warning.main, 0.1),\n                p: 2,\n                borderRadius: 2,\n                border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`\n              }}>\n                <Typography variant=\"h6\" sx={{ color: 'warning.main', mb: 1 }}>\n                  📅 {format(selectedBreakSlot.date, 'EEEE, MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                </Typography>\n                <Typography variant=\"h5\" sx={{ fontWeight: 'bold' }}>\n                  🕐 {(() => {\n                    const startHour = selectedBreakSlot.hour;\n                    const startMinute = selectedBreakSlot.minute;\n                    const endMinute = startMinute + 30;\n                    const endHour = endMinute >= 60 ? startHour + 1 : startHour;\n                    const finalEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;\n\n                    return `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${finalEndMinute.toString().padStart(2, '0')}`;\n                  })()}\n                </Typography>\n              </Box>\n\n              <Typography variant=\"body2\" sx={{ mt: 2, color: 'text.secondary' }}>\n                {t('bookings.takeBreakNote', 'سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط')}\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n\n        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>\n          <Button\n            onClick={() => setBreakDialogOpen(false)}\n            variant=\"outlined\"\n            sx={{ minWidth: 100 }}\n          >\n            {t('common.cancel', 'إلغاء')}\n          </Button>\n          <Button\n            onClick={confirmTakeBreak}\n            variant=\"contained\"\n            color=\"warning\"\n            sx={{ minWidth: 100, ml: 2 }}\n          >\n            {t('common.confirm', 'موافق')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Paper>\n  );\n};\n\nexport default WeeklyBookingsTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,eAAe;AAC9C,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,UAAU,QACL,eAAe;AACtB,SACEC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,oBAAoB,IAAIC,wBAAwB,EAChDC,WAAW,IAAIC,eAAe,EAC9BC,eAAe,IAAIC,QAAQ,QACtB,qBAAqB;AAC5B,SAASC,MAAM,EAAEC,OAAO,QAAQ,UAAU;AAC1C,SAASC,EAAE,EAAEC,IAAI,QAAQ,iBAAiB;AAC1C,OAAOC,MAAM,MAAM,iBAAiB;AACpC,SAASC,2BAA2B,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhE,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,QAAQ;EACRC,OAAO,GAAG,KAAK;EACfC,gBAAgB;EAChBC,UAAU;EACVC,aAAa;EACbC,eAAe;EACfC,cAAc;EACdC,iBAAiB;EACjBC,cAAc;EACdC,aAAa,GAAG,KAAK;EACrBC,cAAc,GAAG,IAAI;EACrBC,WAAW,GAAG,IAAI;EAClBC,YAAY,GAAG;AACjB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGrD,cAAc,CAAC,CAAC;EACpC,MAAMsD,KAAK,GAAGhD,QAAQ,CAAC,CAAC;EACxB,MAAMiD,KAAK,GAAGF,IAAI,CAACG,QAAQ,KAAK,IAAI;;EAEpC;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,KAAK,CAAC4D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9D,KAAK,CAAC4D,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAMG,eAAe,GAAGA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,WAAW,KAAK;IACtD,MAAMC,QAAQ,GAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKP,GAAG,CAAC;IAC7D,MAAMQ,IAAI,GAAG3C,OAAO,CAACY,gBAAgB,EAAE0B,QAAQ,CAAC;IAChD,MAAMM,UAAU,GAAGP,WAAW,GAAG,CAAC,GAAG,EAAE;IAEvCJ,oBAAoB,CAAC;MACnBE,GAAG;MACHQ,IAAI;MACJE,IAAI,EAAET,QAAQ,CAACS,IAAI;MACnBC,MAAM,EAAEF,UAAU;MAClBP,WAAW;MACXD;IACF,CAAC,CAAC;IACFN,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIf,iBAAiB,IAAIX,WAAW,EAAE;MACpCA,WAAW,CAACW,iBAAiB,CAAC;IAChC;IACAF,kBAAkB,CAAC,KAAK,CAAC;IACzBG,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMe,oBAAoB,GAAGA,CAACb,GAAG,EAAEC,QAAQ,EAAEC,WAAW,KAAK;IAC3D,IAAI;MACF,MAAMC,QAAQ,GAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKP,GAAG,CAAC;MAC7D,MAAMQ,IAAI,GAAG3C,OAAO,CAACY,gBAAgB,EAAE0B,QAAQ,CAAC;MAEhD,MAAMM,UAAU,GAAGP,WAAW,GAAG,CAAC,GAAG,EAAE;;MAEvC;MACA,MAAMY,aAAa,GAAG,IAAIC,IAAI,CAACP,IAAI,CAAC;MACpCM,aAAa,CAACE,QAAQ,CAACf,QAAQ,CAACS,IAAI,EAAED,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEvD;MACA,IAAIQ,WAAW;MACf,IAAIpC,cAAc,IAAIA,cAAc,CAACqC,QAAQ,EAAE;QAC7C;QACA,MAAMC,cAAc,GAAGlD,2BAA2B,CAAC,IAAI8C,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,EAAEvC,cAAc,CAACqC,QAAQ,EAAE,qBAAqB,CAAC;QAC5HD,WAAW,GAAG,IAAIF,IAAI,CAACI,cAAc,CAAC;MACxC,CAAC,MAAM;QACL;QACAF,WAAW,GAAG,IAAIF,IAAI,CAAC,CAAC;MAC1B;;MAEA;MACA;MACA,OAAOD,aAAa,IAAIG,WAAW;IAErC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAACvB,GAAG,EAAEC,QAAQ,EAAEC,WAAW,KAAK;IAC9D,IAAI,CAACjB,cAAc,IAAI,CAACD,aAAa,EAAE,OAAO,KAAK;IAEnD,MAAMwC,MAAM,GAAGxB,GAAG,CAACyB,WAAW,CAAC,CAAC;IAChC,MAAMC,QAAQ,GAAGzC,cAAc,CAACuC,MAAM,CAAC;IAEvC,IAAI,CAACE,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE,OAAO,KAAK;;IAEvD;IACA,MAAMvB,QAAQ,GAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKP,GAAG,CAAC;IAC7D,MAAMQ,IAAI,GAAG3C,OAAO,CAACY,gBAAgB,EAAE0B,QAAQ,CAAC;IAChD,MAAMM,UAAU,GAAGP,WAAW,GAAG,CAAC,GAAG,EAAE;;IAEvC;IACA;IACA;;IAEA;IACA,MAAM2B,YAAY,GAAG,IAAId,IAAI,CAACP,IAAI,CAAC;IACnCqB,YAAY,CAACb,QAAQ,CAACf,QAAQ,CAACS,IAAI,EAAED,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAEtDa,OAAO,CAACQ,GAAG,CAAC,8CAA8C,CAAC;IAC3DR,OAAO,CAACQ,GAAG,CAAC,iBAAiB,EAAEtB,IAAI,CAACY,WAAW,CAAC,CAAC,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAChET,OAAO,CAACQ,GAAG,CAAC,gBAAgB,EAAE7B,QAAQ,CAACS,IAAI,EAAE,SAAS,EAAED,UAAU,CAAC;IACnEa,OAAO,CAACQ,GAAG,CAAC,qBAAqB,EAAED,YAAY,CAACT,WAAW,CAAC,CAAC,CAAC;;IAE9D;IACA,MAAMY,WAAW,GAAG7C,YAAY,CAAC8C,IAAI,CAACC,SAAS,IAAI;MACjD,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA,OAAO,KAAK;MACd;MAEA,IAAIA,SAAS,IAAIA,SAAS,CAACC,QAAQ,EAAE;QACnC;QACA,MAAMC,gBAAgB,GAAG,IAAIrB,IAAI,CAACmB,SAAS,CAACC,QAAQ,CAAC;;QAErD;QACA,IAAItD,cAAc,IAAIA,cAAc,CAACqC,QAAQ,EAAE;UAC7C,MAAMmB,eAAe,GAAGxD,cAAc,CAACqC,QAAQ;UAC/C,MAAMoB,gBAAgB,GAAGrE,2BAA2B,CAACiE,SAAS,CAACC,QAAQ,EAAEE,eAAe,EAAE,qBAAqB,CAAC;UAChH,MAAM,CAACE,SAAS,EAAEC,SAAS,CAAC,GAAGF,gBAAgB,CAACP,KAAK,CAAC,GAAG,CAAC;UAC1D,MAAM,CAACU,SAAS,EAAEC,WAAW,CAAC,GAAGF,SAAS,CAACT,KAAK,CAAC,GAAG,CAAC,CAACY,GAAG,CAACC,MAAM,CAAC;;UAEjE;UACA,MAAMC,WAAW,GAAGrC,IAAI,CAACY,WAAW,CAAC,CAAC,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACpD,MAAMe,WAAW,GAAGP,SAAS,KAAKM,WAAW;UAC7C,MAAME,WAAW,GAAGN,SAAS,KAAKxC,QAAQ,CAACS,IAAI,IAAIgC,WAAW,KAAKjC,UAAU;UAE7Ea,OAAO,CAACQ,GAAG,CAAC,wBAAwB,EAAEI,SAAS,CAACC,QAAQ,CAAC;UACzDb,OAAO,CAACQ,GAAG,CAAC,eAAe,EAAEM,gBAAgB,CAAChB,WAAW,CAAC,CAAC,CAAC;UAC5DE,OAAO,CAACQ,GAAG,CAAC,sBAAsB,EAAEQ,gBAAgB,CAAC;UACrDhB,OAAO,CAACQ,GAAG,CAAC,sBAAsB,EAAEgB,WAAW,EAAE,IAAIP,SAAS,OAAOM,WAAW,GAAG,CAAC;UACpFvB,OAAO,CAACQ,GAAG,CAAC,qBAAqB,EAAEiB,WAAW,EAAE,IAAIN,SAAS,IAAIC,WAAW,OAAOzC,QAAQ,CAACS,IAAI,IAAID,UAAU,GAAG,CAAC;UAElH,OAAOqC,WAAW,IAAIC,WAAW;QACnC;MACF;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;IAEFzB,OAAO,CAACQ,GAAG,CAAC,oBAAoB,EAAEE,WAAW,CAAC;IAE9C,IAAI7C,YAAY,CAAC6D,QAAQ,CAACC,QAAQ,CAAC,EAAE;MACnC3B,OAAO,CAACQ,GAAG,CAAC,kCAAkC,CAAC;MAC/C,OAAO,KAAK,CAAC,CAAC;IAChB;;IAEA;IACA,MAAMoB,gBAAgB,GAAGjD,QAAQ,CAACS,IAAI,GAAG,EAAE,GAAGD,UAAU;IACxD,MAAM0C,cAAc,GAAGD,gBAAgB,GAAG,EAAE;;IAE5C;IACA,IAAIjD,QAAQ,CAACS,IAAI,KAAK,EAAE,IAAI,CAACR,WAAW,EAAE;MACxC;MACA,OAAOwB,QAAQ,CAACO,IAAI,CAACmB,IAAI,IAAI;QAC3B,MAAM,CAACC,SAAS,EAAEC,OAAO,CAAC,GAAGF,IAAI,CAACrB,KAAK,CAAC,GAAG,CAAC;QAC5C,MAAM,CAACwB,SAAS,EAAEC,WAAW,CAAC,GAAGH,SAAS,CAACtB,KAAK,CAAC,GAAG,CAAC,CAACY,GAAG,CAACC,MAAM,CAAC;QACjE,IAAI,CAACa,OAAO,EAAEC,SAAS,CAAC,GAAGJ,OAAO,CAACvB,KAAK,CAAC,GAAG,CAAC,CAACY,GAAG,CAACC,MAAM,CAAC;;QAEzD;QACA,IAAIa,OAAO,KAAK,CAAC,IAAIA,OAAO,KAAK,EAAE,EAAE;UACnCA,OAAO,GAAG,EAAE;UACZC,SAAS,GAAG,CAAC;QACf;QAEA,MAAMC,qBAAqB,GAAGJ,SAAS,GAAG,EAAE,GAAGC,WAAW;QAC1D,MAAMI,mBAAmB,GAAGH,OAAO,GAAG,EAAE,GAAGC,SAAS;;QAEpD;QACA,OAAOR,gBAAgB,IAAIS,qBAAqB,IACzCT,gBAAgB,GAAGU,mBAAmB;MAC/C,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOlC,QAAQ,CAACO,IAAI,CAACmB,IAAI,IAAI;MAC3B,MAAM,CAACC,SAAS,EAAEC,OAAO,CAAC,GAAGF,IAAI,CAACrB,KAAK,CAAC,GAAG,CAAC;MAC5C,MAAM,CAACwB,SAAS,EAAEC,WAAW,CAAC,GAAGH,SAAS,CAACtB,KAAK,CAAC,GAAG,CAAC,CAACY,GAAG,CAACC,MAAM,CAAC;MACjE,IAAI,CAACa,OAAO,EAAEC,SAAS,CAAC,GAAGJ,OAAO,CAACvB,KAAK,CAAC,GAAG,CAAC,CAACY,GAAG,CAACC,MAAM,CAAC;;MAEzD;MACA,IAAIa,OAAO,KAAK,CAAC,EAAE;QACjBA,OAAO,GAAG,EAAE;QACZC,SAAS,GAAG,CAAC;MACf;MAEA,MAAMC,qBAAqB,GAAGJ,SAAS,GAAG,EAAE,GAAGC,WAAW;MAC1D,MAAMI,mBAAmB,GAAGH,OAAO,GAAG,EAAE,GAAGC,SAAS;;MAEpD;MACA,OAAOR,gBAAgB,IAAIS,qBAAqB,IAAIR,cAAc,IAAIS,mBAAmB;IAC3F,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,EAAE;EACpB,KAAK,IAAInD,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,EAAE,EAAEA,IAAI,EAAE,EAAE;IACpC,MAAM2C,SAAS,GAAG,GAAG3C,IAAI,CAACoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;IAC1D,MAAMC,OAAO,GAAG,GAAGtD,IAAI,CAACoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;IACxD,MAAMT,OAAO,GAAG5C,IAAI,GAAG,EAAE,GAAG,GAAG,CAACA,IAAI,GAAG,CAAC,EAAEoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;IACpFF,SAAS,CAACI,IAAI,CAAC;MACb1D,GAAG,EAAE,GAAG8C,SAAS,IAAIC,OAAO,EAAE;MAC9BY,KAAK,EAAEb,SAAS;MAChBc,QAAQ,EAAEH,OAAO;MACjBtD,IAAI;MACJC,MAAM,EAAE,CAAC;MACT;MACAyD,SAAS,EAAE,GAAGf,SAAS,IAAIW,OAAO,EAAE;MACpCK,UAAU,EAAE3D,IAAI,GAAG,EAAE,GAAG,GAAGsD,OAAO,IAAIV,OAAO,EAAE,GAAG;IACpD,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMgB,iBAAiB,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;EACxG,MAAMlE,cAAc,GAAG1B,UAAU,GAAGA,UAAU,CAACiE,GAAG,CAAC3C,GAAG,KAAK;IACzDO,GAAG,EAAEP,GAAG;IACRkE,KAAK,EAAE7E,CAAC,CAAC,QAAQW,GAAG,EAAE;EACxB,CAAC,CAAC,CAAC,GAAGsE,iBAAiB,CAAC3B,GAAG,CAAC3C,GAAG,KAAK;IAClCO,GAAG,EAAEP,GAAG;IACRkE,KAAK,EAAE7E,CAAC,CAAC,QAAQW,GAAG,EAAE;EACxB,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMuE,qBAAqB,GAAI/C,MAAM,IAAK;IACxC,MAAMgD,aAAa,GAAG;MACpBC,MAAM,EAAEpF,CAAC,CAAC,kBAAkB,CAAC,IAAI,KAAK;MACtCqF,MAAM,EAAErF,CAAC,CAAC,kBAAkB,CAAC,IAAI,KAAK;MACtCsF,OAAO,EAAEtF,CAAC,CAAC,mBAAmB,CAAC,IAAI,KAAK;MACxCuF,SAAS,EAAEvF,CAAC,CAAC,qBAAqB,CAAC,IAAI,KAAK;MAC5CwF,QAAQ,EAAExF,CAAC,CAAC,oBAAoB,CAAC,IAAI,KAAK;MAC1CyF,MAAM,EAAEzF,CAAC,CAAC,kBAAkB,CAAC,IAAI,KAAK;MACtC0F,QAAQ,EAAE1F,CAAC,CAAC,oBAAoB,CAAC,IAAI;IACvC,CAAC;IACD,OAAOmF,aAAa,CAAChD,MAAM,CAAC,IAAIA,MAAM,CAACwD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC,MAAMC,gBAAgB,GAAG,IAAIpE,IAAI,CAACmE,OAAO,CAAC/C,QAAQ,CAAC;IACnD,MAAMiD,cAAc,GAAG,IAAIrE,IAAI,CAACmE,OAAO,CAAC/C,QAAQ,CAAC;IACjDiD,cAAc,CAACC,UAAU,CAACD,cAAc,CAACE,UAAU,CAAC,CAAC,GAAGC,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,CAAC;IACnF,MAAMC,GAAG,GAAG,IAAI1E,IAAI,CAAC,CAAC;IAEtB,IAAImE,OAAO,CAACQ,MAAM,KAAK,WAAW,EAAE;MAClC,OAAO,WAAW;IACpB;IAEA,IAAID,GAAG,IAAIN,gBAAgB,IAAIM,GAAG,GAAGL,cAAc,EAAE;MACnD,OAAO,SAAS;IAClB;IAEA,IAAIK,GAAG,IAAIL,cAAc,EAAE;MACzB,OAAO,WAAW;IACpB;IAEA,OAAO,WAAW;EACpB,CAAC;;EAED;EACA,MAAMO,yBAAyB,GAAIT,OAAO,IAAK;IAC7C,MAAMQ,MAAM,GAAGT,gBAAgB,CAACC,OAAO,CAAC;IAExC,QAAQQ,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO;UACLE,UAAU,EAAE,2BAA2BpJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC,QAAQvJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACE,IAAI,EAAE,IAAI,CAAC,QAAQ;UACrIC,MAAM,EAAE,aAAazJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC,EAAE;UAC7DG,eAAe,EAAE,2BAA2B1J,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC,QAAQvJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACE,IAAI,EAAE,CAAC,CAAC;QACjI,CAAC;MACH,KAAK,WAAW;QACd,OAAO;UACLJ,UAAU,EAAE,2BAA2BpJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ3J,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ;UAC/HF,MAAM,EAAE,aAAazJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE;UAC1DD,eAAe,EAAE,2BAA2B1J,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ3J,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC3H,CAAC;MACH,KAAK,WAAW;QACd,OAAO;UACLP,UAAU,EAAE,2BAA2BpJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACxE,KAAK,CAAC0E,IAAI,EAAE,IAAI,CAAC,QAAQvJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACxE,KAAK,CAAC2E,IAAI,EAAE,IAAI,CAAC,QAAQ;UACjIC,MAAM,EAAE,aAAazJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACxE,KAAK,CAAC0E,IAAI,EAAE,GAAG,CAAC,EAAE;UAC3DG,eAAe,EAAE,2BAA2B1J,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACxE,KAAK,CAAC0E,IAAI,EAAE,IAAI,CAAC,QAAQvJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACxE,KAAK,CAAC2E,IAAI,EAAE,CAAC,CAAC;QAC7H,CAAC;MACH;QAAS;QACP,OAAO;UACLJ,UAAU,EAAE,2BAA2BpJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACO,IAAI,CAACL,IAAI,EAAE,IAAI,CAAC,QAAQvJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACO,IAAI,CAACJ,IAAI,EAAE,IAAI,CAAC,QAAQ;UAC/HC,MAAM,EAAE,aAAazJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACO,IAAI,CAACL,IAAI,EAAE,GAAG,CAAC,EAAE;UAC1DG,eAAe,EAAE,2BAA2B1J,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACO,IAAI,CAACL,IAAI,EAAE,IAAI,CAAC,QAAQvJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACO,IAAI,CAACJ,IAAI,EAAE,CAAC,CAAC;QAC3H,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMK,mBAAmB,GAAGA,CAACrG,GAAG,EAAEC,QAAQ,KAAK;IAC7C,IAAI,CAAC1B,QAAQ,IAAI,CAACE,gBAAgB,EAAE,OAAO,EAAE;IAE7C,MAAM0B,QAAQ,GAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKP,GAAG,CAAC;IAC7D,MAAMQ,IAAI,GAAG3C,OAAO,CAACY,gBAAgB,EAAE0B,QAAQ,CAAC;IAChD,MAAMmG,OAAO,GAAG1I,MAAM,CAAC4C,IAAI,EAAE,YAAY,CAAC;IAE1C,OAAOjC,QAAQ,CAACgI,MAAM,CAACrB,OAAO,IAAI;MAChC;MACA,IAAIsB,WAAW,EAAEC,WAAW,EAAEC,aAAa;MAE3C,IAAI7H,cAAc,IAAIA,cAAc,CAACqC,QAAQ,EAAE;QAC7C;QACA,MAAMyF,iBAAiB,GAAG1I,2BAA2B,CAACiH,OAAO,CAAC/C,QAAQ,EAAEtD,cAAc,CAACqC,QAAQ,EAAE,qBAAqB,CAAC;QACvH,MAAM,CAAC0F,QAAQ,EAAEC,QAAQ,CAAC,GAAGF,iBAAiB,CAAC5E,KAAK,CAAC,GAAG,CAAC;QACzDyE,WAAW,GAAGI,QAAQ;QAEtB,MAAM,CAACE,OAAO,EAAEC,SAAS,CAAC,GAAGF,QAAQ,CAAC9E,KAAK,CAAC,GAAG,CAAC;QAChD0E,WAAW,GAAGlB,QAAQ,CAACuB,OAAO,CAAC;QAC/BJ,aAAa,GAAGnB,QAAQ,CAACwB,SAAS,CAAC;MACrC,CAAC,MAAM;QACL;QACA,MAAMC,eAAe,GAAG,IAAIjG,IAAI,CAACmE,OAAO,CAAC/C,QAAQ,CAAC;QAClDqE,WAAW,GAAG5I,MAAM,CAACoJ,eAAe,EAAE,YAAY,CAAC;QACnDP,WAAW,GAAGO,eAAe,CAACC,QAAQ,CAAC,CAAC;QACxCP,aAAa,GAAGM,eAAe,CAAC1B,UAAU,CAAC,CAAC;MAC9C;;MAEA;MACA,MAAMxC,WAAW,GAAG0D,WAAW,KAAKF,OAAO;MAC3C,IAAI,CAACxD,WAAW,EAAE,OAAO,KAAK;;MAE9B;MACA,MAAM0C,QAAQ,GAAGD,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,IAAI,EAAE;MACjD,MAAM0B,mBAAmB,GAAGT,WAAW,GAAG,EAAE,GAAGC,aAAa;MAC5D,MAAMS,iBAAiB,GAAGD,mBAAmB,GAAG1B,QAAQ;;MAExD;MACA,MAAMtC,gBAAgB,GAAGjD,QAAQ,CAACS,IAAI,GAAG,EAAE;MAC3C,MAAMyC,cAAc,GAAGD,gBAAgB,GAAG,EAAE;;MAE5C;MACA,IAAIsC,QAAQ,KAAK,EAAE,EAAE;QACnB;QACA,MAAM4B,gBAAgB,GAAGF,mBAAmB,IAAIhE,gBAAgB,IAAIgE,mBAAmB,GAAG/D,cAAc;;QAExG;QACA,MAAMkE,mBAAmB,GAAGH,mBAAmB,GAAGhE,gBAAgB,IAAIiE,iBAAiB,GAAGjE,gBAAgB;QAE1G,OAAOkE,gBAAgB,IAAIC,mBAAmB;MAChD,CAAC,MAAM;QACL;QACA,MAAMC,UAAU,GAAGpE,gBAAgB,GAAG,EAAE;;QAExC;QACA,IAAIgE,mBAAmB,IAAIhE,gBAAgB,IAAIgE,mBAAmB,GAAGI,UAAU,EAAE;UAC/E;UACA,OAAOJ,mBAAmB,IAAIhE,gBAAgB,IAAIgE,mBAAmB,GAAGI,UAAU;QACpF,CAAC,MAAM,IAAIJ,mBAAmB,IAAII,UAAU,IAAIJ,mBAAmB,GAAG/D,cAAc,EAAE;UACpF;UACA,OAAO+D,mBAAmB,IAAII,UAAU,IAAIJ,mBAAmB,GAAG/D,cAAc;QAClF;MACF;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ,CAAC;EAED,IAAI3E,OAAO,EAAE;IACX,oBACEL,OAAA,CAACjC,GAAG;MAACqL,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5DxJ,OAAA,CAAC1B,gBAAgB;QAAAmL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,IAAI,CAACxJ,QAAQ,IAAIA,QAAQ,CAACyJ,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE7J,OAAA,CAAC/B,KAAK;MAAC6L,SAAS,EAAE,CAAE;MAACV,EAAE,EAAE;QAAEW,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAV,QAAA,eAC7ExJ,OAAA,CAAChC,UAAU;QAACmM,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAZ,QAAA,EAC5CtI,CAAC,CAAC,qBAAqB;MAAC;QAAAuI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEZ;EAEA,oBACE5J,OAAA,CAAC/B,KAAK;IAAC6L,SAAS,EAAE,CAAE;IAACV,EAAE,EAAE;MAAEY,EAAE,EAAE,CAAC;MAAEC,YAAY,EAAE;IAAE,CAAE;IAAAT,QAAA,gBAElDxJ,OAAA,CAACjC,GAAG;MAACqL,EAAE,EAAE;QACP3B,UAAU,EAAE,2BAA2BrG,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,QAAQxG,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACxC,IAAI,QAAQ;QAC3GkC,CAAC,EAAE,CAAC;QACJK,KAAK,EAAE,OAAO;QACdf,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BgB,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,MAAM;QAChBC,GAAG,EAAE;MACP,CAAE;MAAAhB,QAAA,eACAxJ,OAAA,CAACjC,GAAG;QAAAyL,QAAA,gBACFxJ,OAAA,CAAChC,UAAU;UAACmM,OAAO,EAAC,IAAI;UAACf,EAAE,EAAE;YAAEqB,UAAU,EAAE,MAAM;YAAET,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,GAAC,eACvD,EAACtI,CAAC,CAAC,sBAAsB,CAAC;QAAA;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACb5J,OAAA,CAAChC,UAAU;UAACmM,OAAO,EAAC,OAAO;UAACf,EAAE,EAAE;YAAEsB,OAAO,EAAE;UAAI,CAAE;UAAAlB,QAAA,EAC9CtI,CAAC,CAAC,4BAA4B;QAAC;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5J,OAAA,CAACjC,GAAG;MAACqL,EAAE,EAAE;QAAEuB,QAAQ,EAAE;MAAO,CAAE;MAAAnB,QAAA,gBAE5BxJ,OAAA,CAACjC,GAAG;QAACqL,EAAE,EAAE;UACPC,OAAO,EAAE,MAAM;UACfuB,mBAAmB,EAAE;YACnBC,EAAE,EAAE,mCAAmC;YACvCC,EAAE,EAAE,oCAAoC;YACxCC,EAAE,EAAE;UACN,CAAC;UACDC,OAAO,EAAE3M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,IAAI,CAAC;UAChDqD,YAAY,EAAE,aAAa5M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,GAAG,CAAC,EAAE;UACnEsD,QAAQ,EAAE,QAAQ;UAClBC,GAAG,EAAE,CAAC;UACNC,MAAM,EAAE;QACV,CAAE;QAAA5B,QAAA,gBACAxJ,OAAA,CAACjC,GAAG;UAACqL,EAAE,EAAE;YACPW,CAAC,EAAE;cAAEc,EAAE,EAAE,GAAG;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAI,CAAC;YAC9BM,SAAS,EAAE;cAAER,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAC;YACjD1B,OAAO,EAAE,MAAM;YACfiB,UAAU,EAAE,QAAQ;YACpBhB,cAAc,EAAE,QAAQ;YACxBgC,WAAW,EAAE,aAAajN,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,GAAG,CAAC;UAClE,CAAE;UAAA4B,QAAA,eACAxJ,OAAA,CAAChC,UAAU;YAACmM,OAAO,EAAC,OAAO;YAACf,EAAE,EAAE;cAC9BqB,UAAU,EAAE,MAAM;cAClBL,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAAC6D,IAAI,CAACC,SAAS;cACnCC,QAAQ,EAAE;gBAAEZ,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAO;YACrD,CAAE;YAAAvB,QAAA,GAAC,SACC,EAACtI,CAAC,CAAC,cAAc,CAAC;UAAA;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EACL3H,cAAc,CAACuC,GAAG,CAAC,CAAC3C,GAAG,EAAE6J,KAAK,KAAK;UAClC;UACA,MAAMC,OAAO,GAAGrL,gBAAgB,GAAGZ,OAAO,CAACY,gBAAgB,EAAEoL,KAAK,CAAC,GAAG,IAAI9I,IAAI,CAAC,CAAC;UAEhF,oBACE5C,OAAA,CAACjC,GAAG;YAEFqL,EAAE,EAAE;cACFW,CAAC,EAAE;gBAAEc,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAI,CAAC;cAC9BM,SAAS,EAAE;gBAAER,EAAE,EAAE,MAAM;gBAAEC,EAAE,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAO,CAAC;cACjDb,SAAS,EAAE,QAAQ;cACnBoB,WAAW,EAAE,aAAajN,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,GAAG,CAAC,EAAE;cAClE,cAAc,EAAE;gBAAE0D,WAAW,EAAE;cAAO,CAAC;cACvCjC,OAAO,EAAE,MAAM;cACfuC,aAAa,EAAE,QAAQ;cACvBtC,cAAc,EAAE,QAAQ;cACxBgB,UAAU,EAAE;YACd,CAAE;YAAAd,QAAA,gBAEFxJ,OAAA,CAAChC,UAAU;cAACmM,OAAO,EAAC,IAAI;cAACf,EAAE,EAAE;gBAC3BqB,UAAU,EAAE,MAAM;gBAClBL,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI;gBACjC6D,QAAQ,EAAE;kBAAEZ,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAS,CAAC;gBACpDc,UAAU,EAAE;cACd,CAAE;cAAArC,QAAA,gBAEAxJ,OAAA,CAACjC,GAAG;gBAAC+N,SAAS,EAAC,MAAM;gBAAC1C,EAAE,EAAE;kBAAEC,OAAO,EAAE;oBAAEwB,EAAE,EAAE,MAAM;oBAAEE,EAAE,EAAE;kBAAQ;gBAAE,CAAE;gBAAAvB,QAAA,EAChE3H,GAAG,CAACkE;cAAK;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACN5J,OAAA,CAACjC,GAAG;gBAAC+N,SAAS,EAAC,MAAM;gBAAC1C,EAAE,EAAE;kBAAEC,OAAO,EAAE;oBAAEwB,EAAE,EAAE,MAAM;oBAAEC,EAAE,EAAE,OAAO;oBAAEC,EAAE,EAAE;kBAAO;gBAAE,CAAE;gBAAAvB,QAAA,EAC5E3H,GAAG,CAACkE,KAAK,CAAC8D,MAAM,GAAG,CAAC,GAAGhI,GAAG,CAACkE,KAAK,CAACc,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGhF,GAAG,CAACkE;cAAK;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACN5J,OAAA,CAACjC,GAAG;gBAAC+N,SAAS,EAAC,MAAM;gBAAC1C,EAAE,EAAE;kBAAEC,OAAO,EAAE;oBAAEwB,EAAE,EAAE,OAAO;oBAAEC,EAAE,EAAE;kBAAO;gBAAE,CAAE;gBAAAtB,QAAA,EAChEpD,qBAAqB,CAACvE,GAAG,CAACO,GAAG;cAAC;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGb5J,OAAA,CAAChC,UAAU;cAACmM,OAAO,EAAC,SAAS;cAACf,EAAE,EAAE;gBAChCC,OAAO,EAAE,OAAO;gBAChBe,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAAC6D,IAAI,CAACC,SAAS;gBACnCC,QAAQ,EAAE;kBAAEZ,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAS,CAAC;gBACtDgB,EAAE,EAAE;cACN,CAAE;cAAAvC,QAAA,EACC/J,MAAM,CAACkM,OAAO,EAAE,OAAO,EAAE;gBAAEK,MAAM,EAAE3K,KAAK,GAAG1B,EAAE,GAAGC;cAAK,CAAC;YAAC;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA,GAvCR/H,GAAG,CAACO,GAAG;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwCT,CAAC;QAEV,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5J,OAAA,CAACjC,GAAG;QAAAyL,QAAA,EACD9D,SAAS,CAAClB,GAAG,CAAC,CAAC1C,QAAQ,EAAE4J,KAAK,KAAK;UAClC,MAAMO,WAAW,GAAGnK,QAAQ,CAACU,MAAM,KAAK,CAAC;UACzC,oBACExC,OAAA,CAACjC,GAAG;YAEFqL,EAAE,EAAE;cACFC,OAAO,EAAE,MAAM;cACfuB,mBAAmB,EAAE;gBACnBC,EAAE,EAAE,mCAAmC;gBACvCC,EAAE,EAAE,oCAAoC;gBACxCC,EAAE,EAAE;cACN,CAAC;cACDE,YAAY,EAAE,aAAa5M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACwE,OAAO,EAAED,WAAW,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE;cAClFjB,OAAO,EAAEU,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGrN,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,IAAI,CAAC,GAAG,aAAa;cAChF,SAAS,EAAE;gBACToD,OAAO,EAAE3M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,IAAI;cACjD;YACF,CAAE;YAAA4B,QAAA,gBAGFxJ,OAAA,CAACjC,GAAG;cAACqL,EAAE,EAAE;gBACPW,CAAC,EAAE;kBAAEc,EAAE,EAAE,GAAG;kBAAEC,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAI,CAAC;gBAC9B1B,OAAO,EAAE,MAAM;gBACf8C,UAAU,EAAE,QAAQ;gBACpBb,WAAW,EAAE,aAAajN,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,GAAG,CAAC,EAAE;gBAClEoD,OAAO,EAAEiB,WAAW,GAAG5N,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,IAAI,CAAC,GAAG,aAAa;gBAC9EsD,QAAQ,EAAE;cACZ,CAAE;cAAA1B,QAAA,eACAxJ,OAAA,CAACjC,GAAG;gBAACqL,EAAE,EAAE;kBACPC,OAAO,EAAE,MAAM;kBACfuC,aAAa,EAAE,QAAQ;kBACvBtB,UAAU,EAAE,QAAQ;kBACpBE,GAAG,EAAE,GAAG;kBACRU,QAAQ,EAAE,UAAU;kBACpBkB,IAAI,EAAE,KAAK;kBACXC,SAAS,EAAE,kBAAkB;kBAC7BlB,GAAG,EAAErJ,QAAQ,CAACU,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG;gBACxC,CAAE;gBAAAgH,QAAA,eAEAxJ,OAAA,CAAChC,UAAU;kBACTmM,OAAO,EAAC,OAAO;kBACff,EAAE,EAAE;oBACFqB,UAAU,EAAE,MAAM;oBAClBL,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI;oBACjC6D,QAAQ,EAAE;sBAAEZ,EAAE,EAAE,QAAQ;sBAAEC,EAAE,EAAE,QAAQ;sBAAEC,EAAE,EAAE;oBAAS;kBACvD,CAAE;kBAAAvB,QAAA,EAED1H,QAAQ,CAACiE;gBAAK;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL3H,cAAc,CAACuC,GAAG,CAAE3C,GAAG,IAAK;cAC3B,MAAMyK,iBAAiB,GAAGpE,mBAAmB,CAACrG,GAAG,CAACO,GAAG,EAAEN,QAAQ,CAAC;;cAEhE;cACA;cACR,MAAMyK,cAAc,GAAGD,iBAAiB,CAAClE,MAAM,CAACrB,OAAO,IAAI;gBACjD;gBACA,IAAIuB,WAAW,GAAG,CAAC;gBACnB,IAAIC,aAAa,GAAG,CAAC;gBAErB,IAAI7H,cAAc,IAAIA,cAAc,CAACqC,QAAQ,EAAE;kBAC7C,MAAMyF,iBAAiB,GAAG1I,2BAA2B,CAACiH,OAAO,CAAC/C,QAAQ,EAAEtD,cAAc,CAACqC,QAAQ,EAAE,qBAAqB,CAAC;kBACvH,MAAM,GAAG2F,QAAQ,CAAC,GAAGF,iBAAiB,CAAC5E,KAAK,CAAC,GAAG,CAAC;kBACjD,MAAM,CAAC+E,OAAO,EAAEC,SAAS,CAAC,GAAGF,QAAQ,CAAC9E,KAAK,CAAC,GAAG,CAAC;kBAChD0E,WAAW,GAAGlB,QAAQ,CAACuB,OAAO,CAAC;kBAC/BJ,aAAa,GAAGnB,QAAQ,CAACwB,SAAS,CAAC;gBACrC,CAAC,MAAM;kBACL,MAAMP,WAAW,GAAG,IAAIzF,IAAI,CAACmE,OAAO,CAAC/C,QAAQ,CAAC;kBAC9CsE,WAAW,GAAGD,WAAW,CAACS,QAAQ,CAAC,CAAC;kBACpCP,aAAa,GAAGF,WAAW,CAAClB,UAAU,CAAC,CAAC;gBAC1C;gBAEA,MAAME,QAAQ,GAAGD,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,IAAI,EAAE;gBACjD,MAAM0B,mBAAmB,GAAGT,WAAW,GAAG,EAAE,GAAGC,aAAa;gBAC5D,MAAMiE,uBAAuB,GAAG1K,QAAQ,CAACS,IAAI,GAAG,EAAE;;gBAElD;gBACA;gBACA,IAAI8E,QAAQ,KAAK,EAAE,EAAE;kBACnB,MAAM6B,mBAAmB,GAAGH,mBAAmB,GAAGyD,uBAAuB;kBACzE,OAAO,CAACtD,mBAAmB,CAAC,CAAC;gBAC/B;gBAEA,OAAO,IAAI,CAAC,CAAC;cACf,CAAC,CAAC;;cAEF;cACA;cACA;cACA,MAAMuD,kBAAkB,GAAGF,cAAc,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;gBAC9D,MAAMxK,GAAG,GAAGwK,IAAI,CAAC5I,QAAQ,CAAC,CAAC;gBAC3B,MAAM6I,QAAQ,GAAGF,GAAG,CAACvK,GAAG,CAAC;gBAEzB,IAAI,CAACyK,QAAQ,EAAE;kBACbF,GAAG,CAACvK,GAAG,CAAC,GAAGwK,IAAI,CAAC,CAAC;gBACnB,CAAC,MAAM;kBACL;kBACA,IAAIC,QAAQ,CAACtF,MAAM,KAAK,WAAW,IAAIqF,IAAI,CAACrF,MAAM,KAAK,WAAW,EAAE;oBAClEoF,GAAG,CAACvK,GAAG,CAAC,GAAGwK,IAAI;kBACjB;kBACA;kBACA;gBACF;gBACA,OAAOD,GAAG;cACZ,CAAC,EAAE,CAAC,CAAC,CAAC;cAEN,MAAMG,eAAe,GAAGC,MAAM,CAACC,MAAM,CAACP,kBAAkB,CAAC;cAEzD,oBACEzM,OAAA,CAACjC,GAAG;gBAEFqL,EAAE,EAAE;kBACFW,CAAC,EAAE;oBAAEc,EAAE,EAAE,GAAG;oBAAEC,EAAE,EAAE,GAAG;oBAAEC,EAAE,EAAE;kBAAI,CAAC;kBAChCM,SAAS,EAAE;oBAAER,EAAE,EAAE,MAAM;oBAAEC,EAAE,EAAE,OAAO;oBAAEC,EAAE,EAAE;kBAAQ,CAAC;kBAAE;kBACrD1B,OAAO,EAAE,MAAM;kBACfiB,UAAU,EAAE,QAAQ;kBACpBhB,cAAc,EAAE,QAAQ;kBACxBgC,WAAW,EAAE,aAAajN,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI,EAAE,GAAG,CAAC,EAAE;kBAClE,cAAc,EAAE;oBAAE0D,WAAW,EAAE;kBAAO,CAAC;kBACvCJ,QAAQ,EAAE;gBACZ,CAAE;gBAAA1B,QAAA,gBAGFxJ,OAAA,CAACjC,GAAG;kBACFqL,EAAE,EAAE;oBACF8B,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,CAAC;oBACNiB,IAAI,EAAE,CAAC;oBACPa,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,KAAK;oBACblC,OAAO,EAAE5J,KAAK,CAACsG,OAAO,CAAC2C,OAAO,CAACzC,IAAI;oBACnCwD,MAAM,EAAE,CAAC;oBACTnB,YAAY,EAAE,KAAK;oBACnBkD,SAAS,EAAE,2BAA2B;oBACtCC,aAAa,EAAE;kBACjB;gBAAE;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEF5J,OAAA,CAACjC,GAAG;kBACFqL,EAAE,EAAE;oBACF8B,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,KAAK;oBACViB,IAAI,EAAE,CAAC;oBACPa,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,KAAK;oBACblC,OAAO,EAAE3M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACwE,OAAO,EAAE,GAAG,CAAC;oBAAE;oBAC5Cd,MAAM,EAAE,CAAC;oBAAE;oBACXnB,YAAY,EAAE;kBAChB;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAEDkD,eAAe,CAACjD,MAAM,GAAG,CAAC,gBACzB7J,OAAA,CAAAE,SAAA;kBAAAsJ,QAAA,EACGsD,eAAe,CAACtI,GAAG,CAAC,CAACuC,OAAO,EAAEsG,YAAY,KAAK;oBAC9C,MAAMC,aAAa,GAAG9F,yBAAyB,CAACT,OAAO,CAAC;oBACxD,MAAMwG,YAAY,GAAGnG,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,KAAK,EAAE;;oBAEtD;oBACA,IAAIkB,aAAa,GAAG,CAAC;oBACrB,IAAID,WAAW,GAAG,CAAC;oBACnB,IAAI5H,cAAc,IAAIA,cAAc,CAACqC,QAAQ,EAAE;sBAC7C,MAAMyF,iBAAiB,GAAG1I,2BAA2B,CAACiH,OAAO,CAAC/C,QAAQ,EAAEtD,cAAc,CAACqC,QAAQ,EAAE,qBAAqB,CAAC;sBACvH,MAAM,GAAG2F,QAAQ,CAAC,GAAGF,iBAAiB,CAAC5E,KAAK,CAAC,GAAG,CAAC;sBACjD,MAAM,CAAC+E,OAAO,EAAEC,SAAS,CAAC,GAAGF,QAAQ,CAAC9E,KAAK,CAAC,GAAG,CAAC;sBAChD0E,WAAW,GAAGlB,QAAQ,CAACuB,OAAO,CAAC;sBAC/BJ,aAAa,GAAGnB,QAAQ,CAACwB,SAAS,CAAC;oBACrC,CAAC,MAAM;sBACL,MAAMP,WAAW,GAAG,IAAIzF,IAAI,CAACmE,OAAO,CAAC/C,QAAQ,CAAC;sBAC9CsE,WAAW,GAAGD,WAAW,CAACS,QAAQ,CAAC,CAAC;sBACpCP,aAAa,GAAGF,WAAW,CAAClB,UAAU,CAAC,CAAC;oBAC1C;;oBAEA;oBACA;oBACA,IAAIpF,WAAW,GAAGwG,aAAa,KAAK,CAAC;;oBAErC;oBACA,IAAIgF,YAAY,EAAE;sBAChB;sBACA,MAAMxE,mBAAmB,GAAGT,WAAW,GAAG,EAAE,GAAGC,aAAa;sBAC5D,MAAMS,iBAAiB,GAAGD,mBAAmB,GAAG,EAAE,CAAC,CAAC;sBACpD,MAAMyD,uBAAuB,GAAG1K,QAAQ,CAACS,IAAI,GAAG,EAAE;sBAClD,MAAMiL,qBAAqB,GAAGhB,uBAAuB,GAAG,EAAE;;sBAE1D;sBACA,MAAMvD,gBAAgB,GAAGF,mBAAmB,IAAIyD,uBAAuB,IAAIzD,mBAAmB,GAAGyE,qBAAqB;sBAEtH,IAAIvE,gBAAgB,EAAE;wBACpB;wBACA,MAAMwE,iBAAiB,GAAGzE,iBAAiB,GAAGwE,qBAAqB;wBACnE,IAAIC,iBAAiB,EAAE;0BACrB;0BACA1L,WAAW,GAAG,UAAU,CAAC,CAAC;wBAC5B,CAAC,MAAM;0BACL;0BACAA,WAAW,GAAGwG,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,gBAAgB;wBAC7D;sBACF;oBACF;oBAEA,oBACEvI,OAAA,CAACjC,GAAG;sBAEFqL,EAAE,EAAE;wBACFsE,KAAK,EAAE,KAAK;wBACZR,MAAM,EAAE,CAAC,MAAM;0BACb,IAAInL,WAAW,KAAK,UAAU,EAAE;4BAC9B;4BACA,OAAO,MAAM,CAAC,CAAC;0BACjB;0BACA,IAAIA,WAAW,KAAK,gBAAgB,EAAE;4BACpC;4BACA,OAAO,KAAK;0BACd;0BAEA,OAAOwL,YAAY,GAAG,KAAK,GAAG,KAAK;wBACrC,CAAC,EAAE,CAAC;wBACJrC,QAAQ,EAAE,UAAU;wBACpBC,GAAG,EAAE,CAAC,MAAM;0BACV,IAAIpJ,WAAW,KAAK,UAAU,EAAE;4BAC9B;4BACA,OAAO,KAAK;0BACd;0BACA,IAAIA,WAAW,KAAK,gBAAgB,EAAE;4BACpC;4BACA,OAAO,KAAK;0BACd;0BAEA,OAAOwL,YAAY,GAAG,MAAM,GAAIxL,WAAW,GAAG,IAAI,GAAG,KAAM;wBAC7D,CAAC,EAAE,CAAC;wBACJqK,IAAI,EAAE,IAAI;wBACVhB,MAAM,EAAE,CAAC,MAAM;0BACb,IAAIrJ,WAAW,KAAK,UAAU,EAAE;4BAC9B,OAAO,GAAG,CAAC,CAAC;0BACd;0BAEA,OAAO,EAAE,GAAGsL,YAAY,CAAC,CAAC;wBAC5B,CAAC,EAAE,CAAC;wBACJpD,YAAY,EAAE,CAAC;wBACfxC,UAAU,EAAE6F,aAAa,CAAC7F,UAAU;wBACpC4B,OAAO,EAAE,MAAM;wBACfuC,aAAa,EAAE,QAAQ;wBACvBtB,UAAU,EAAE,QAAQ;wBACpBhB,cAAc,EAAE,QAAQ;wBACxBkB,GAAG,EAAE;0BAAEK,EAAE,EAAE,GAAG;0BAAEC,EAAE,EAAE,GAAG;0BAAEC,EAAE,EAAE;wBAAI,CAAC;wBAClChB,CAAC,EAAE;0BAAEc,EAAE,EAAE,GAAG;0BAAEC,EAAE,EAAE,GAAG;0BAAEC,EAAE,EAAE;wBAAI,CAAC;wBAChC4C,MAAM,EAAE,SAAS;wBACjB7F,MAAM,EAAEwF,aAAa,CAACxF,MAAM;wBAC5BqF,SAAS,EAAE,2BAA2B;wBACtC,SAAS,EAAE;0BACT1F,UAAU,EAAE6F,aAAa,CAACvF,eAAe;0BACzCsE,SAAS,EAAE,kBAAkB;0BAC7Bc,SAAS,EAAE;wBACb,CAAC;wBACDS,UAAU,EAAE,sBAAsB;wBAClCjD,QAAQ,EAAE;sBACZ,CAAE;sBACFkD,OAAO,EAAEA,CAAA,KAAMrN,aAAa,CAACuG,OAAO,CAAE;sBAAAyC,QAAA,gBAItCxJ,OAAA,CAAChC,UAAU;wBACTmM,OAAO,EAAC,OAAO;wBACff,EAAE,EAAE;0BACFgB,KAAK,EAAE,OAAO;0BACdK,UAAU,EAAE,MAAM;0BAClBgB,QAAQ,EAAE;4BAAEZ,EAAE,EAAE,QAAQ;4BAAEC,EAAE,EAAE,QAAQ;4BAAEC,EAAE,EAAE;0BAAU,CAAC;0BACvDb,SAAS,EAAE,QAAQ;0BACnB2B,UAAU,EAAE,GAAG;0BACflB,QAAQ,EAAE,QAAQ;0BAClBmD,YAAY,EAAE,UAAU;0BACxBzE,OAAO,EAAE,aAAa;0BACtB0E,eAAe,EAAE,CAAC;0BAClBC,eAAe,EAAE,UAAU;0BAC3BC,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,2BAA2B;0BACvClE,EAAE,EAAE;4BAAEa,EAAE,EAAE,GAAG;4BAAEC,EAAE,EAAE,GAAG;4BAAEC,EAAE,EAAE;0BAAI;wBAClC,CAAE;wBAAAvB,QAAA,EAED3I,aAAa,GAAGkG,OAAO,CAACoH,YAAY,GAAGpH,OAAO,CAACqH;sBAAY;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eAEb5J,OAAA,CAACjC,GAAG;wBAACqL,EAAE,EAAE;0BAAEC,OAAO,EAAE,MAAM;0BAAEmB,GAAG,EAAE;4BAAEK,EAAE,EAAE,GAAG;4BAAEC,EAAE,EAAE,GAAG;4BAAEC,EAAE,EAAE;0BAAI;wBAAE,CAAE;wBAAAvB,QAAA,gBAC/DxJ,OAAA,CAAC9B,MAAM;0BACLmQ,IAAI,EAAC,OAAO;0BACZlE,OAAO,EAAC,WAAW;0BACnB0D,OAAO,EAAGS,CAAC,IAAK;4BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;4BACnB/N,aAAa,CAACuG,OAAO,CAAC;0BACxB,CAAE;0BACFqC,EAAE,EAAE;4BACFoF,QAAQ,EAAE,MAAM;4BAChBd,KAAK,EAAE;8BAAE7C,EAAE,EAAE,EAAE;8BAAEC,EAAE,EAAE,EAAE;8BAAEC,EAAE,EAAE;4BAAG,CAAC;4BACjCmC,MAAM,EAAE;8BAAErC,EAAE,EAAE,EAAE;8BAAEC,EAAE,EAAE,EAAE;8BAAEC,EAAE,EAAE;4BAAG,CAAC;4BAClChB,CAAC,EAAE,CAAC;4BACJiB,OAAO,EAAE,0BAA0B;4BACnCZ,KAAK,EAAE,OAAO;4BACdH,YAAY,EAAE,GAAG;4BACjB,SAAS,EAAE;8BACTe,OAAO,EAAE;4BACX;0BACF,CAAE;0BAAAxB,QAAA,eAEFxJ,OAAA,CAAClB,QAAQ;4BAACsK,EAAE,EAAE;8BAAEqC,QAAQ,EAAE;gCAAEZ,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE;8BAAS;4BAAE;0BAAE;4BAAAtB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtE,CAAC,EACR7C,OAAO,CAACQ,MAAM,KAAK,WAAW,IAAI9G,eAAe,IAAI,CAACI,aAAa,iBAClEb,OAAA,CAAC9B,MAAM;0BACLmQ,IAAI,EAAC,OAAO;0BACZlE,OAAO,EAAC,WAAW;0BACnB0D,OAAO,EAAGS,CAAC,IAAK;4BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;4BACnB9N,eAAe,CAACsG,OAAO,CAAC;0BAC1B,CAAE;0BACFqC,EAAE,EAAE;4BACFoF,QAAQ,EAAE,MAAM;4BAChBd,KAAK,EAAE;8BAAE7C,EAAE,EAAE,EAAE;8BAAEC,EAAE,EAAE,EAAE;8BAAEC,EAAE,EAAE;4BAAG,CAAC;4BACjCmC,MAAM,EAAE;8BAAErC,EAAE,EAAE,EAAE;8BAAEC,EAAE,EAAE,EAAE;8BAAEC,EAAE,EAAE;4BAAG,CAAC;4BAClChB,CAAC,EAAE,CAAC;4BACJiB,OAAO,EAAE,wBAAwB;4BACjCZ,KAAK,EAAE,OAAO;4BACdH,YAAY,EAAE,GAAG;4BACjB,SAAS,EAAE;8BACTe,OAAO,EAAE;4BACX;0BACF,CAAE;0BAAAxB,QAAA,eAEFxJ,OAAA,CAAChB,UAAU;4BAACoK,EAAE,EAAE;8BAAEqC,QAAQ,EAAE;gCAAEZ,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE;8BAAS;4BAAE;0BAAE;4BAAAtB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxE,CACT;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA,GA/HD,GAAG7C,OAAO,CAAC0H,EAAE,IAAIpB,YAAY,EAAE;sBAAA5D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgIjC,CAAC;kBAEV,CAAC;gBAAC,gBACF,CAAC;gBAAA;gBAEH;gBACA5J,OAAA,CAACjC,GAAG;kBAACqL,EAAE,EAAE;oBACPsE,KAAK,EAAE,KAAK;oBACZR,MAAM,EAAE;sBAAErC,EAAE,EAAE,MAAM;sBAAEC,EAAE,EAAE,MAAM;sBAAEC,EAAE,EAAE;oBAAQ,CAAC;oBAC/Cd,YAAY,EAAE,GAAG;oBACjBnC,MAAM,EAAE,aAAazJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE;oBAC1DqB,OAAO,EAAE,MAAM;oBACfuC,aAAa,EAAE,QAAQ;oBACvBjB,QAAQ,EAAE,QAAQ;oBAClBS,MAAM,EAAE;kBACV,CAAE;kBAAA5B,QAAA,GAEC,CAAC,MAAM;oBACN,MAAMkF,kBAAkB,GAAGtL,uBAAuB,CAACvB,GAAG,CAACO,GAAG,EAAEN,QAAQ,EAAE,IAAI,CAAC;oBAC3E,MAAM6M,eAAe,GAAGjM,oBAAoB,CAACb,GAAG,CAACO,GAAG,EAAEN,QAAQ,EAAE,IAAI,CAAC;oBAErE,oBACE9B,OAAA,CAACjC,GAAG;sBAACqL,EAAE,EAAE;wBACPwF,IAAI,EAAE,CAAC;wBACPvF,OAAO,EAAE,MAAM;wBACfiB,UAAU,EAAE,QAAQ;wBACpBhB,cAAc,EAAE,QAAQ;wBACxB2B,YAAY,EAAE,aAAa5M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE;wBAChEgD,OAAO,EAAE2D,eAAe,GACpBtQ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GACpC0G,kBAAkB,GAChBrQ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC,GACvCvJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;wBACzC4F,UAAU,EAAE;sBACd,CAAE;sBAAApE,QAAA,EACCmF,eAAe,gBACd3O,OAAA,CAACzB,OAAO;wBAACsQ,KAAK,EAAE,GAAG/M,QAAQ,CAACS,IAAI,CAACoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,SAAS1E,CAAC,CAAC,mBAAmB,EAAE,WAAW,CAAC,EAAG;wBAAC4N,KAAK;wBAAAtF,QAAA,eAC/GxJ,OAAA,CAACZ,wBAAwB;0BACvBgK,EAAE,EAAE;4BACFgB,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC;4BAC9ByD,QAAQ,EAAE;8BAAEZ,EAAE,EAAE,QAAQ;8BAAEC,EAAE,EAAE,QAAQ;8BAAEC,EAAE,EAAE;4BAAS;0BACvD;wBAAE;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC,GACR8E,kBAAkB,gBACpB1O,OAAA,CAACjC,GAAG;wBAACqL,EAAE,EAAE;0BAAEC,OAAO,EAAE,MAAM;0BAAEiB,UAAU,EAAE,QAAQ;0BAAEE,GAAG,EAAE;wBAAI,CAAE;wBAAAhB,QAAA,gBAC3DxJ,OAAA,CAACzB,OAAO;0BAACsQ,KAAK,EAAE,GAAG/M,QAAQ,CAACS,IAAI,CAACoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,SAAS1E,CAAC,CAAC,wBAAwB,EAAE,WAAW,CAAC,EAAG;0BAAC4N,KAAK;0BAAAtF,QAAA,eACpHxJ,OAAA,CAACV,eAAe;4BACd8J,EAAE,EAAE;8BACFgB,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACC,IAAI;8BACjC6D,QAAQ,EAAE;gCAAEZ,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE,MAAM;gCAAEC,EAAE,EAAE;8BAAS,CAAC;8BACpD4C,MAAM,EAAE;4BACV;0BAAE;4BAAAlE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACK,CAAC,EACT7I,WAAW,iBACVf,OAAA,CAACzB,OAAO;0BAACsQ,KAAK,EAAE3N,CAAC,CAAC,oBAAoB,EAAE,YAAY,CAAE;0BAAC4N,KAAK;0BAAAtF,QAAA,eAC1DxJ,OAAA,CAACpB,UAAU;4BACTyP,IAAI,EAAC,OAAO;4BACZR,OAAO,EAAEA,CAAA,KAAMjM,eAAe,CAACC,GAAG,CAACO,GAAG,EAAEN,QAAQ,EAAE,IAAI,CAAE;4BACxDsH,EAAE,EAAE;8BACFgB,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAACqH,OAAO,CAACnH,IAAI;8BACjC6D,QAAQ,EAAE;gCAAEZ,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE;8BAAS,CAAC;8BACtDiE,OAAO,EAAE,KAAK;8BACd,SAAS,EAAE;gCACThE,OAAO,EAAE3M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACqH,OAAO,CAACnH,IAAI,EAAE,GAAG;8BAChD;4BACF,CAAE;4BAAA4B,QAAA,eAEFxJ,OAAA,CAACR,QAAQ;8BAAC4J,EAAE,EAAE;gCAAEqC,QAAQ,EAAE;8BAAU;4BAAE;8BAAAhC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CACV;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,gBAEN5J,OAAA,CAACZ,wBAAwB;wBACvBgK,EAAE,EAAE;0BACFgB,KAAK,EAAE/L,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;0BAC1CyD,QAAQ,EAAE;4BAAEZ,EAAE,EAAE,QAAQ;4BAAEC,EAAE,EAAE,QAAQ;4BAAEC,EAAE,EAAE;0BAAS;wBACvD;sBAAE;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAEV,CAAC,EAAE,CAAC,EAGH,CAAC,MAAM;oBACN,MAAMqF,mBAAmB,GAAG7L,uBAAuB,CAACvB,GAAG,CAACO,GAAG,EAAEN,QAAQ,EAAE,KAAK,CAAC;oBAC7E,MAAMoN,gBAAgB,GAAGxM,oBAAoB,CAACb,GAAG,CAACO,GAAG,EAAEN,QAAQ,EAAE,KAAK,CAAC;oBAEvE,oBACE9B,OAAA,CAACjC,GAAG;sBAACqL,EAAE,EAAE;wBACPwF,IAAI,EAAE,CAAC;wBACPvF,OAAO,EAAE,MAAM;wBACfiB,UAAU,EAAE,QAAQ;wBACpBhB,cAAc,EAAE,QAAQ;wBACxB0B,OAAO,EAAEkE,gBAAgB,GACrB7Q,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GACpCiH,mBAAmB,GACjB5Q,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI,CAAC,GACvCvJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;wBACzC4F,UAAU,EAAE;sBACd,CAAE;sBAAApE,QAAA,EACC0F,gBAAgB,gBACflP,OAAA,CAACzB,OAAO;wBAACsQ,KAAK,EAAE,GAAG/M,QAAQ,CAACS,IAAI,CAACoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM9D,QAAQ,CAACS,IAAI,KAAK,EAAE,GAAG,QAAQ,GAAG,IAAI,CAACT,QAAQ,CAACS,IAAI,GAAG,CAAC,EAAEoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM1E,CAAC,CAAC,mBAAmB,EAAE,WAAW,CAAC,EAAG;wBAAC4N,KAAK;wBAAAtF,QAAA,eAC5MxJ,OAAA,CAACZ,wBAAwB;0BACvBgK,EAAE,EAAE;4BACFgB,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC;4BAC9ByD,QAAQ,EAAE;8BAAEZ,EAAE,EAAE,QAAQ;8BAAEC,EAAE,EAAE,QAAQ;8BAAEC,EAAE,EAAE;4BAAS;0BACvD;wBAAE;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC,GACRqF,mBAAmB,gBACrBjP,OAAA,CAACjC,GAAG;wBAACqL,EAAE,EAAE;0BAAEC,OAAO,EAAE,MAAM;0BAAEiB,UAAU,EAAE,QAAQ;0BAAEE,GAAG,EAAE;wBAAI,CAAE;wBAAAhB,QAAA,gBAC3DxJ,OAAA,CAACzB,OAAO;0BAACsQ,KAAK,EAAE,GAAG/M,QAAQ,CAACS,IAAI,CAACoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM9D,QAAQ,CAACS,IAAI,KAAK,EAAE,GAAG,QAAQ,GAAG,IAAI,CAACT,QAAQ,CAACS,IAAI,GAAG,CAAC,EAAEoD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM1E,CAAC,CAAC,wBAAwB,EAAE,WAAW,CAAC,EAAG;0BAAC4N,KAAK;0BAAAtF,QAAA,eACjNxJ,OAAA,CAACV,eAAe;4BACd8J,EAAE,EAAE;8BACFgB,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAACC,OAAO,CAACC,IAAI;8BACjC6D,QAAQ,EAAE;gCAAEZ,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE,MAAM;gCAAEC,EAAE,EAAE;8BAAS,CAAC;8BACpD4C,MAAM,EAAE;4BACV;0BAAE;4BAAAlE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACK,CAAC,EACT7I,WAAW,iBACVf,OAAA,CAACzB,OAAO;0BAACsQ,KAAK,EAAE3N,CAAC,CAAC,oBAAoB,EAAE,YAAY,CAAE;0BAAC4N,KAAK;0BAAAtF,QAAA,eAC1DxJ,OAAA,CAACpB,UAAU;4BACTyP,IAAI,EAAC,OAAO;4BACZR,OAAO,EAAEA,CAAA,KAAMjM,eAAe,CAACC,GAAG,CAACO,GAAG,EAAEN,QAAQ,EAAE,KAAK,CAAE;4BACzDsH,EAAE,EAAE;8BACFgB,KAAK,EAAEhJ,KAAK,CAACsG,OAAO,CAACqH,OAAO,CAACnH,IAAI;8BACjC6D,QAAQ,EAAE;gCAAEZ,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE,QAAQ;gCAAEC,EAAE,EAAE;8BAAS,CAAC;8BACtDiE,OAAO,EAAE,KAAK;8BACd,SAAS,EAAE;gCACThE,OAAO,EAAE3M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACqH,OAAO,CAACnH,IAAI,EAAE,GAAG;8BAChD;4BACF,CAAE;4BAAA4B,QAAA,eAEFxJ,OAAA,CAACR,QAAQ;8BAAC4J,EAAE,EAAE;gCAAEqC,QAAQ,EAAE;8BAAU;4BAAE;8BAAAhC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CACV;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,gBAEN5J,OAAA,CAACZ,wBAAwB;wBACvBgK,EAAE,EAAE;0BACFgB,KAAK,EAAE/L,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;0BAC1CyD,QAAQ,EAAE;4BAAEZ,EAAE,EAAE,QAAQ;4BAAEC,EAAE,EAAE,QAAQ;4BAAEC,EAAE,EAAE;0BAAS;wBACvD;sBAAE;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAEV,CAAC,EAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACN;cAAA,GAvXI,GAAG/H,GAAG,CAACO,GAAG,IAAIN,QAAQ,CAACM,GAAG,EAAE;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwX9B,CAAC;YAEV,CAAC,CAAC;UAAA,GAveG9H,QAAQ,CAACM,GAAG;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwed,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5J,OAAA,CAACxB,MAAM;MACL2Q,IAAI,EAAE5N,eAAgB;MACtB6N,OAAO,EAAEA,CAAA,KAAM5N,kBAAkB,CAAC,KAAK,CAAE;MACzCyM,QAAQ,EAAC,IAAI;MACboB,SAAS;MAAA7F,QAAA,gBAETxJ,OAAA,CAACvB,WAAW;QAAC2K,EAAE,EAAE;UAAEc,SAAS,EAAE,QAAQ;UAAEoF,EAAE,EAAE;QAAE,CAAE;QAAA9F,QAAA,gBAC9CxJ,OAAA,CAACR,QAAQ;UAAC4J,EAAE,EAAE;YAAEqC,QAAQ,EAAE,MAAM;YAAErB,KAAK,EAAE,cAAc;YAAEJ,EAAE,EAAE;UAAE;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpE5J,OAAA,CAAChC,UAAU;UAACmM,OAAO,EAAC,IAAI;UAAC2B,SAAS,EAAC,KAAK;UAAAtC,QAAA,EACrCtI,CAAC,CAAC,yBAAyB,EAAE,oBAAoB;QAAC;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEd5J,OAAA,CAACtB,aAAa;QAAC0K,EAAE,EAAE;UAAEc,SAAS,EAAE,QAAQ;UAAEqF,EAAE,EAAE;QAAE,CAAE;QAAA/F,QAAA,EAC/C9H,iBAAiB,iBAChB1B,OAAA,CAACjC,GAAG;UAAAyL,QAAA,gBACFxJ,OAAA,CAAChC,UAAU;YAACmM,OAAO,EAAC,OAAO;YAACf,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,EACvCtI,CAAC,CAAC,2BAA2B,EAAE,6BAA6B;UAAC;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eAEb5J,OAAA,CAACjC,GAAG;YAACqL,EAAE,EAAE;cACP4B,OAAO,EAAE3M,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACqH,OAAO,CAACnH,IAAI,EAAE,GAAG,CAAC;cAC/CmC,CAAC,EAAE,CAAC;cACJE,YAAY,EAAE,CAAC;cACfnC,MAAM,EAAE,aAAazJ,KAAK,CAAC+C,KAAK,CAACsG,OAAO,CAACqH,OAAO,CAACnH,IAAI,EAAE,GAAG,CAAC;YAC7D,CAAE;YAAA4B,QAAA,gBACAxJ,OAAA,CAAChC,UAAU;cAACmM,OAAO,EAAC,IAAI;cAACf,EAAE,EAAE;gBAAEgB,KAAK,EAAE,cAAc;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,GAAC,eAC1D,EAAC/J,MAAM,CAACiC,iBAAiB,CAACW,IAAI,EAAE,mBAAmB,EAAE;gBAAE2J,MAAM,EAAE3K,KAAK,GAAG1B,EAAE,GAAGC;cAAK,CAAC,CAAC;YAAA;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACb5J,OAAA,CAAChC,UAAU;cAACmM,OAAO,EAAC,IAAI;cAACf,EAAE,EAAE;gBAAEqB,UAAU,EAAE;cAAO,CAAE;cAAAjB,QAAA,GAAC,eAChD,EAAC,CAAC,MAAM;gBACT,MAAMpE,SAAS,GAAG1D,iBAAiB,CAACa,IAAI;gBACxC,MAAM8C,WAAW,GAAG3D,iBAAiB,CAACc,MAAM;gBAC5C,MAAM+C,SAAS,GAAGF,WAAW,GAAG,EAAE;gBAClC,MAAMC,OAAO,GAAGC,SAAS,IAAI,EAAE,GAAGH,SAAS,GAAG,CAAC,GAAGA,SAAS;gBAC3D,MAAMoK,cAAc,GAAGjK,SAAS,IAAI,EAAE,GAAGA,SAAS,GAAG,EAAE,GAAGA,SAAS;gBAEnE,OAAO,GAAGH,SAAS,CAACO,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIP,WAAW,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,MAAMN,OAAO,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI4J,cAAc,CAAC7J,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;cACrL,CAAC,EAAE,CAAC;YAAA;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN5J,OAAA,CAAChC,UAAU;YAACmM,OAAO,EAAC,OAAO;YAACf,EAAE,EAAE;cAAE2C,EAAE,EAAE,CAAC;cAAE3B,KAAK,EAAE;YAAiB,CAAE;YAAAZ,QAAA,EAChEtI,CAAC,CAAC,wBAAwB,EAAE,iDAAiD;UAAC;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAEhB5J,OAAA,CAACrB,aAAa;QAACyK,EAAE,EAAE;UAAEE,cAAc,EAAE,QAAQ;UAAEgG,EAAE,EAAE;QAAE,CAAE;QAAA9F,QAAA,gBACrDxJ,OAAA,CAAC9B,MAAM;UACL2P,OAAO,EAAEA,CAAA,KAAMrM,kBAAkB,CAAC,KAAK,CAAE;UACzC2I,OAAO,EAAC,UAAU;UAClBf,EAAE,EAAE;YAAEoF,QAAQ,EAAE;UAAI,CAAE;UAAAhF,QAAA,EAErBtI,CAAC,CAAC,eAAe,EAAE,OAAO;QAAC;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACT5J,OAAA,CAAC9B,MAAM;UACL2P,OAAO,EAAEpL,gBAAiB;UAC1B0H,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,SAAS;UACfhB,EAAE,EAAE;YAAEoF,QAAQ,EAAE,GAAG;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAjG,QAAA,EAE5BtI,CAAC,CAAC,gBAAgB,EAAE,OAAO;QAAC;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ,CAAC;AAAC3I,EAAA,CAjiCId,mBAAmB;EAAA,QAeHrC,cAAc,EACpBM,QAAQ;AAAA;AAAAsR,EAAA,GAhBlBvP,mBAAmB;AAmiCzB,eAAeA,mBAAmB;AAAC,IAAAuP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}