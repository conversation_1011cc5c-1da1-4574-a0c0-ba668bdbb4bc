{"ast": null, "code": "function d() {\n  let r;\n  return {\n    before({\n      doc: e\n    }) {\n      var l;\n      let o = e.documentElement,\n        t = (l = e.defaultView) != null ? l : window;\n      r = Math.max(0, t.innerWidth - o.clientWidth);\n    },\n    after({\n      doc: e,\n      d: o\n    }) {\n      let t = e.documentElement,\n        l = Math.max(0, t.clientWidth - t.offsetWidth),\n        n = Math.max(0, r - l);\n      o.style(t, \"paddingRight\", `${n}px`);\n    }\n  };\n}\nexport { d as adjustScrollbarPadding };", "map": {"version": 3, "names": ["d", "r", "before", "doc", "e", "l", "o", "documentElement", "t", "defaultView", "window", "Math", "max", "innerWidth", "clientWidth", "after", "offsetWidth", "n", "style", "adjustScrollbarPadding"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js"], "sourcesContent": ["function d(){let r;return{before({doc:e}){var l;let o=e.documentElement,t=(l=e.defaultView)!=null?l:window;r=Math.max(0,t.innerWidth-o.clientWidth)},after({doc:e,d:o}){let t=e.documentElement,l=Math.max(0,t.clientWidth-t.offsetWidth),n=Math.max(0,r-l);o.style(t,\"paddingRight\",`${n}px`)}}}export{d as adjustScrollbarPadding};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC;EAAC,OAAM;IAACC,MAAMA,CAAC;MAACC,GAAG,EAACC;IAAC,CAAC,EAAC;MAAC,IAAIC,CAAC;MAAC,IAAIC,CAAC,GAACF,CAAC,CAACG,eAAe;QAACC,CAAC,GAAC,CAACH,CAAC,GAACD,CAAC,CAACK,WAAW,KAAG,IAAI,GAACJ,CAAC,GAACK,MAAM;MAACT,CAAC,GAACU,IAAI,CAACC,GAAG,CAAC,CAAC,EAACJ,CAAC,CAACK,UAAU,GAACP,CAAC,CAACQ,WAAW,CAAC;IAAA,CAAC;IAACC,KAAKA,CAAC;MAACZ,GAAG,EAACC,CAAC;MAACJ,CAAC,EAACM;IAAC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACJ,CAAC,CAACG,eAAe;QAACF,CAAC,GAACM,IAAI,CAACC,GAAG,CAAC,CAAC,EAACJ,CAAC,CAACM,WAAW,GAACN,CAAC,CAACQ,WAAW,CAAC;QAACC,CAAC,GAACN,IAAI,CAACC,GAAG,CAAC,CAAC,EAACX,CAAC,GAACI,CAAC,CAAC;MAACC,CAAC,CAACY,KAAK,CAACV,CAAC,EAAC,cAAc,EAAC,GAAGS,CAAC,IAAI,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOjB,CAAC,IAAImB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}