{"ast": null, "code": "const $b4b717babfbb907b$var$focusableElements = ['input:not([disabled]):not([type=hidden])', 'select:not([disabled])', 'textarea:not([disabled])', 'button:not([disabled])', 'a[href]', 'area[href]', 'summary', 'iframe', 'object', 'embed', 'audio[controls]', 'video[controls]', '[contenteditable]:not([contenteditable^=\"false\"])'];\nconst $b4b717babfbb907b$var$FOCUSABLE_ELEMENT_SELECTOR = $b4b717babfbb907b$var$focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n$b4b717babfbb907b$var$focusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst $b4b717babfbb907b$var$TABBABLE_ELEMENT_SELECTOR = $b4b717babfbb907b$var$focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\nfunction $b4b717babfbb907b$export$4c063cf1350e6fed(element) {\n  return element.matches($b4b717babfbb907b$var$FOCUSABLE_ELEMENT_SELECTOR);\n}\nfunction $b4b717babfbb907b$export$bebd5a1431fec25d(element) {\n  return element.matches($b4b717babfbb907b$var$TABBABLE_ELEMENT_SELECTOR);\n}\nexport { $b4b717babfbb907b$export$4c063cf1350e6fed as isFocusable, $b4b717babfbb907b$export$bebd5a1431fec25d as isTabbable };", "map": {"version": 3, "names": ["$b4b717babfbb907b$var$focusableElements", "$b4b717babfbb907b$var$FOCUSABLE_ELEMENT_SELECTOR", "join", "push", "$b4b717babfbb907b$var$TABBABLE_ELEMENT_SELECTOR", "$b4b717babfbb907b$export$4c063cf1350e6fed", "element", "matches", "$b4b717babfbb907b$export$bebd5a1431fec25d"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\isFocusable.ts"], "sourcesContent": ["const focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR);\n}\n"], "mappings": "AAAA,MAAMA,uCAAA,GAAoB,CACxB,4CACA,0BACA,4BACA,0BACA,WACA,cACA,WACA,UACA,UACA,SACA,mBACA,mBACA,oDACD;AAED,MAAMC,gDAAA,GAA6BD,uCAAA,CAAkBE,IAAI,CAAC,qBAAqB;AAE/EF,uCAAA,CAAkBG,IAAI,CAAC;AACvB,MAAMC,+CAAA,GAA4BJ,uCAAA,CAAkBE,IAAI,CAAC;AAElD,SAASG,0CAAYC,OAAgB;EAC1C,OAAOA,OAAA,CAAQC,OAAO,CAACN,gDAAA;AACzB;AAEO,SAASO,0CAAWF,OAAgB;EACzC,OAAOA,OAAA,CAAQC,OAAO,CAACH,+CAAA;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}