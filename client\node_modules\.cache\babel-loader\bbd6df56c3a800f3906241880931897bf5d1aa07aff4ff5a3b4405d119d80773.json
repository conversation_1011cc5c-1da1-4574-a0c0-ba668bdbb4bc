{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{Box,Typography,Paper,Button,Chip,useTheme,alpha,CircularProgress,Tooltip,Dialog,DialogTitle,DialogContent,DialogActions,IconButton}from'@mui/material';import{Visibility as ViewIcon,Cancel as CancelIcon,AccessTime as AccessTimeIcon,RadioButtonUnchecked as RadioButtonUncheckedIcon,CheckCircle as CheckCircleIcon,SelfImprovement as RestIcon}from'@mui/icons-material';import{format,addDays}from'date-fns';import{ar,enUS}from'date-fns/locale';import moment from'moment-timezone';import{formatDateInStudentTimezone}from'../utils/timezone';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const WeeklyBookingsTable=_ref=>{let{bookings,loading=false,currentWeekStart,daysOfWeek,onViewDetails,onCancelBooking,studentProfile,formatBookingTime,getStatusColor,isTeacherView=false,availableHours=null,onTakeBreak=null,weeklyBreaks=[]}=_ref;const{t,i18n}=useTranslation();const theme=useTheme();const isRtl=i18n.language==='ar';// State for break dialog\nconst[breakDialogOpen,setBreakDialogOpen]=React.useState(false);const[selectedBreakSlot,setSelectedBreakSlot]=React.useState(null);// Handle take break\nconst handleTakeBreak=(day,timeSlot,isFirstHalf)=>{const dayIndex=daysOfWeekData.findIndex(d=>d.key===day);const date=addDays(currentWeekStart,dayIndex);const slotMinute=isFirstHalf?0:30;setSelectedBreakSlot({day,date,hour:timeSlot.hour,minute:slotMinute,isFirstHalf,timeSlot});setBreakDialogOpen(true);};const confirmTakeBreak=()=>{if(selectedBreakSlot&&onTakeBreak){onTakeBreak(selectedBreakSlot);}setBreakDialogOpen(false);setSelectedBreakSlot(null);};// Check if a half-hour slot is in the past (considering teacher's timezone)\nconst isHalfHourSlotInPast=(day,timeSlot,isFirstHalf)=>{try{const dayIndex=daysOfWeekData.findIndex(d=>d.key===day);const date=addDays(currentWeekStart,dayIndex);const slotMinute=isFirstHalf?0:30;// Create the slot start datetime\nconst slotStartTime=new Date(date);slotStartTime.setHours(timeSlot.hour,slotMinute,0,0);// Get current time in teacher's timezone using the same method as the table\nlet currentTime;if(studentProfile&&studentProfile.timezone){// Use teacher's timezone\nconst currentTimeStr=formatDateInStudentTimezone(new Date().toISOString(),studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');currentTime=new Date(currentTimeStr);}else{// Fallback to browser local time\ncurrentTime=new Date();}// A slot is considered \"past\" if its start time has already passed\n// This means the current half-hour slot is also considered past\nreturn slotStartTime<=currentTime;}catch(error){console.error('Error checking if slot is in past:',error);return false;}};// Check if a specific half-hour slot is available\nconst isHalfHourSlotAvailable=(day,timeSlot,isFirstHalf)=>{if(!availableHours||!isTeacherView)return false;const dayKey=day.toLowerCase();const daySlots=availableHours[dayKey];if(!daySlots||!Array.isArray(daySlots))return false;// Check if this slot is taken as a break\nconst dayIndex=daysOfWeekData.findIndex(d=>d.key===day);const date=addDays(currentWeekStart,dayIndex);const slotMinute=isFirstHalf?0:30;// Create break key in teacher's timezone (to match backend format)\n// The backend sends break keys in teacher's local time, so we need to match that format\nlet breakKey;if(studentProfile&&studentProfile.timezone){// Parse teacher timezone offset (e.g., \"UTC+05:00\")\nconst teacherTimezone=studentProfile.timezone;const timezoneMatch=teacherTimezone.match(/UTC([+-])(\\d{2}):(\\d{2})/);if(timezoneMatch){const sign=timezoneMatch[1]==='+'?1:-1;const hours=parseInt(timezoneMatch[2]);const minutes=parseInt(timezoneMatch[3]);const offsetMinutes=sign*(hours*60+minutes);// Create UTC datetime for this slot\nconst slotDateTime=new Date(date);slotDateTime.setHours(timeSlot.hour,slotMinute,0,0);// Convert to teacher's timezone by adding offset\nconst teacherDateTime=new Date(slotDateTime.getTime()+offsetMinutes*60*1000);// Use UTC methods to avoid local timezone interference (same as backend)\nconst teacherDateStr=teacherDateTime.toISOString().split('T')[0];const teacherHour=teacherDateTime.getUTCHours();const teacherMinute=teacherDateTime.getUTCMinutes();breakKey=`${teacherDateStr}_${teacherHour.toString().padStart(2,'0')}:${teacherMinute.toString().padStart(2,'0')}`;}else{// Fallback if timezone format is not recognized\nbreakKey=`${date.toISOString().split('T')[0]}_${timeSlot.hour.toString().padStart(2,'0')}:${slotMinute.toString().padStart(2,'0')}`;}}else{// Fallback to local time\nbreakKey=`${date.toISOString().split('T')[0]}_${timeSlot.hour.toString().padStart(2,'0')}:${slotMinute.toString().padStart(2,'0')}`;}console.log('🔍 FRONTEND CHECK: Checking if slot is break');console.log('  📅 Local Date:',date.toISOString().split('T')[0]);console.log('  ⏰ Local Hour:',timeSlot.hour,'Minute:',slotMinute);console.log('  🔑 Break key (teacher timezone):',breakKey);console.log('  📋 All weekly breaks:',weeklyBreaks);console.log('  ❓ Is break?',weeklyBreaks.includes(breakKey));if(weeklyBreaks.includes(breakKey)){console.log('  🚫 SLOT IS BREAK - hiding slot');return false;// Slot is taken as break\n}// Calculate the exact 30-minute slot we're checking (using existing slotMinute)\nconst slotStartMinutes=timeSlot.hour*60+slotMinute;const slotEndMinutes=slotStartMinutes+30;// Handle edge case for last half hour of the day (23:30-24:00)\nif(timeSlot.hour===23&&!isFirstHalf){// For 23:30-24:00, check if any available slot covers 23:30\nreturn daySlots.some(slot=>{const[startTime,endTime]=slot.split('-');const[startHour,startMinute]=startTime.split(':').map(Number);let[endHour,endMinute]=endTime.split(':').map(Number);// Handle 24:00 or 00:00 as end time\nif(endHour===0||endHour===24){endHour=24;endMinute=0;}const availableStartMinutes=startHour*60+startMinute;const availableEndMinutes=endHour*60+endMinute;// Check if 23:30 is covered\nreturn slotStartMinutes>=availableStartMinutes&&slotStartMinutes<availableEndMinutes;});}// Check if this specific 30-minute slot is in the available hours\nreturn daySlots.some(slot=>{const[startTime,endTime]=slot.split('-');const[startHour,startMinute]=startTime.split(':').map(Number);let[endHour,endMinute]=endTime.split(':').map(Number);// Handle 24:00 or 00:00 as end time (next day)\nif(endHour===0){endHour=24;endMinute=0;}const availableStartMinutes=startHour*60+startMinute;const availableEndMinutes=endHour*60+endMinute;// Check if the 30-minute slot fits within the available time range\nreturn slotStartMinutes>=availableStartMinutes&&slotEndMinutes<=availableEndMinutes;});};// Define time slots - full hours from 00:00 to 23:00\nconst timeSlots=[];for(let hour=0;hour<24;hour++){const startTime=`${hour.toString().padStart(2,'0')}:00`;const midTime=`${hour.toString().padStart(2,'0')}:30`;const endTime=hour<23?`${(hour+1).toString().padStart(2,'0')}:00`:'00:00';timeSlots.push({key:`${startTime}-${endTime}`,label:startTime,midLabel:midTime,hour,minute:0,// Include both half-hour slots for this hour\nfirstHalf:`${startTime}-${midTime}`,secondHalf:hour<23?`${midTime}-${endTime}`:'23:30-00:00'});}// Define days of the week\nconst defaultDaysOfWeek=['monday','tuesday','wednesday','thursday','friday','saturday','sunday'];const daysOfWeekData=daysOfWeek?daysOfWeek.map(day=>({key:day,label:t(`days.${day}`)})):defaultDaysOfWeek.map(day=>({key:day,label:t(`days.${day}`)}));// Get abbreviated day names for mobile\nconst getAbbreviatedDayName=dayKey=>{const abbreviations={sunday:t('days.sundayShort')||'Sun',monday:t('days.mondayShort')||'Mon',tuesday:t('days.tuesdayShort')||'Tue',wednesday:t('days.wednesdayShort')||'Wed',thursday:t('days.thursdayShort')||'Thu',friday:t('days.fridayShort')||'Fri',saturday:t('days.saturdayShort')||'Sat'};return abbreviations[dayKey]||dayKey.substring(0,3);};// Get meeting status based on current time\nconst getMeetingStatus=booking=>{const meetingStartTime=new Date(booking.datetime);const meetingEndTime=new Date(booking.datetime);meetingEndTime.setMinutes(meetingEndTime.getMinutes()+parseInt(booking.duration));const now=new Date();if(booking.status==='cancelled'){return'cancelled';}if(now>=meetingStartTime&&now<meetingEndTime){return'ongoing';}if(now>=meetingEndTime){return'completed';}return'scheduled';};// Get background color based on meeting status\nconst getBookingBackgroundColor=booking=>{const status=getMeetingStatus(booking);switch(status){case'ongoing':return{background:`linear-gradient(135deg, ${alpha(theme.palette.success.main,0.85)} 0%, ${alpha(theme.palette.success.dark,0.95)} 100%)`,border:`2px solid ${alpha(theme.palette.success.main,0.3)}`,hoverBackground:`linear-gradient(135deg, ${alpha(theme.palette.success.main,0.95)} 0%, ${alpha(theme.palette.success.dark,1)} 100%)`};case'completed':return{background:`linear-gradient(135deg, ${alpha(theme.palette.grey[500],0.85)} 0%, ${alpha(theme.palette.grey[700],0.95)} 100%)`,border:`2px solid ${alpha(theme.palette.grey[500],0.3)}`,hoverBackground:`linear-gradient(135deg, ${alpha(theme.palette.grey[500],0.95)} 0%, ${alpha(theme.palette.grey[700],1)} 100%)`};case'cancelled':return{background:`linear-gradient(135deg, ${alpha(theme.palette.error.main,0.85)} 0%, ${alpha(theme.palette.error.dark,0.95)} 100%)`,border:`2px solid ${alpha(theme.palette.error.main,0.3)}`,hoverBackground:`linear-gradient(135deg, ${alpha(theme.palette.error.main,0.95)} 0%, ${alpha(theme.palette.error.dark,1)} 100%)`};default:// scheduled\nreturn{background:`linear-gradient(135deg, ${alpha(theme.palette.info.main,0.85)} 0%, ${alpha(theme.palette.info.dark,0.95)} 100%)`,border:`2px solid ${alpha(theme.palette.info.main,0.3)}`,hoverBackground:`linear-gradient(135deg, ${alpha(theme.palette.info.main,0.95)} 0%, ${alpha(theme.palette.info.dark,1)} 100%)`};}};// Find all bookings for specific day and time slot\nconst findBookingsForSlot=(day,timeSlot)=>{if(!bookings||!currentWeekStart)return[];const dayIndex=daysOfWeekData.findIndex(d=>d.key===day);const date=addDays(currentWeekStart,dayIndex);const dateStr=format(date,'yyyy-MM-dd');return bookings.filter(booking=>{// Get booking date and time in student timezone\nlet bookingDate,bookingHour,bookingMinute;if(studentProfile&&studentProfile.timezone){// Use student timezone for both date and time\nconst formattedDateTime=formatDateInStudentTimezone(booking.datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');const[datePart,timePart]=formattedDateTime.split(' ');bookingDate=datePart;const[hourStr,minuteStr]=timePart.split(':');bookingHour=parseInt(hourStr);bookingMinute=parseInt(minuteStr);}else{// Fallback to browser local time\nconst bookingDateTime=new Date(booking.datetime);bookingDate=format(bookingDateTime,'yyyy-MM-dd');bookingHour=bookingDateTime.getHours();bookingMinute=bookingDateTime.getMinutes();}// Check if booking matches this date\nconst isDateMatch=bookingDate===dateStr;if(!isDateMatch)return false;// Calculate booking start and end times in minutes from midnight\nconst duration=parseInt(booking.duration)||25;const bookingStartMinutes=bookingHour*60+bookingMinute;const bookingEndMinutes=bookingStartMinutes+duration;// Calculate slot start and end times in minutes from midnight\nconst slotStartMinutes=timeSlot.hour*60;const slotEndMinutes=slotStartMinutes+60;// For 50-minute lessons, show in both starting hour and next hour if it spans\nif(duration===50){// Check if booking starts in this slot\nconst startsInThisSlot=bookingStartMinutes>=slotStartMinutes&&bookingStartMinutes<slotEndMinutes;// Check if booking extends into this slot from previous hour\nconst extendsIntoThisSlot=bookingStartMinutes<slotStartMinutes&&bookingEndMinutes>slotStartMinutes;return startsInThisSlot||extendsIntoThisSlot;}else{// For 25-minute lessons, show in the exact 30-minute slot\nconst slotMiddle=slotStartMinutes+30;// Check if booking is in first half (00-30) or second half (30-60)\nif(bookingStartMinutes>=slotStartMinutes&&bookingStartMinutes<slotMiddle){// First half booking\nreturn bookingStartMinutes>=slotStartMinutes&&bookingStartMinutes<slotMiddle;}else if(bookingStartMinutes>=slotMiddle&&bookingStartMinutes<slotEndMinutes){// Second half booking\nreturn bookingStartMinutes>=slotMiddle&&bookingStartMinutes<slotEndMinutes;}}return false;});};if(loading){return/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',my:4},children:/*#__PURE__*/_jsx(CircularProgress,{})});}if(!bookings||bookings.length===0){return/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:3,mb:4,borderRadius:2,textAlign:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:t('bookings.noBookings')})});}return/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{mb:4,borderRadius:2},children:[/*#__PURE__*/_jsx(Box,{sx:{background:`linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,p:3,color:'white',display:'flex',justifyContent:'space-between',alignItems:'center',flexWrap:'wrap',gap:2},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:'bold',mb:1},children:[\"\\uD83D\\uDCC5 \",t('bookings.weeklyTitle')]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{opacity:0.9},children:t('bookings.weeklyDescription')})]})}),/*#__PURE__*/_jsxs(Box,{sx:{overflow:'auto'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'60px repeat(7, minmax(80px, 1fr))',sm:'80px repeat(7, minmax(100px, 1fr))',md:'120px repeat(7, minmax(120px, 1fr))'},bgcolor:alpha(theme.palette.primary.main,0.05),borderBottom:`2px solid ${alpha(theme.palette.primary.main,0.1)}`,position:'sticky',top:0,zIndex:10},children:[/*#__PURE__*/_jsx(Box,{sx:{p:{xs:1.5,sm:2,md:2.5},minHeight:{xs:'60px',sm:'75px',md:'90px'},display:'flex',alignItems:'center',justifyContent:'center',borderRight:`1px solid ${alpha(theme.palette.primary.main,0.1)}`},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{fontWeight:'bold',color:theme.palette.text.secondary,fontSize:{xs:'0.8rem',sm:'0.9rem',md:'1rem'}},children:[\"\\u23F0 \",t('teacher.time')]})}),daysOfWeekData.map((day,index)=>{// Calculate the date for this day\nconst dayDate=currentWeekStart?addDays(currentWeekStart,index):new Date();return/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:1.5,sm:2,md:2.5},minHeight:{xs:'60px',sm:'75px',md:'90px'},textAlign:'center',borderRight:`1px solid ${alpha(theme.palette.primary.main,0.1)}`,'&:last-child':{borderRight:'none'},display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:'bold',color:theme.palette.primary.main,fontSize:{xs:'0.8rem',sm:'1rem',md:'1.2rem'},lineHeight:1.2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{display:{xs:'none',md:'block'}},children:day.label}),/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{display:{xs:'none',sm:'block',md:'none'}},children:day.label.length>6?day.label.substring(0,6):day.label}),/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{display:{xs:'block',sm:'none'}},children:getAbbreviatedDayName(day.key)})]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:'block',color:theme.palette.text.secondary,fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'},mt:0.5},children:format(dayDate,'MMM d',{locale:isRtl?ar:enUS})})]},day.key);})]}),/*#__PURE__*/_jsx(Box,{children:timeSlots.map((timeSlot,index)=>{const isHourStart=timeSlot.minute===0;return/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'60px repeat(7, minmax(80px, 1fr))',sm:'80px repeat(7, minmax(100px, 1fr))',md:'120px repeat(7, minmax(120px, 1fr))'},borderBottom:`1px solid ${alpha(theme.palette.divider,isHourStart?0.3:0.1)}`,bgcolor:index%4<2?alpha(theme.palette.primary.main,0.02):'transparent','&:hover':{bgcolor:alpha(theme.palette.primary.main,0.05)}},children:[/*#__PURE__*/_jsx(Box,{sx:{p:{xs:0.5,sm:1,md:1.5},display:'grid',placeItems:'center',borderRight:`1px solid ${alpha(theme.palette.primary.main,0.1)}`,bgcolor:isHourStart?alpha(theme.palette.primary.main,0.05):'transparent',position:'relative'},children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',gap:0.5,position:'absolute',left:'50%',transform:'translateX(-50%)',top:timeSlot.minute===0?'-8px':'calc(50% + 20px)'},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontWeight:'bold',color:theme.palette.primary.main,fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'}},children:timeSlot.label})})}),daysOfWeekData.map(day=>{const allBookingsInSlot=findBookingsForSlot(day.key,timeSlot);// Filter out extended bookings from previous hour\n// Filter out extended bookings from previous hour\nconst bookingsInSlot=allBookingsInSlot.filter(booking=>{// Get booking start time to determine if it's extended from previous hour\nlet bookingHour=0;let bookingMinute=0;if(studentProfile&&studentProfile.timezone){const formattedDateTime=formatDateInStudentTimezone(booking.datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');const[,timePart]=formattedDateTime.split(' ');const[hourStr,minuteStr]=timePart.split(':');bookingHour=parseInt(hourStr);bookingMinute=parseInt(minuteStr);}else{const bookingDate=new Date(booking.datetime);bookingHour=bookingDate.getHours();bookingMinute=bookingDate.getMinutes();}const duration=parseInt(booking.duration)||25;const bookingStartMinutes=bookingHour*60+bookingMinute;const currentSlotStartMinutes=timeSlot.hour*60;// Only show bookings that start in this slot or earlier in the same hour\n// Don't show bookings that extend from previous hour\nif(duration===50){const extendsIntoThisSlot=bookingStartMinutes<currentSlotStartMinutes;return!extendsIntoThisSlot;// Filter out extended bookings\n}return true;// Show all 25-minute bookings\n});// Deduplicate overlapping bookings that share the exact same start time (datetime)\n// If a non-cancelled booking and a cancelled booking overlap, keep the non-cancelled one\n// so the student sees the active reservation instead of the cancelled one.\nconst visibleBookingsMap=bookingsInSlot.reduce((acc,curr)=>{const key=curr.datetime;// ISO string coming from backend\nconst existing=acc[key];if(!existing){acc[key]=curr;// first occurrence\n}else{// If the existing booking is cancelled and the new one is not, replace it\nif(existing.status==='cancelled'&&curr.status!=='cancelled'){acc[key]=curr;}// If both have same non-cancelled status keep the earlier inserted one\n// otherwise, leave as is.\n}return acc;},{});const visibleBookings=Object.values(visibleBookingsMap);return/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:0.2,sm:0.4,md:0.6},minHeight:{xs:'80px',sm:'100px',md:'120px'},// Increased height for better content fit\ndisplay:'flex',alignItems:'center',justifyContent:'center',borderRight:`1px solid ${alpha(theme.palette.primary.main,0.1)}`,'&:last-child':{borderRight:'none'},position:'relative'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,right:0,height:'4px',bgcolor:theme.palette.primary.main,zIndex:0,borderRadius:'2px',boxShadow:'0 2px 4px rgba(0,0,0,0.1)',pointerEvents:'none'}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:'50%',left:0,right:0,height:'2px',bgcolor:alpha(theme.palette.divider,0.3),// More transparent\nzIndex:0,// Lower z-index to stay behind bookings\nborderRadius:'1px'}}),visibleBookings.length>0?/*#__PURE__*/_jsx(_Fragment,{children:visibleBookings.map((booking,bookingIndex)=>{const bookingColors=getBookingBackgroundColor(booking);const isFullLesson=parseInt(booking.duration)===50;// Get booking start time to determine position\nlet bookingMinute=0;let bookingHour=0;if(studentProfile&&studentProfile.timezone){const formattedDateTime=formatDateInStudentTimezone(booking.datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');const[,timePart]=formattedDateTime.split(' ');const[hourStr,minuteStr]=timePart.split(':');bookingHour=parseInt(hourStr);bookingMinute=parseInt(minuteStr);}else{const bookingDate=new Date(booking.datetime);bookingHour=bookingDate.getHours();bookingMinute=bookingDate.getMinutes();}// For full lessons, determine which half of this slot the booking occupies\n// For half lessons, show in correct half based on start time\nlet isFirstHalf=bookingMinute===0;// For full lessons, determine the visual representation\nif(isFullLesson){// Calculate if this booking spans into the next hour\nconst bookingStartMinutes=bookingHour*60+bookingMinute;const bookingEndMinutes=bookingStartMinutes+50;// 50-minute lesson\nconst currentSlotStartMinutes=timeSlot.hour*60;const currentSlotEndMinutes=currentSlotStartMinutes+60;// Check if booking starts in this slot\nconst startsInThisSlot=bookingStartMinutes>=currentSlotStartMinutes&&bookingStartMinutes<currentSlotEndMinutes;if(startsInThisSlot){// Booking starts in this slot\nconst extendsToNextHour=bookingEndMinutes>currentSlotEndMinutes;if(extendsToNextHour){// This is a cross-hour booking, show it spanning from current position to next hour\nisFirstHalf='spanning';// Special case for spanning bookings\n}else{// Regular full lesson within one hour - don't override first half bookings\nisFirstHalf=bookingMinute===0?null:'secondHalfFull';}}}return/*#__PURE__*/_jsxs(Box,{sx:{width:'90%',height:(()=>{if(isFirstHalf==='spanning'){// For cross-hour bookings, extend from second half to next hour\nreturn'100%';// Extend to cover both halves visually\n}if(isFirstHalf==='secondHalfFull'){// Full lesson starting from second half - only show in second half\nreturn'40%';}return isFullLesson?'95%':'40%';})(),position:'absolute',top:(()=>{if(isFirstHalf==='spanning'){// Start from the second half of current slot\nreturn'50%';}if(isFirstHalf==='secondHalfFull'){// Position in second half only\nreturn'55%';}return isFullLesson?'2.5%':isFirstHalf?'5%':'55%';})(),left:'5%',zIndex:(()=>{if(isFirstHalf==='spanning'){return 100;// Very high z-index for spanning bookings\n}return 10+bookingIndex;// Stacked z-index for multiple bookings\n})(),borderRadius:1,background:bookingColors.background,display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',gap:{xs:0.2,sm:0.3,md:0.4},p:{xs:0.2,sm:0.3,md:0.4},cursor:'pointer',border:bookingColors.border,boxShadow:'0 2px 8px rgba(0,0,0,0.1)','&:hover':{background:bookingColors.hoverBackground,transform:'translateY(-1px)',boxShadow:'0 4px 12px rgba(0,0,0,0.15)'},transition:'all 0.2s ease-in-out',overflow:'hidden'},onClick:()=>onViewDetails(booking),children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'white',fontWeight:'bold',fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.75rem'},textAlign:'center',lineHeight:1.1,overflow:'hidden',textOverflow:'ellipsis',display:'-webkit-box',WebkitLineClamp:1,WebkitBoxOrient:'vertical',maxWidth:'100%',textShadow:'0 1px 2px rgba(0,0,0,0.3)',mb:{xs:0.1,sm:0.2,md:0.2}},children:isTeacherView?booking.student_name:booking.teacher_name}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:{xs:0.2,sm:0.3,md:0.3}},children:[/*#__PURE__*/_jsx(Button,{size:\"small\",variant:\"contained\",onClick:e=>{e.stopPropagation();onViewDetails(booking);},sx:{minWidth:'auto',width:{xs:18,sm:22,md:24},height:{xs:16,sm:20,md:22},p:0,bgcolor:'rgba(255, 255, 255, 0.2)',color:'white',borderRadius:0.5,'&:hover':{bgcolor:'rgba(255, 255, 255, 0.3)'}},children:/*#__PURE__*/_jsx(ViewIcon,{sx:{fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'}}})}),booking.status==='scheduled'&&onCancelBooking&&!isTeacherView&&/*#__PURE__*/_jsx(Button,{size:\"small\",variant:\"contained\",onClick:e=>{e.stopPropagation();onCancelBooking(booking);},sx:{minWidth:'auto',width:{xs:18,sm:22,md:24},height:{xs:16,sm:20,md:22},p:0,bgcolor:'rgba(244, 67, 54, 0.8)',color:'white',borderRadius:0.5,'&:hover':{bgcolor:'rgba(244, 67, 54, 1)'}},children:/*#__PURE__*/_jsx(CancelIcon,{sx:{fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'}}})})]})]},`${booking.id}-${bookingIndex}`);})}):/*#__PURE__*/// Empty slot - show two halves for 30-minute slots\n_jsxs(Box,{sx:{width:'90%',height:{xs:'65px',sm:'85px',md:'105px'},borderRadius:1.5,border:`1px solid ${alpha(theme.palette.grey[300],0.5)}`,display:'flex',flexDirection:'column',overflow:'hidden',zIndex:1},children:[(()=>{const firstHalfAvailable=isHalfHourSlotAvailable(day.key,timeSlot,true);const firstHalfInPast=isHalfHourSlotInPast(day.key,timeSlot,true);return/*#__PURE__*/_jsx(Box,{sx:{flex:1,display:'flex',alignItems:'center',justifyContent:'center',borderBottom:`1px solid ${alpha(theme.palette.grey[300],0.3)}`,bgcolor:firstHalfInPast?alpha(theme.palette.grey[600],0.15):firstHalfAvailable?alpha(theme.palette.success.main,0.08):alpha(theme.palette.grey[100],0.3),transition:'all 0.2s ease'},children:firstHalfInPast?/*#__PURE__*/_jsx(Tooltip,{title:`${timeSlot.hour.toString().padStart(2,'0')}:00 - ${t('bookings.pastSlot','Past Time')}`,arrow:true,children:/*#__PURE__*/_jsx(RadioButtonUncheckedIcon,{sx:{color:theme.palette.grey[600],fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'}}})}):firstHalfAvailable?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(Tooltip,{title:`${timeSlot.hour.toString().padStart(2,'0')}:00 - ${t('bookings.availableSlot','Available')}`,arrow:true,children:/*#__PURE__*/_jsx(CheckCircleIcon,{sx:{color:theme.palette.success.main,fontSize:{xs:'0.9rem',sm:'1rem',md:'1.1rem'},cursor:'pointer'}})}),onTakeBreak&&/*#__PURE__*/_jsx(Tooltip,{title:t('bookings.takeBreak','Take Break'),arrow:true,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleTakeBreak(day.key,timeSlot,true),sx:{color:theme.palette.warning.main,fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'},padding:'2px','&:hover':{bgcolor:alpha(theme.palette.warning.main,0.1)}},children:/*#__PURE__*/_jsx(RestIcon,{sx:{fontSize:'inherit'}})})})]}):/*#__PURE__*/_jsx(RadioButtonUncheckedIcon,{sx:{color:alpha(theme.palette.grey[400],0.5),fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'}}})});})(),(()=>{const secondHalfAvailable=isHalfHourSlotAvailable(day.key,timeSlot,false);const secondHalfInPast=isHalfHourSlotInPast(day.key,timeSlot,false);return/*#__PURE__*/_jsx(Box,{sx:{flex:1,display:'flex',alignItems:'center',justifyContent:'center',bgcolor:secondHalfInPast?alpha(theme.palette.grey[600],0.15):secondHalfAvailable?alpha(theme.palette.success.main,0.08):alpha(theme.palette.grey[100],0.3),transition:'all 0.2s ease'},children:secondHalfInPast?/*#__PURE__*/_jsx(Tooltip,{title:`${timeSlot.hour.toString().padStart(2,'0')}:30${timeSlot.hour===23?'-24:00':`-${(timeSlot.hour+1).toString().padStart(2,'0')}:00`} - ${t('bookings.pastSlot','Past Time')}`,arrow:true,children:/*#__PURE__*/_jsx(RadioButtonUncheckedIcon,{sx:{color:theme.palette.grey[600],fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'}}})}):secondHalfAvailable?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(Tooltip,{title:`${timeSlot.hour.toString().padStart(2,'0')}:30${timeSlot.hour===23?'-24:00':`-${(timeSlot.hour+1).toString().padStart(2,'0')}:00`} - ${t('bookings.availableSlot','Available')}`,arrow:true,children:/*#__PURE__*/_jsx(CheckCircleIcon,{sx:{color:theme.palette.success.main,fontSize:{xs:'0.9rem',sm:'1rem',md:'1.1rem'},cursor:'pointer'}})}),onTakeBreak&&/*#__PURE__*/_jsx(Tooltip,{title:t('bookings.takeBreak','Take Break'),arrow:true,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleTakeBreak(day.key,timeSlot,false),sx:{color:theme.palette.warning.main,fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'},padding:'2px','&:hover':{bgcolor:alpha(theme.palette.warning.main,0.1)}},children:/*#__PURE__*/_jsx(RestIcon,{sx:{fontSize:'inherit'}})})})]}):/*#__PURE__*/_jsx(RadioButtonUncheckedIcon,{sx:{color:alpha(theme.palette.grey[400],0.5),fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'}}})});})()]})]},`${day.key}-${timeSlot.key}`);})]},timeSlot.key);})})]}),/*#__PURE__*/_jsxs(Dialog,{open:breakDialogOpen,onClose:()=>setBreakDialogOpen(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsxs(DialogTitle,{sx:{textAlign:'center',pb:1},children:[/*#__PURE__*/_jsx(RestIcon,{sx:{fontSize:'2rem',color:'warning.main',mb:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",component:\"div\",children:t('bookings.takeBreakTitle','أخذ هذا الوقت راحة')})]}),/*#__PURE__*/_jsx(DialogContent,{sx:{textAlign:'center',py:3},children:selectedBreakSlot&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2},children:t('bookings.takeBreakMessage','هل تريد أخذ هذا الوقت راحة؟')}),/*#__PURE__*/_jsxs(Box,{sx:{bgcolor:alpha(theme.palette.warning.main,0.1),p:2,borderRadius:2,border:`1px solid ${alpha(theme.palette.warning.main,0.3)}`},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{color:'warning.main',mb:1},children:[\"\\uD83D\\uDCC5 \",format(selectedBreakSlot.date,'EEEE, MMM d, yyyy',{locale:isRtl?ar:enUS})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",sx:{fontWeight:'bold'},children:[\"\\uD83D\\uDD50 \",(()=>{const startHour=selectedBreakSlot.hour;const startMinute=selectedBreakSlot.minute;const endMinute=startMinute+30;const endHour=endMinute>=60?startHour+1:startHour;const finalEndMinute=endMinute>=60?endMinute-60:endMinute;return`${startHour.toString().padStart(2,'0')}:${startMinute.toString().padStart(2,'0')} - ${endHour.toString().padStart(2,'0')}:${finalEndMinute.toString().padStart(2,'0')}`;})()]})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:2,color:'text.secondary'},children:t('bookings.takeBreakNote','سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط')})]})}),/*#__PURE__*/_jsxs(DialogActions,{sx:{justifyContent:'center',pb:3},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setBreakDialogOpen(false),variant:\"outlined\",sx:{minWidth:100},children:t('common.cancel','إلغاء')}),/*#__PURE__*/_jsx(Button,{onClick:confirmTakeBreak,variant:\"contained\",color:\"warning\",sx:{minWidth:100,ml:2},children:t('common.confirm','موافق')})]})]})]});};export default WeeklyBookingsTable;", "map": {"version": 3, "names": ["React", "useTranslation", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Chip", "useTheme", "alpha", "CircularProgress", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "Visibility", "ViewIcon", "Cancel", "CancelIcon", "AccessTime", "AccessTimeIcon", "RadioButton<PERSON><PERSON><PERSON>ed", "RadioButtonUncheckedIcon", "CheckCircle", "CheckCircleIcon", "SelfImprovement", "RestIcon", "format", "addDays", "ar", "enUS", "moment", "formatDateInStudentTimezone", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "WeeklyBookingsTable", "_ref", "bookings", "loading", "currentWeekStart", "daysOfWeek", "onViewDetails", "onCancelBooking", "studentProfile", "formatBookingTime", "getStatusColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "availableHours", "onTakeBreak", "weeklyBreaks", "t", "i18n", "theme", "isRtl", "language", "breakDialogOpen", "setBreakDialogOpen", "useState", "selectedBreakSlot", "setSelectedBreakSlot", "handleTakeBreak", "day", "timeSlot", "isFirstHalf", "dayIndex", "daysOfWeekData", "findIndex", "d", "key", "date", "slotMinute", "hour", "minute", "confirmTakeBreak", "isHalfHourSlotInPast", "slotStartTime", "Date", "setHours", "currentTime", "timezone", "currentTimeStr", "toISOString", "error", "console", "isHalfHourSlotAvailable", "<PERSON><PERSON><PERSON>", "toLowerCase", "daySlots", "Array", "isArray", "<PERSON><PERSON><PERSON>", "teacherTimezone", "timezoneMatch", "match", "sign", "hours", "parseInt", "minutes", "offsetMinutes", "slotDateTime", "teacherDateTime", "getTime", "teacherDateStr", "split", "teacherHour", "getUTCHours", "teacher<PERSON><PERSON><PERSON>", "getUTCMinutes", "toString", "padStart", "log", "includes", "slotStartMinutes", "slotEndMinutes", "some", "slot", "startTime", "endTime", "startHour", "startMinute", "map", "Number", "endHour", "endMinute", "availableStartMinutes", "availableEndMinutes", "timeSlots", "midTime", "push", "label", "midLabel", "firstHalf", "secondHalf", "defaultDaysOfWeek", "getAbbreviatedDayName", "abbreviations", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "substring", "getMeetingStatus", "booking", "meetingStartTime", "datetime", "meetingEndTime", "setMinutes", "getMinutes", "duration", "now", "status", "getBookingBackgroundColor", "background", "palette", "success", "main", "dark", "border", "hoverBackground", "grey", "info", "findBookingsForSlot", "dateStr", "filter", "bookingDate", "bookingHour", "bookingMinute", "formattedDateTime", "datePart", "timePart", "hourStr", "minuteStr", "bookingDateTime", "getHours", "isDateMatch", "bookingStartMinutes", "bookingEndMinutes", "startsInThisSlot", "extendsIntoThisSlot", "slotMiddle", "sx", "display", "justifyContent", "my", "children", "length", "elevation", "p", "mb", "borderRadius", "textAlign", "variant", "color", "primary", "alignItems", "flexWrap", "gap", "fontWeight", "opacity", "overflow", "gridTemplateColumns", "xs", "sm", "md", "bgcolor", "borderBottom", "position", "top", "zIndex", "minHeight", "borderRight", "text", "secondary", "fontSize", "index", "dayDate", "flexDirection", "lineHeight", "component", "mt", "locale", "isHourStart", "divider", "placeItems", "left", "transform", "allBookingsInSlot", "bookingsInSlot", "currentSlotStartMinutes", "visibleBookingsMap", "reduce", "acc", "curr", "existing", "visibleBookings", "Object", "values", "right", "height", "boxShadow", "pointerEvents", "bookingIndex", "bookingColors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentSlotEndMinutes", "extendsToNextHour", "width", "cursor", "transition", "onClick", "textOverflow", "WebkitLineClamp", "WebkitBoxOrient", "max<PERSON><PERSON><PERSON>", "textShadow", "student_name", "teacher_name", "size", "e", "stopPropagation", "min<PERSON><PERSON><PERSON>", "id", "firstHalfAvailable", "firstHalfInPast", "flex", "title", "arrow", "warning", "padding", "secondHalfAvailable", "secondHalfInPast", "open", "onClose", "fullWidth", "pb", "py", "finalEndMinute", "ml"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/WeeklyBookingsTable.js"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Chip,\n  useTheme,\n  alpha,\n  CircularProgress,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  IconButton\n} from '@mui/material';\nimport {\n  Visibility as ViewIcon,\n  Cancel as CancelIcon,\n  AccessTime as AccessTimeIcon,\n  RadioButtonUnchecked as RadioButtonUncheckedIcon,\n  CheckCircle as CheckCircleIcon,\n  SelfImprovement as RestIcon\n} from '@mui/icons-material';\nimport { format, addDays } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport moment from 'moment-timezone';\nimport { formatDateInStudentTimezone } from '../utils/timezone';\n\nconst WeeklyBookingsTable = ({\n  bookings,\n  loading = false,\n  currentWeekStart,\n  daysOfWeek,\n  onViewDetails,\n  onCancelBooking,\n  studentProfile,\n  formatBookingTime,\n  getStatusColor,\n  isTeacherView = false,\n  availableHours = null,\n  onTakeBreak = null,\n  weeklyBreaks = []\n}) => {\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  // State for break dialog\n  const [breakDialogOpen, setBreakDialogOpen] = React.useState(false);\n  const [selectedBreakSlot, setSelectedBreakSlot] = React.useState(null);\n\n  // Handle take break\n  const handleTakeBreak = (day, timeSlot, isFirstHalf) => {\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const slotMinute = isFirstHalf ? 0 : 30;\n\n    setSelectedBreakSlot({\n      day,\n      date,\n      hour: timeSlot.hour,\n      minute: slotMinute,\n      isFirstHalf,\n      timeSlot\n    });\n    setBreakDialogOpen(true);\n  };\n\n  const confirmTakeBreak = () => {\n    if (selectedBreakSlot && onTakeBreak) {\n      onTakeBreak(selectedBreakSlot);\n    }\n    setBreakDialogOpen(false);\n    setSelectedBreakSlot(null);\n  };\n\n  // Check if a half-hour slot is in the past (considering teacher's timezone)\n  const isHalfHourSlotInPast = (day, timeSlot, isFirstHalf) => {\n    try {\n      const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n      const date = addDays(currentWeekStart, dayIndex);\n\n      const slotMinute = isFirstHalf ? 0 : 30;\n\n      // Create the slot start datetime\n      const slotStartTime = new Date(date);\n      slotStartTime.setHours(timeSlot.hour, slotMinute, 0, 0);\n\n      // Get current time in teacher's timezone using the same method as the table\n      let currentTime;\n      if (studentProfile && studentProfile.timezone) {\n        // Use teacher's timezone\n        const currentTimeStr = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n        currentTime = new Date(currentTimeStr);\n      } else {\n        // Fallback to browser local time\n        currentTime = new Date();\n      }\n\n      // A slot is considered \"past\" if its start time has already passed\n      // This means the current half-hour slot is also considered past\n      return slotStartTime <= currentTime;\n\n    } catch (error) {\n      console.error('Error checking if slot is in past:', error);\n      return false;\n    }\n  };\n\n  // Check if a specific half-hour slot is available\n  const isHalfHourSlotAvailable = (day, timeSlot, isFirstHalf) => {\n    if (!availableHours || !isTeacherView) return false;\n\n    const dayKey = day.toLowerCase();\n    const daySlots = availableHours[dayKey];\n\n    if (!daySlots || !Array.isArray(daySlots)) return false;\n\n    // Check if this slot is taken as a break\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const slotMinute = isFirstHalf ? 0 : 30;\n\n    // Create break key in teacher's timezone (to match backend format)\n    // The backend sends break keys in teacher's local time, so we need to match that format\n    let breakKey;\n    if (studentProfile && studentProfile.timezone) {\n      // Parse teacher timezone offset (e.g., \"UTC+05:00\")\n      const teacherTimezone = studentProfile.timezone;\n      const timezoneMatch = teacherTimezone.match(/UTC([+-])(\\d{2}):(\\d{2})/);\n\n      if (timezoneMatch) {\n        const sign = timezoneMatch[1] === '+' ? 1 : -1;\n        const hours = parseInt(timezoneMatch[2]);\n        const minutes = parseInt(timezoneMatch[3]);\n        const offsetMinutes = sign * (hours * 60 + minutes);\n\n        // Create UTC datetime for this slot\n        const slotDateTime = new Date(date);\n        slotDateTime.setHours(timeSlot.hour, slotMinute, 0, 0);\n\n        // Convert to teacher's timezone by adding offset\n        const teacherDateTime = new Date(slotDateTime.getTime() + offsetMinutes * 60 * 1000);\n\n        // Use UTC methods to avoid local timezone interference (same as backend)\n        const teacherDateStr = teacherDateTime.toISOString().split('T')[0];\n        const teacherHour = teacherDateTime.getUTCHours();\n        const teacherMinute = teacherDateTime.getUTCMinutes();\n\n        breakKey = `${teacherDateStr}_${teacherHour.toString().padStart(2, '0')}:${teacherMinute.toString().padStart(2, '0')}`;\n      } else {\n        // Fallback if timezone format is not recognized\n        breakKey = `${date.toISOString().split('T')[0]}_${timeSlot.hour.toString().padStart(2, '0')}:${slotMinute.toString().padStart(2, '0')}`;\n      }\n    } else {\n      // Fallback to local time\n      breakKey = `${date.toISOString().split('T')[0]}_${timeSlot.hour.toString().padStart(2, '0')}:${slotMinute.toString().padStart(2, '0')}`;\n    }\n\n    console.log('🔍 FRONTEND CHECK: Checking if slot is break');\n    console.log('  📅 Local Date:', date.toISOString().split('T')[0]);\n    console.log('  ⏰ Local Hour:', timeSlot.hour, 'Minute:', slotMinute);\n    console.log('  🔑 Break key (teacher timezone):', breakKey);\n    console.log('  📋 All weekly breaks:', weeklyBreaks);\n    console.log('  ❓ Is break?', weeklyBreaks.includes(breakKey));\n\n    if (weeklyBreaks.includes(breakKey)) {\n      console.log('  🚫 SLOT IS BREAK - hiding slot');\n      return false; // Slot is taken as break\n    }\n\n    // Calculate the exact 30-minute slot we're checking (using existing slotMinute)\n    const slotStartMinutes = timeSlot.hour * 60 + slotMinute;\n    const slotEndMinutes = slotStartMinutes + 30;\n\n    // Handle edge case for last half hour of the day (23:30-24:00)\n    if (timeSlot.hour === 23 && !isFirstHalf) {\n      // For 23:30-24:00, check if any available slot covers 23:30\n      return daySlots.some(slot => {\n        const [startTime, endTime] = slot.split('-');\n        const [startHour, startMinute] = startTime.split(':').map(Number);\n        let [endHour, endMinute] = endTime.split(':').map(Number);\n\n        // Handle 24:00 or 00:00 as end time\n        if (endHour === 0 || endHour === 24) {\n          endHour = 24;\n          endMinute = 0;\n        }\n\n        const availableStartMinutes = startHour * 60 + startMinute;\n        const availableEndMinutes = endHour * 60 + endMinute;\n\n        // Check if 23:30 is covered\n        return slotStartMinutes >= availableStartMinutes &&\n               slotStartMinutes < availableEndMinutes;\n      });\n    }\n\n    // Check if this specific 30-minute slot is in the available hours\n    return daySlots.some(slot => {\n      const [startTime, endTime] = slot.split('-');\n      const [startHour, startMinute] = startTime.split(':').map(Number);\n      let [endHour, endMinute] = endTime.split(':').map(Number);\n\n      // Handle 24:00 or 00:00 as end time (next day)\n      if (endHour === 0) {\n        endHour = 24;\n        endMinute = 0;\n      }\n\n      const availableStartMinutes = startHour * 60 + startMinute;\n      const availableEndMinutes = endHour * 60 + endMinute;\n\n      // Check if the 30-minute slot fits within the available time range\n      return slotStartMinutes >= availableStartMinutes && slotEndMinutes <= availableEndMinutes;\n    });\n  };\n\n  // Define time slots - full hours from 00:00 to 23:00\n  const timeSlots = [];\n  for (let hour = 0; hour < 24; hour++) {\n    const startTime = `${hour.toString().padStart(2, '0')}:00`;\n    const midTime = `${hour.toString().padStart(2, '0')}:30`;\n    const endTime = hour < 23 ? `${(hour + 1).toString().padStart(2, '0')}:00` : '00:00';\n    timeSlots.push({\n      key: `${startTime}-${endTime}`,\n      label: startTime,\n      midLabel: midTime,\n      hour,\n      minute: 0,\n      // Include both half-hour slots for this hour\n      firstHalf: `${startTime}-${midTime}`,\n      secondHalf: hour < 23 ? `${midTime}-${endTime}` : '23:30-00:00'\n    });\n  }\n\n  // Define days of the week\n  const defaultDaysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n  const daysOfWeekData = daysOfWeek ? daysOfWeek.map(day => ({\n    key: day,\n    label: t(`days.${day}`)\n  })) : defaultDaysOfWeek.map(day => ({\n    key: day,\n    label: t(`days.${day}`)\n  }));\n\n  // Get abbreviated day names for mobile\n  const getAbbreviatedDayName = (dayKey) => {\n    const abbreviations = {\n      sunday: t('days.sundayShort') || 'Sun',\n      monday: t('days.mondayShort') || 'Mon',\n      tuesday: t('days.tuesdayShort') || 'Tue',\n      wednesday: t('days.wednesdayShort') || 'Wed',\n      thursday: t('days.thursdayShort') || 'Thu',\n      friday: t('days.fridayShort') || 'Fri',\n      saturday: t('days.saturdayShort') || 'Sat'\n    };\n    return abbreviations[dayKey] || dayKey.substring(0, 3);\n  };\n\n  // Get meeting status based on current time\n  const getMeetingStatus = (booking) => {\n    const meetingStartTime = new Date(booking.datetime);\n    const meetingEndTime = new Date(booking.datetime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));\n    const now = new Date();\n\n    if (booking.status === 'cancelled') {\n      return 'cancelled';\n    }\n\n    if (now >= meetingStartTime && now < meetingEndTime) {\n      return 'ongoing';\n    }\n\n    if (now >= meetingEndTime) {\n      return 'completed';\n    }\n\n    return 'scheduled';\n  };\n\n  // Get background color based on meeting status\n  const getBookingBackgroundColor = (booking) => {\n    const status = getMeetingStatus(booking);\n\n    switch (status) {\n      case 'ongoing':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.85)} 0%, ${alpha(theme.palette.success.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.95)} 0%, ${alpha(theme.palette.success.dark, 1)} 100%)`\n        };\n      case 'completed':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.85)} 0%, ${alpha(theme.palette.grey[700], 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.grey[500], 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.95)} 0%, ${alpha(theme.palette.grey[700], 1)} 100%)`\n        };\n      case 'cancelled':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.85)} 0%, ${alpha(theme.palette.error.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.95)} 0%, ${alpha(theme.palette.error.dark, 1)} 100%)`\n        };\n      default: // scheduled\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.85)} 0%, ${alpha(theme.palette.info.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.info.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.95)} 0%, ${alpha(theme.palette.info.dark, 1)} 100%)`\n        };\n    }\n  };\n\n  // Find all bookings for specific day and time slot\n  const findBookingsForSlot = (day, timeSlot) => {\n    if (!bookings || !currentWeekStart) return [];\n\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const dateStr = format(date, 'yyyy-MM-dd');\n\n    return bookings.filter(booking => {\n      // Get booking date and time in student timezone\n      let bookingDate, bookingHour, bookingMinute;\n\n      if (studentProfile && studentProfile.timezone) {\n        // Use student timezone for both date and time\n        const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n        const [datePart, timePart] = formattedDateTime.split(' ');\n        bookingDate = datePart;\n\n        const [hourStr, minuteStr] = timePart.split(':');\n        bookingHour = parseInt(hourStr);\n        bookingMinute = parseInt(minuteStr);\n      } else {\n        // Fallback to browser local time\n        const bookingDateTime = new Date(booking.datetime);\n        bookingDate = format(bookingDateTime, 'yyyy-MM-dd');\n        bookingHour = bookingDateTime.getHours();\n        bookingMinute = bookingDateTime.getMinutes();\n      }\n\n      // Check if booking matches this date\n      const isDateMatch = bookingDate === dateStr;\n      if (!isDateMatch) return false;\n\n      // Calculate booking start and end times in minutes from midnight\n      const duration = parseInt(booking.duration) || 25;\n      const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n      const bookingEndMinutes = bookingStartMinutes + duration;\n\n      // Calculate slot start and end times in minutes from midnight\n      const slotStartMinutes = timeSlot.hour * 60;\n      const slotEndMinutes = slotStartMinutes + 60;\n\n      // For 50-minute lessons, show in both starting hour and next hour if it spans\n      if (duration === 50) {\n        // Check if booking starts in this slot\n        const startsInThisSlot = bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotEndMinutes;\n\n        // Check if booking extends into this slot from previous hour\n        const extendsIntoThisSlot = bookingStartMinutes < slotStartMinutes && bookingEndMinutes > slotStartMinutes;\n\n        return startsInThisSlot || extendsIntoThisSlot;\n      } else {\n        // For 25-minute lessons, show in the exact 30-minute slot\n        const slotMiddle = slotStartMinutes + 30;\n\n        // Check if booking is in first half (00-30) or second half (30-60)\n        if (bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle) {\n          // First half booking\n          return bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle;\n        } else if (bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes) {\n          // Second half booking\n          return bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes;\n        }\n      }\n\n      return false;\n    });\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  if (!bookings || bookings.length === 0) {\n    return (\n      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2, textAlign: 'center' }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          {t('bookings.noBookings')}\n        </Typography>\n      </Paper>\n    );\n  }\n\n  return (\n    <Paper elevation={3} sx={{ mb: 4, borderRadius: 2 }}>\n      {/* Header */}\n      <Box sx={{\n        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n        p: 3,\n        color: 'white',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        flexWrap: 'wrap',\n        gap: 2\n      }}>\n        <Box>\n          <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 1 }}>\n            📅 {t('bookings.weeklyTitle')}\n          </Typography>\n          <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n            {t('bookings.weeklyDescription')}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Calendar Table */}\n      <Box sx={{ overflow: 'auto' }}>\n        {/* Days Header */}\n        <Box sx={{\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '60px repeat(7, minmax(80px, 1fr))',\n            sm: '80px repeat(7, minmax(100px, 1fr))',\n            md: '120px repeat(7, minmax(120px, 1fr))',\n          },\n          bgcolor: alpha(theme.palette.primary.main, 0.05),\n          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          position: 'sticky',\n          top: 0,\n          zIndex: 10\n        }}>\n          <Box sx={{\n            p: { xs: 1.5, sm: 2, md: 2.5 },\n            minHeight: { xs: '60px', sm: '75px', md: '90px' },\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`\n          }}>\n            <Typography variant=\"body2\" sx={{\n              fontWeight: 'bold',\n              color: theme.palette.text.secondary,\n              fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' }\n            }}>\n              ⏰ {t('teacher.time')}\n            </Typography>\n          </Box>\n          {daysOfWeekData.map((day, index) => {\n            // Calculate the date for this day\n            const dayDate = currentWeekStart ? addDays(currentWeekStart, index) : new Date();\n            \n            return (\n              <Box\n                key={day.key}\n                sx={{\n                  p: { xs: 1.5, sm: 2, md: 2.5 },\n                  minHeight: { xs: '60px', sm: '75px', md: '90px' },\n                  textAlign: 'center',\n                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                  '&:last-child': { borderRight: 'none' },\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center',\n                  alignItems: 'center'\n                }}\n              >\n                <Typography variant=\"h6\" sx={{\n                  fontWeight: 'bold',\n                  color: theme.palette.primary.main,\n                  fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' },\n                  lineHeight: 1.2\n                }}>\n                  {/* Day name */}\n                  <Box component=\"span\" sx={{ display: { xs: 'none', md: 'block' } }}>\n                    {day.label}\n                  </Box>\n                  <Box component=\"span\" sx={{ display: { xs: 'none', sm: 'block', md: 'none' } }}>\n                    {day.label.length > 6 ? day.label.substring(0, 6) : day.label}\n                  </Box>\n                  <Box component=\"span\" sx={{ display: { xs: 'block', sm: 'none' } }}>\n                    {getAbbreviatedDayName(day.key)}\n                  </Box>\n                </Typography>\n                \n                {/* Date */}\n                <Typography variant=\"caption\" sx={{\n                  display: 'block',\n                  color: theme.palette.text.secondary,\n                  fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' },\n                  mt: 0.5\n                }}>\n                  {format(dayDate, 'MMM d', { locale: isRtl ? ar : enUS })}\n                </Typography>\n              </Box>\n            );\n          })}\n        </Box>\n\n        {/* Time Slots Grid */}\n        <Box>\n          {timeSlots.map((timeSlot, index) => {\n            const isHourStart = timeSlot.minute === 0;\n            return (\n              <Box\n                key={timeSlot.key}\n                sx={{\n                  display: 'grid',\n                  gridTemplateColumns: {\n                    xs: '60px repeat(7, minmax(80px, 1fr))',\n                    sm: '80px repeat(7, minmax(100px, 1fr))',\n                    md: '120px repeat(7, minmax(120px, 1fr))',\n                  },\n                  borderBottom: `1px solid ${alpha(theme.palette.divider, isHourStart ? 0.3 : 0.1)}`,\n                  bgcolor: index % 4 < 2 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',\n                  '&:hover': {\n                    bgcolor: alpha(theme.palette.primary.main, 0.05)\n                  }\n                }}\n              >\n                {/* Time Labels */}\n                <Box sx={{\n                  p: { xs: 0.5, sm: 1, md: 1.5 },\n                  display: 'grid',\n                  placeItems: 'center',\n                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                  bgcolor: isHourStart ? alpha(theme.palette.primary.main, 0.05) : 'transparent',\n                  position: 'relative'\n                }}>\n                  <Box sx={{ \n                    display: 'flex', \n                    flexDirection: 'column', \n                    alignItems: 'center', \n                    gap: 0.5,\n                    position: 'absolute',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    top: timeSlot.minute === 0 ? '-8px' : 'calc(50% + 20px)'\n                  }}\n                  >\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        fontWeight: 'bold',\n                        color: theme.palette.primary.main,\n                        fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' }\n                      }}\n                    >\n                      {timeSlot.label}\n                    </Typography>\n                  </Box>\n                </Box>\n\n                {/* Day Cells */}\n                {daysOfWeekData.map((day) => {\n                  const allBookingsInSlot = findBookingsForSlot(day.key, timeSlot);\n\n                  // Filter out extended bookings from previous hour\n                  // Filter out extended bookings from previous hour\n          const bookingsInSlot = allBookingsInSlot.filter(booking => {\n                    // Get booking start time to determine if it's extended from previous hour\n                    let bookingHour = 0;\n                    let bookingMinute = 0;\n\n                    if (studentProfile && studentProfile.timezone) {\n                      const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n                      const [, timePart] = formattedDateTime.split(' ');\n                      const [hourStr, minuteStr] = timePart.split(':');\n                      bookingHour = parseInt(hourStr);\n                      bookingMinute = parseInt(minuteStr);\n                    } else {\n                      const bookingDate = new Date(booking.datetime);\n                      bookingHour = bookingDate.getHours();\n                      bookingMinute = bookingDate.getMinutes();\n                    }\n\n                    const duration = parseInt(booking.duration) || 25;\n                    const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n                    const currentSlotStartMinutes = timeSlot.hour * 60;\n\n                    // Only show bookings that start in this slot or earlier in the same hour\n                    // Don't show bookings that extend from previous hour\n                    if (duration === 50) {\n                      const extendsIntoThisSlot = bookingStartMinutes < currentSlotStartMinutes;\n                      return !extendsIntoThisSlot; // Filter out extended bookings\n                    }\n\n                    return true; // Show all 25-minute bookings\n                  });\n\n                  // Deduplicate overlapping bookings that share the exact same start time (datetime)\n                  // If a non-cancelled booking and a cancelled booking overlap, keep the non-cancelled one\n                  // so the student sees the active reservation instead of the cancelled one.\n                  const visibleBookingsMap = bookingsInSlot.reduce((acc, curr) => {\n                    const key = curr.datetime; // ISO string coming from backend\n                    const existing = acc[key];\n\n                    if (!existing) {\n                      acc[key] = curr; // first occurrence\n                    } else {\n                      // If the existing booking is cancelled and the new one is not, replace it\n                      if (existing.status === 'cancelled' && curr.status !== 'cancelled') {\n                        acc[key] = curr;\n                      }\n                      // If both have same non-cancelled status keep the earlier inserted one\n                      // otherwise, leave as is.\n                    }\n                    return acc;\n                  }, {});\n\n                  const visibleBookings = Object.values(visibleBookingsMap);\n\n                  return (\n                    <Box\n                      key={`${day.key}-${timeSlot.key}`}\n                      sx={{\n                        p: { xs: 0.2, sm: 0.4, md: 0.6 },\n                        minHeight: { xs: '80px', sm: '100px', md: '120px' }, // Increased height for better content fit\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                        '&:last-child': { borderRight: 'none' },\n                        position: 'relative'\n                      }}\n                    >\n                      {/* Continuous hour line */}\n                      <Box\n                        sx={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          right: 0,\n                          height: '4px',\n                          bgcolor: theme.palette.primary.main,\n                          zIndex: 0,\n                          borderRadius: '2px',\n                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                          pointerEvents: 'none'\n                        }}\n                      />\n                      {/* Lighter half-hour line */}\n                      <Box\n                        sx={{\n                          position: 'absolute',\n                          top: '50%',\n                          left: 0,\n                          right: 0,\n                          height: '2px',\n                          bgcolor: alpha(theme.palette.divider, 0.3), // More transparent\n                          zIndex: 0, // Lower z-index to stay behind bookings\n                          borderRadius: '1px'\n                        }}\n                      />\n\n                      {visibleBookings.length > 0 ? (\n                        <>\n                          {visibleBookings.map((booking, bookingIndex) => {\n                            const bookingColors = getBookingBackgroundColor(booking);\n                            const isFullLesson = parseInt(booking.duration) === 50;\n\n                            // Get booking start time to determine position\n                            let bookingMinute = 0;\n                            let bookingHour = 0;\n                            if (studentProfile && studentProfile.timezone) {\n                              const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n                              const [, timePart] = formattedDateTime.split(' ');\n                              const [hourStr, minuteStr] = timePart.split(':');\n                              bookingHour = parseInt(hourStr);\n                              bookingMinute = parseInt(minuteStr);\n                            } else {\n                              const bookingDate = new Date(booking.datetime);\n                              bookingHour = bookingDate.getHours();\n                              bookingMinute = bookingDate.getMinutes();\n                            }\n\n                            // For full lessons, determine which half of this slot the booking occupies\n                            // For half lessons, show in correct half based on start time\n                            let isFirstHalf = bookingMinute === 0;\n\n                            // For full lessons, determine the visual representation\n                            if (isFullLesson) {\n                              // Calculate if this booking spans into the next hour\n                              const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n                              const bookingEndMinutes = bookingStartMinutes + 50; // 50-minute lesson\n                              const currentSlotStartMinutes = timeSlot.hour * 60;\n                              const currentSlotEndMinutes = currentSlotStartMinutes + 60;\n\n                              // Check if booking starts in this slot\n                              const startsInThisSlot = bookingStartMinutes >= currentSlotStartMinutes && bookingStartMinutes < currentSlotEndMinutes;\n\n                              if (startsInThisSlot) {\n                                // Booking starts in this slot\n                                const extendsToNextHour = bookingEndMinutes > currentSlotEndMinutes;\n                                if (extendsToNextHour) {\n                                  // This is a cross-hour booking, show it spanning from current position to next hour\n                                  isFirstHalf = 'spanning'; // Special case for spanning bookings\n                                } else {\n                                  // Regular full lesson within one hour - don't override first half bookings\n                                  isFirstHalf = bookingMinute === 0 ? null : 'secondHalfFull';\n                                }\n                              }\n                            }\n\n                            return (\n                              <Box\n                                key={`${booking.id}-${bookingIndex}`}\n                                sx={{\n                                  width: '90%',\n                                  height: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      // For cross-hour bookings, extend from second half to next hour\n                                      return '100%'; // Extend to cover both halves visually\n                                    }\n                                    if (isFirstHalf === 'secondHalfFull') {\n                                      // Full lesson starting from second half - only show in second half\n                                      return '40%';\n                                    }\n\n                                    return isFullLesson ? '95%' : '40%';\n                                  })(),\n                                  position: 'absolute',\n                                  top: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      // Start from the second half of current slot\n                                      return '50%';\n                                    }\n                                    if (isFirstHalf === 'secondHalfFull') {\n                                      // Position in second half only\n                                      return '55%';\n                                    }\n\n                                    return isFullLesson ? '2.5%' : (isFirstHalf ? '5%' : '55%');\n                                  })(),\n                                  left: '5%',\n                                  zIndex: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      return 100; // Very high z-index for spanning bookings\n                                    }\n\n                                    return 10 + bookingIndex; // Stacked z-index for multiple bookings\n                                  })(),\n                                  borderRadius: 1,\n                                  background: bookingColors.background,\n                                  display: 'flex',\n                                  flexDirection: 'column',\n                                  alignItems: 'center',\n                                  justifyContent: 'center',\n                                  gap: { xs: 0.2, sm: 0.3, md: 0.4 },\n                                  p: { xs: 0.2, sm: 0.3, md: 0.4 },\n                                  cursor: 'pointer',\n                                  border: bookingColors.border,\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                                  '&:hover': {\n                                    background: bookingColors.hoverBackground,\n                                    transform: 'translateY(-1px)',\n                                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                                  },\n                                  transition: 'all 0.2s ease-in-out',\n                                  overflow: 'hidden'\n                                }}\n                                onClick={() => onViewDetails(booking)}\n                              >\n\n\n                                <Typography\n                                  variant=\"body2\"\n                                  sx={{\n                                    color: 'white',\n                                    fontWeight: 'bold',\n                                    fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.75rem' },\n                                    textAlign: 'center',\n                                    lineHeight: 1.1,\n                                    overflow: 'hidden',\n                                    textOverflow: 'ellipsis',\n                                    display: '-webkit-box',\n                                    WebkitLineClamp: 1,\n                                    WebkitBoxOrient: 'vertical',\n                                    maxWidth: '100%',\n                                    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n                                    mb: { xs: 0.1, sm: 0.2, md: 0.2 }\n                                  }}\n                                >\n                                  {isTeacherView ? booking.student_name : booking.teacher_name}\n                                </Typography>\n\n                                <Box sx={{ display: 'flex', gap: { xs: 0.2, sm: 0.3, md: 0.3 } }}>\n                                  <Button\n                                    size=\"small\"\n                                    variant=\"contained\"\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      onViewDetails(booking);\n                                    }}\n                                    sx={{\n                                      minWidth: 'auto',\n                                      width: { xs: 18, sm: 22, md: 24 },\n                                      height: { xs: 16, sm: 20, md: 22 },\n                                      p: 0,\n                                      bgcolor: 'rgba(255, 255, 255, 0.2)',\n                                      color: 'white',\n                                      borderRadius: 0.5,\n                                      '&:hover': {\n                                        bgcolor: 'rgba(255, 255, 255, 0.3)',\n                                      }\n                                    }}\n                                  >\n                                    <ViewIcon sx={{ fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' } }} />\n                                  </Button>\n                                  {booking.status === 'scheduled' && onCancelBooking && !isTeacherView && (\n                                    <Button\n                                      size=\"small\"\n                                      variant=\"contained\"\n                                      onClick={(e) => {\n                                        e.stopPropagation();\n                                        onCancelBooking(booking);\n                                      }}\n                                      sx={{\n                                        minWidth: 'auto',\n                                        width: { xs: 18, sm: 22, md: 24 },\n                                        height: { xs: 16, sm: 20, md: 22 },\n                                        p: 0,\n                                        bgcolor: 'rgba(244, 67, 54, 0.8)',\n                                        color: 'white',\n                                        borderRadius: 0.5,\n                                        '&:hover': {\n                                          bgcolor: 'rgba(244, 67, 54, 1)',\n                                        }\n                                      }}\n                                    >\n                                      <CancelIcon sx={{ fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' } }} />\n                                    </Button>\n                                  )}\n                                </Box>\n                              </Box>\n                            );\n                          })}\n                        </>\n                      ) : (\n                        // Empty slot - show two halves for 30-minute slots\n                        <Box sx={{\n                          width: '90%',\n                          height: { xs: '65px', sm: '85px', md: '105px' },\n                          borderRadius: 1.5,\n                          border: `1px solid ${alpha(theme.palette.grey[300], 0.5)}`,\n                          display: 'flex',\n                          flexDirection: 'column',\n                          overflow: 'hidden',\n                          zIndex: 1\n                        }}>\n                          {/* First Half (00-30 minutes) */}\n                          {(() => {\n                            const firstHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, true);\n                            const firstHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, true);\n\n                            return (\n                              <Box sx={{\n                                flex: 1,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                borderBottom: `1px solid ${alpha(theme.palette.grey[300], 0.3)}`,\n                                bgcolor: firstHalfInPast\n                                  ? alpha(theme.palette.grey[600], 0.15)\n                                  : firstHalfAvailable\n                                    ? alpha(theme.palette.success.main, 0.08)\n                                    : alpha(theme.palette.grey[100], 0.3),\n                                transition: 'all 0.2s ease'\n                              }}>\n                                {firstHalfInPast ? (\n                                  <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.pastSlot', 'Past Time')}`} arrow>\n                                    <RadioButtonUncheckedIcon\n                                      sx={{\n                                        color: theme.palette.grey[600],\n                                        fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                      }}\n                                    />\n                                  </Tooltip>\n                                ) : firstHalfAvailable ? (\n                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                                    <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.availableSlot', 'Available')}`} arrow>\n                                      <CheckCircleIcon\n                                        sx={{\n                                          color: theme.palette.success.main,\n                                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },\n                                          cursor: 'pointer'\n                                        }}\n                                      />\n                                    </Tooltip>\n                                    {onTakeBreak && (\n                                      <Tooltip title={t('bookings.takeBreak', 'Take Break')} arrow>\n                                        <IconButton\n                                          size=\"small\"\n                                          onClick={() => handleTakeBreak(day.key, timeSlot, true)}\n                                          sx={{\n                                            color: theme.palette.warning.main,\n                                            fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },\n                                            padding: '2px',\n                                            '&:hover': {\n                                              bgcolor: alpha(theme.palette.warning.main, 0.1)\n                                            }\n                                          }}\n                                        >\n                                          <RestIcon sx={{ fontSize: 'inherit' }} />\n                                        </IconButton>\n                                      </Tooltip>\n                                    )}\n                                  </Box>\n                                ) : (\n                                  <RadioButtonUncheckedIcon\n                                    sx={{\n                                      color: alpha(theme.palette.grey[400], 0.5),\n                                      fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                    }}\n                                  />\n                                )}\n                              </Box>\n                            );\n                          })()}\n\n                          {/* Second Half (30-60 minutes) */}\n                          {(() => {\n                            const secondHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, false);\n                            const secondHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, false);\n\n                            return (\n                              <Box sx={{\n                                flex: 1,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                bgcolor: secondHalfInPast\n                                  ? alpha(theme.palette.grey[600], 0.15)\n                                  : secondHalfAvailable\n                                    ? alpha(theme.palette.success.main, 0.08)\n                                    : alpha(theme.palette.grey[100], 0.3),\n                                transition: 'all 0.2s ease'\n                              }}>\n                                {secondHalfInPast ? (\n                                  <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.pastSlot', 'Past Time')}`} arrow>\n                                    <RadioButtonUncheckedIcon\n                                      sx={{\n                                        color: theme.palette.grey[600],\n                                        fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                      }}\n                                    />\n                                  </Tooltip>\n                                ) : secondHalfAvailable ? (\n                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                                    <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.availableSlot', 'Available')}`} arrow>\n                                      <CheckCircleIcon\n                                        sx={{\n                                          color: theme.palette.success.main,\n                                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },\n                                          cursor: 'pointer'\n                                        }}\n                                      />\n                                    </Tooltip>\n                                    {onTakeBreak && (\n                                      <Tooltip title={t('bookings.takeBreak', 'Take Break')} arrow>\n                                        <IconButton\n                                          size=\"small\"\n                                          onClick={() => handleTakeBreak(day.key, timeSlot, false)}\n                                          sx={{\n                                            color: theme.palette.warning.main,\n                                            fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },\n                                            padding: '2px',\n                                            '&:hover': {\n                                              bgcolor: alpha(theme.palette.warning.main, 0.1)\n                                            }\n                                          }}\n                                        >\n                                          <RestIcon sx={{ fontSize: 'inherit' }} />\n                                        </IconButton>\n                                      </Tooltip>\n                                    )}\n                                  </Box>\n                                ) : (\n                                  <RadioButtonUncheckedIcon\n                                    sx={{\n                                      color: alpha(theme.palette.grey[400], 0.5),\n                                      fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                    }}\n                                  />\n                                )}\n                              </Box>\n                            );\n                          })()}\n                        </Box>\n                      )}\n                    </Box>\n                  );\n                })}\n              </Box>\n            );\n          })}\n        </Box>\n      </Box>\n\n      {/* Take Break Dialog */}\n      <Dialog\n        open={breakDialogOpen}\n        onClose={() => setBreakDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>\n          <RestIcon sx={{ fontSize: '2rem', color: 'warning.main', mb: 1 }} />\n          <Typography variant=\"h6\" component=\"div\">\n            {t('bookings.takeBreakTitle', 'أخذ هذا الوقت راحة')}\n          </Typography>\n        </DialogTitle>\n\n        <DialogContent sx={{ textAlign: 'center', py: 3 }}>\n          {selectedBreakSlot && (\n            <Box>\n              <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                {t('bookings.takeBreakMessage', 'هل تريد أخذ هذا الوقت راحة؟')}\n              </Typography>\n\n              <Box sx={{\n                bgcolor: alpha(theme.palette.warning.main, 0.1),\n                p: 2,\n                borderRadius: 2,\n                border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`\n              }}>\n                <Typography variant=\"h6\" sx={{ color: 'warning.main', mb: 1 }}>\n                  📅 {format(selectedBreakSlot.date, 'EEEE, MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                </Typography>\n                <Typography variant=\"h5\" sx={{ fontWeight: 'bold' }}>\n                  🕐 {(() => {\n                    const startHour = selectedBreakSlot.hour;\n                    const startMinute = selectedBreakSlot.minute;\n                    const endMinute = startMinute + 30;\n                    const endHour = endMinute >= 60 ? startHour + 1 : startHour;\n                    const finalEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;\n\n                    return `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${finalEndMinute.toString().padStart(2, '0')}`;\n                  })()}\n                </Typography>\n              </Box>\n\n              <Typography variant=\"body2\" sx={{ mt: 2, color: 'text.secondary' }}>\n                {t('bookings.takeBreakNote', 'سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط')}\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n\n        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>\n          <Button\n            onClick={() => setBreakDialogOpen(false)}\n            variant=\"outlined\"\n            sx={{ minWidth: 100 }}\n          >\n            {t('common.cancel', 'إلغاء')}\n          </Button>\n          <Button\n            onClick={confirmTakeBreak}\n            variant=\"contained\"\n            color=\"warning\"\n            sx={{ minWidth: 100, ml: 2 }}\n          >\n            {t('common.confirm', 'موافق')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Paper>\n  );\n};\n\nexport default WeeklyBookingsTable;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,KAAK,CACLC,gBAAgB,CAChBC,OAAO,CACPC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,UAAU,KACL,eAAe,CACtB,OACEC,UAAU,GAAI,CAAAC,QAAQ,CACtBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,oBAAoB,GAAI,CAAAC,wBAAwB,CAChDC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,eAAe,GAAI,CAAAC,QAAQ,KACtB,qBAAqB,CAC5B,OAASC,MAAM,CAAEC,OAAO,KAAQ,UAAU,CAC1C,OAASC,EAAE,CAAEC,IAAI,KAAQ,iBAAiB,CAC1C,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CACpC,OAASC,2BAA2B,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEhE,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EActB,IAduB,CAC3BC,QAAQ,CACRC,OAAO,CAAG,KAAK,CACfC,gBAAgB,CAChBC,UAAU,CACVC,aAAa,CACbC,eAAe,CACfC,cAAc,CACdC,iBAAiB,CACjBC,cAAc,CACdC,aAAa,CAAG,KAAK,CACrBC,cAAc,CAAG,IAAI,CACrBC,WAAW,CAAG,IAAI,CAClBC,YAAY,CAAG,EACjB,CAAC,CAAAb,IAAA,CACC,KAAM,CAAEc,CAAC,CAAEC,IAAK,CAAC,CAAGvD,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAwD,KAAK,CAAGlD,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAmD,KAAK,CAAGF,IAAI,CAACG,QAAQ,GAAK,IAAI,CAEpC;AACA,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAG7D,KAAK,CAAC8D,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhE,KAAK,CAAC8D,QAAQ,CAAC,IAAI,CAAC,CAEtE;AACA,KAAM,CAAAG,eAAe,CAAGA,CAACC,GAAG,CAAEC,QAAQ,CAAEC,WAAW,GAAK,CACtD,KAAM,CAAAC,QAAQ,CAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKP,GAAG,CAAC,CAC7D,KAAM,CAAAQ,IAAI,CAAG7C,OAAO,CAACe,gBAAgB,CAAEyB,QAAQ,CAAC,CAChD,KAAM,CAAAM,UAAU,CAAGP,WAAW,CAAG,CAAC,CAAG,EAAE,CAEvCJ,oBAAoB,CAAC,CACnBE,GAAG,CACHQ,IAAI,CACJE,IAAI,CAAET,QAAQ,CAACS,IAAI,CACnBC,MAAM,CAAEF,UAAU,CAClBP,WAAW,CACXD,QACF,CAAC,CAAC,CACFN,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAiB,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAIf,iBAAiB,EAAIV,WAAW,CAAE,CACpCA,WAAW,CAACU,iBAAiB,CAAC,CAChC,CACAF,kBAAkB,CAAC,KAAK,CAAC,CACzBG,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAe,oBAAoB,CAAGA,CAACb,GAAG,CAAEC,QAAQ,CAAEC,WAAW,GAAK,CAC3D,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKP,GAAG,CAAC,CAC7D,KAAM,CAAAQ,IAAI,CAAG7C,OAAO,CAACe,gBAAgB,CAAEyB,QAAQ,CAAC,CAEhD,KAAM,CAAAM,UAAU,CAAGP,WAAW,CAAG,CAAC,CAAG,EAAE,CAEvC;AACA,KAAM,CAAAY,aAAa,CAAG,GAAI,CAAAC,IAAI,CAACP,IAAI,CAAC,CACpCM,aAAa,CAACE,QAAQ,CAACf,QAAQ,CAACS,IAAI,CAAED,UAAU,CAAE,CAAC,CAAE,CAAC,CAAC,CAEvD;AACA,GAAI,CAAAQ,WAAW,CACf,GAAInC,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C;AACA,KAAM,CAAAC,cAAc,CAAGpD,2BAA2B,CAAC,GAAI,CAAAgD,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CAAEtC,cAAc,CAACoC,QAAQ,CAAE,qBAAqB,CAAC,CAC5HD,WAAW,CAAG,GAAI,CAAAF,IAAI,CAACI,cAAc,CAAC,CACxC,CAAC,IAAM,CACL;AACAF,WAAW,CAAG,GAAI,CAAAF,IAAI,CAAC,CAAC,CAC1B,CAEA;AACA;AACA,MAAO,CAAAD,aAAa,EAAIG,WAAW,CAErC,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAE,uBAAuB,CAAGA,CAACvB,GAAG,CAAEC,QAAQ,CAAEC,WAAW,GAAK,CAC9D,GAAI,CAAChB,cAAc,EAAI,CAACD,aAAa,CAAE,MAAO,MAAK,CAEnD,KAAM,CAAAuC,MAAM,CAAGxB,GAAG,CAACyB,WAAW,CAAC,CAAC,CAChC,KAAM,CAAAC,QAAQ,CAAGxC,cAAc,CAACsC,MAAM,CAAC,CAEvC,GAAI,CAACE,QAAQ,EAAI,CAACC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,CAAE,MAAO,MAAK,CAEvD;AACA,KAAM,CAAAvB,QAAQ,CAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKP,GAAG,CAAC,CAC7D,KAAM,CAAAQ,IAAI,CAAG7C,OAAO,CAACe,gBAAgB,CAAEyB,QAAQ,CAAC,CAChD,KAAM,CAAAM,UAAU,CAAGP,WAAW,CAAG,CAAC,CAAG,EAAE,CAEvC;AACA;AACA,GAAI,CAAA2B,QAAQ,CACZ,GAAI/C,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C;AACA,KAAM,CAAAY,eAAe,CAAGhD,cAAc,CAACoC,QAAQ,CAC/C,KAAM,CAAAa,aAAa,CAAGD,eAAe,CAACE,KAAK,CAAC,0BAA0B,CAAC,CAEvE,GAAID,aAAa,CAAE,CACjB,KAAM,CAAAE,IAAI,CAAGF,aAAa,CAAC,CAAC,CAAC,GAAK,GAAG,CAAG,CAAC,CAAG,CAAC,CAAC,CAC9C,KAAM,CAAAG,KAAK,CAAGC,QAAQ,CAACJ,aAAa,CAAC,CAAC,CAAC,CAAC,CACxC,KAAM,CAAAK,OAAO,CAAGD,QAAQ,CAACJ,aAAa,CAAC,CAAC,CAAC,CAAC,CAC1C,KAAM,CAAAM,aAAa,CAAGJ,IAAI,EAAIC,KAAK,CAAG,EAAE,CAAGE,OAAO,CAAC,CAEnD;AACA,KAAM,CAAAE,YAAY,CAAG,GAAI,CAAAvB,IAAI,CAACP,IAAI,CAAC,CACnC8B,YAAY,CAACtB,QAAQ,CAACf,QAAQ,CAACS,IAAI,CAAED,UAAU,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtD;AACA,KAAM,CAAA8B,eAAe,CAAG,GAAI,CAAAxB,IAAI,CAACuB,YAAY,CAACE,OAAO,CAAC,CAAC,CAAGH,aAAa,CAAG,EAAE,CAAG,IAAI,CAAC,CAEpF;AACA,KAAM,CAAAI,cAAc,CAAGF,eAAe,CAACnB,WAAW,CAAC,CAAC,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAClE,KAAM,CAAAC,WAAW,CAAGJ,eAAe,CAACK,WAAW,CAAC,CAAC,CACjD,KAAM,CAAAC,aAAa,CAAGN,eAAe,CAACO,aAAa,CAAC,CAAC,CAErDjB,QAAQ,CAAG,GAAGY,cAAc,IAAIE,WAAW,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIH,aAAa,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACxH,CAAC,IAAM,CACL;AACAnB,QAAQ,CAAG,GAAGrB,IAAI,CAACY,WAAW,CAAC,CAAC,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIzC,QAAQ,CAACS,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIvC,UAAU,CAACsC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACzI,CACF,CAAC,IAAM,CACL;AACAnB,QAAQ,CAAG,GAAGrB,IAAI,CAACY,WAAW,CAAC,CAAC,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIzC,QAAQ,CAACS,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIvC,UAAU,CAACsC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACzI,CAEA1B,OAAO,CAAC2B,GAAG,CAAC,8CAA8C,CAAC,CAC3D3B,OAAO,CAAC2B,GAAG,CAAC,kBAAkB,CAAEzC,IAAI,CAACY,WAAW,CAAC,CAAC,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACjEpB,OAAO,CAAC2B,GAAG,CAAC,iBAAiB,CAAEhD,QAAQ,CAACS,IAAI,CAAE,SAAS,CAAED,UAAU,CAAC,CACpEa,OAAO,CAAC2B,GAAG,CAAC,oCAAoC,CAAEpB,QAAQ,CAAC,CAC3DP,OAAO,CAAC2B,GAAG,CAAC,yBAAyB,CAAE7D,YAAY,CAAC,CACpDkC,OAAO,CAAC2B,GAAG,CAAC,eAAe,CAAE7D,YAAY,CAAC8D,QAAQ,CAACrB,QAAQ,CAAC,CAAC,CAE7D,GAAIzC,YAAY,CAAC8D,QAAQ,CAACrB,QAAQ,CAAC,CAAE,CACnCP,OAAO,CAAC2B,GAAG,CAAC,kCAAkC,CAAC,CAC/C,MAAO,MAAK,CAAE;AAChB,CAEA;AACA,KAAM,CAAAE,gBAAgB,CAAGlD,QAAQ,CAACS,IAAI,CAAG,EAAE,CAAGD,UAAU,CACxD,KAAM,CAAA2C,cAAc,CAAGD,gBAAgB,CAAG,EAAE,CAE5C;AACA,GAAIlD,QAAQ,CAACS,IAAI,GAAK,EAAE,EAAI,CAACR,WAAW,CAAE,CACxC;AACA,MAAO,CAAAwB,QAAQ,CAAC2B,IAAI,CAACC,IAAI,EAAI,CAC3B,KAAM,CAACC,SAAS,CAAEC,OAAO,CAAC,CAAGF,IAAI,CAACZ,KAAK,CAAC,GAAG,CAAC,CAC5C,KAAM,CAACe,SAAS,CAAEC,WAAW,CAAC,CAAGH,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAACiB,GAAG,CAACC,MAAM,CAAC,CACjE,GAAI,CAACC,OAAO,CAAEC,SAAS,CAAC,CAAGN,OAAO,CAACd,KAAK,CAAC,GAAG,CAAC,CAACiB,GAAG,CAACC,MAAM,CAAC,CAEzD;AACA,GAAIC,OAAO,GAAK,CAAC,EAAIA,OAAO,GAAK,EAAE,CAAE,CACnCA,OAAO,CAAG,EAAE,CACZC,SAAS,CAAG,CAAC,CACf,CAEA,KAAM,CAAAC,qBAAqB,CAAGN,SAAS,CAAG,EAAE,CAAGC,WAAW,CAC1D,KAAM,CAAAM,mBAAmB,CAAGH,OAAO,CAAG,EAAE,CAAGC,SAAS,CAEpD;AACA,MAAO,CAAAX,gBAAgB,EAAIY,qBAAqB,EACzCZ,gBAAgB,CAAGa,mBAAmB,CAC/C,CAAC,CAAC,CACJ,CAEA;AACA,MAAO,CAAAtC,QAAQ,CAAC2B,IAAI,CAACC,IAAI,EAAI,CAC3B,KAAM,CAACC,SAAS,CAAEC,OAAO,CAAC,CAAGF,IAAI,CAACZ,KAAK,CAAC,GAAG,CAAC,CAC5C,KAAM,CAACe,SAAS,CAAEC,WAAW,CAAC,CAAGH,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAACiB,GAAG,CAACC,MAAM,CAAC,CACjE,GAAI,CAACC,OAAO,CAAEC,SAAS,CAAC,CAAGN,OAAO,CAACd,KAAK,CAAC,GAAG,CAAC,CAACiB,GAAG,CAACC,MAAM,CAAC,CAEzD;AACA,GAAIC,OAAO,GAAK,CAAC,CAAE,CACjBA,OAAO,CAAG,EAAE,CACZC,SAAS,CAAG,CAAC,CACf,CAEA,KAAM,CAAAC,qBAAqB,CAAGN,SAAS,CAAG,EAAE,CAAGC,WAAW,CAC1D,KAAM,CAAAM,mBAAmB,CAAGH,OAAO,CAAG,EAAE,CAAGC,SAAS,CAEpD;AACA,MAAO,CAAAX,gBAAgB,EAAIY,qBAAqB,EAAIX,cAAc,EAAIY,mBAAmB,CAC3F,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAG,EAAE,CACpB,IAAK,GAAI,CAAAvD,IAAI,CAAG,CAAC,CAAEA,IAAI,CAAG,EAAE,CAAEA,IAAI,EAAE,CAAE,CACpC,KAAM,CAAA6C,SAAS,CAAG,GAAG7C,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,CAC1D,KAAM,CAAAkB,OAAO,CAAG,GAAGxD,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,CACxD,KAAM,CAAAQ,OAAO,CAAG9C,IAAI,CAAG,EAAE,CAAG,GAAG,CAACA,IAAI,CAAG,CAAC,EAAEqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,CAAG,OAAO,CACpFiB,SAAS,CAACE,IAAI,CAAC,CACb5D,GAAG,CAAE,GAAGgD,SAAS,IAAIC,OAAO,EAAE,CAC9BY,KAAK,CAAEb,SAAS,CAChBc,QAAQ,CAAEH,OAAO,CACjBxD,IAAI,CACJC,MAAM,CAAE,CAAC,CACT;AACA2D,SAAS,CAAE,GAAGf,SAAS,IAAIW,OAAO,EAAE,CACpCK,UAAU,CAAE7D,IAAI,CAAG,EAAE,CAAG,GAAGwD,OAAO,IAAIV,OAAO,EAAE,CAAG,aACpD,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAgB,iBAAiB,CAAG,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CACxG,KAAM,CAAApE,cAAc,CAAGzB,UAAU,CAAGA,UAAU,CAACgF,GAAG,CAAC3D,GAAG,GAAK,CACzDO,GAAG,CAAEP,GAAG,CACRoE,KAAK,CAAE/E,CAAC,CAAC,QAAQW,GAAG,EAAE,CACxB,CAAC,CAAC,CAAC,CAAGwE,iBAAiB,CAACb,GAAG,CAAC3D,GAAG,GAAK,CAClCO,GAAG,CAAEP,GAAG,CACRoE,KAAK,CAAE/E,CAAC,CAAC,QAAQW,GAAG,EAAE,CACxB,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAyE,qBAAqB,CAAIjD,MAAM,EAAK,CACxC,KAAM,CAAAkD,aAAa,CAAG,CACpBC,MAAM,CAAEtF,CAAC,CAAC,kBAAkB,CAAC,EAAI,KAAK,CACtCuF,MAAM,CAAEvF,CAAC,CAAC,kBAAkB,CAAC,EAAI,KAAK,CACtCwF,OAAO,CAAExF,CAAC,CAAC,mBAAmB,CAAC,EAAI,KAAK,CACxCyF,SAAS,CAAEzF,CAAC,CAAC,qBAAqB,CAAC,EAAI,KAAK,CAC5C0F,QAAQ,CAAE1F,CAAC,CAAC,oBAAoB,CAAC,EAAI,KAAK,CAC1C2F,MAAM,CAAE3F,CAAC,CAAC,kBAAkB,CAAC,EAAI,KAAK,CACtC4F,QAAQ,CAAE5F,CAAC,CAAC,oBAAoB,CAAC,EAAI,KACvC,CAAC,CACD,MAAO,CAAAqF,aAAa,CAAClD,MAAM,CAAC,EAAIA,MAAM,CAAC0D,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CACxD,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAIC,OAAO,EAAK,CACpC,KAAM,CAAAC,gBAAgB,CAAG,GAAI,CAAAtE,IAAI,CAACqE,OAAO,CAACE,QAAQ,CAAC,CACnD,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAAxE,IAAI,CAACqE,OAAO,CAACE,QAAQ,CAAC,CACjDC,cAAc,CAACC,UAAU,CAACD,cAAc,CAACE,UAAU,CAAC,CAAC,CAAGtD,QAAQ,CAACiD,OAAO,CAACM,QAAQ,CAAC,CAAC,CACnF,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAA5E,IAAI,CAAC,CAAC,CAEtB,GAAIqE,OAAO,CAACQ,MAAM,GAAK,WAAW,CAAE,CAClC,MAAO,WAAW,CACpB,CAEA,GAAID,GAAG,EAAIN,gBAAgB,EAAIM,GAAG,CAAGJ,cAAc,CAAE,CACnD,MAAO,SAAS,CAClB,CAEA,GAAII,GAAG,EAAIJ,cAAc,CAAE,CACzB,MAAO,WAAW,CACpB,CAEA,MAAO,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAM,yBAAyB,CAAIT,OAAO,EAAK,CAC7C,KAAM,CAAAQ,MAAM,CAAGT,gBAAgB,CAACC,OAAO,CAAC,CAExC,OAAQQ,MAAM,EACZ,IAAK,SAAS,CACZ,MAAO,CACLE,UAAU,CAAE,2BAA2BxJ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACE,IAAI,CAAE,IAAI,CAAC,QAAQ,CACrIC,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,EAAE,CAC7DG,eAAe,CAAE,2BAA2B9J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACE,IAAI,CAAE,CAAC,CAAC,QACjI,CAAC,CACH,IAAK,WAAW,CACd,MAAO,CACLJ,UAAU,CAAE,2BAA2BxJ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,QAAQ/J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,QAAQ,CAC/HF,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,EAAE,CAC1DD,eAAe,CAAE,2BAA2B9J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,QAAQ/J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC,QAC3H,CAAC,CACH,IAAK,WAAW,CACd,MAAO,CACLP,UAAU,CAAE,2BAA2BxJ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC4E,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC6E,IAAI,CAAE,IAAI,CAAC,QAAQ,CACjIC,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC4E,IAAI,CAAE,GAAG,CAAC,EAAE,CAC3DG,eAAe,CAAE,2BAA2B9J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC4E,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC6E,IAAI,CAAE,CAAC,CAAC,QAC7H,CAAC,CACH,QAAS;AACP,MAAO,CACLJ,UAAU,CAAE,2BAA2BxJ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACL,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACJ,IAAI,CAAE,IAAI,CAAC,QAAQ,CAC/HC,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACL,IAAI,CAAE,GAAG,CAAC,EAAE,CAC1DG,eAAe,CAAE,2BAA2B9J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACL,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACJ,IAAI,CAAE,CAAC,CAAC,QAC3H,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAK,mBAAmB,CAAGA,CAACvG,GAAG,CAAEC,QAAQ,GAAK,CAC7C,GAAI,CAACzB,QAAQ,EAAI,CAACE,gBAAgB,CAAE,MAAO,EAAE,CAE7C,KAAM,CAAAyB,QAAQ,CAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKP,GAAG,CAAC,CAC7D,KAAM,CAAAQ,IAAI,CAAG7C,OAAO,CAACe,gBAAgB,CAAEyB,QAAQ,CAAC,CAChD,KAAM,CAAAqG,OAAO,CAAG9I,MAAM,CAAC8C,IAAI,CAAE,YAAY,CAAC,CAE1C,MAAO,CAAAhC,QAAQ,CAACiI,MAAM,CAACrB,OAAO,EAAI,CAChC;AACA,GAAI,CAAAsB,WAAW,CAAEC,WAAW,CAAEC,aAAa,CAE3C,GAAI9H,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C;AACA,KAAM,CAAA2F,iBAAiB,CAAG9I,2BAA2B,CAACqH,OAAO,CAACE,QAAQ,CAAExG,cAAc,CAACoC,QAAQ,CAAE,qBAAqB,CAAC,CACvH,KAAM,CAAC4F,QAAQ,CAAEC,QAAQ,CAAC,CAAGF,iBAAiB,CAACnE,KAAK,CAAC,GAAG,CAAC,CACzDgE,WAAW,CAAGI,QAAQ,CAEtB,KAAM,CAACE,OAAO,CAAEC,SAAS,CAAC,CAAGF,QAAQ,CAACrE,KAAK,CAAC,GAAG,CAAC,CAChDiE,WAAW,CAAGxE,QAAQ,CAAC6E,OAAO,CAAC,CAC/BJ,aAAa,CAAGzE,QAAQ,CAAC8E,SAAS,CAAC,CACrC,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,eAAe,CAAG,GAAI,CAAAnG,IAAI,CAACqE,OAAO,CAACE,QAAQ,CAAC,CAClDoB,WAAW,CAAGhJ,MAAM,CAACwJ,eAAe,CAAE,YAAY,CAAC,CACnDP,WAAW,CAAGO,eAAe,CAACC,QAAQ,CAAC,CAAC,CACxCP,aAAa,CAAGM,eAAe,CAACzB,UAAU,CAAC,CAAC,CAC9C,CAEA;AACA,KAAM,CAAA2B,WAAW,CAAGV,WAAW,GAAKF,OAAO,CAC3C,GAAI,CAACY,WAAW,CAAE,MAAO,MAAK,CAE9B;AACA,KAAM,CAAA1B,QAAQ,CAAGvD,QAAQ,CAACiD,OAAO,CAACM,QAAQ,CAAC,EAAI,EAAE,CACjD,KAAM,CAAA2B,mBAAmB,CAAGV,WAAW,CAAG,EAAE,CAAGC,aAAa,CAC5D,KAAM,CAAAU,iBAAiB,CAAGD,mBAAmB,CAAG3B,QAAQ,CAExD;AACA,KAAM,CAAAvC,gBAAgB,CAAGlD,QAAQ,CAACS,IAAI,CAAG,EAAE,CAC3C,KAAM,CAAA0C,cAAc,CAAGD,gBAAgB,CAAG,EAAE,CAE5C;AACA,GAAIuC,QAAQ,GAAK,EAAE,CAAE,CACnB;AACA,KAAM,CAAA6B,gBAAgB,CAAGF,mBAAmB,EAAIlE,gBAAgB,EAAIkE,mBAAmB,CAAGjE,cAAc,CAExG;AACA,KAAM,CAAAoE,mBAAmB,CAAGH,mBAAmB,CAAGlE,gBAAgB,EAAImE,iBAAiB,CAAGnE,gBAAgB,CAE1G,MAAO,CAAAoE,gBAAgB,EAAIC,mBAAmB,CAChD,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,UAAU,CAAGtE,gBAAgB,CAAG,EAAE,CAExC;AACA,GAAIkE,mBAAmB,EAAIlE,gBAAgB,EAAIkE,mBAAmB,CAAGI,UAAU,CAAE,CAC/E;AACA,MAAO,CAAAJ,mBAAmB,EAAIlE,gBAAgB,EAAIkE,mBAAmB,CAAGI,UAAU,CACpF,CAAC,IAAM,IAAIJ,mBAAmB,EAAII,UAAU,EAAIJ,mBAAmB,CAAGjE,cAAc,CAAE,CACpF;AACA,MAAO,CAAAiE,mBAAmB,EAAII,UAAU,EAAIJ,mBAAmB,CAAGjE,cAAc,CAClF,CACF,CAEA,MAAO,MAAK,CACd,CAAC,CAAC,CACJ,CAAC,CAED,GAAI3E,OAAO,CAAE,CACX,mBACER,IAAA,CAACjC,GAAG,EAAC0L,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cAC5D7J,IAAA,CAAC1B,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA,GAAI,CAACiC,QAAQ,EAAIA,QAAQ,CAACuJ,MAAM,GAAK,CAAC,CAAE,CACtC,mBACE9J,IAAA,CAAC/B,KAAK,EAAC8L,SAAS,CAAE,CAAE,CAACN,EAAE,CAAE,CAAEO,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,YAAY,CAAE,CAAC,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAN,QAAA,cAC7E7J,IAAA,CAAChC,UAAU,EAACoM,OAAO,CAAC,IAAI,CAACC,KAAK,CAAC,gBAAgB,CAAAR,QAAA,CAC5CzI,CAAC,CAAC,qBAAqB,CAAC,CACf,CAAC,CACR,CAAC,CAEZ,CAEA,mBACElB,KAAA,CAACjC,KAAK,EAAC8L,SAAS,CAAE,CAAE,CAACN,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAC,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAElD7J,IAAA,CAACjC,GAAG,EAAC0L,EAAE,CAAE,CACP5B,UAAU,CAAE,2BAA2BvG,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,QAAQ1G,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACrC,IAAI,QAAQ,CAC3G+B,CAAC,CAAE,CAAC,CACJK,KAAK,CAAE,OAAO,CACdX,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BY,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,MAAM,CAChBC,GAAG,CAAE,CACP,CAAE,CAAAZ,QAAA,cACA3J,KAAA,CAACnC,GAAG,EAAA8L,QAAA,eACF3J,KAAA,CAAClC,UAAU,EAACoM,OAAO,CAAC,IAAI,CAACX,EAAE,CAAE,CAAEiB,UAAU,CAAE,MAAM,CAAET,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,EAAC,eACvD,CAACzI,CAAC,CAAC,sBAAsB,CAAC,EACnB,CAAC,cACbpB,IAAA,CAAChC,UAAU,EAACoM,OAAO,CAAC,OAAO,CAACX,EAAE,CAAE,CAAEkB,OAAO,CAAE,GAAI,CAAE,CAAAd,QAAA,CAC9CzI,CAAC,CAAC,4BAA4B,CAAC,CACtB,CAAC,EACV,CAAC,CACH,CAAC,cAGNlB,KAAA,CAACnC,GAAG,EAAC0L,EAAE,CAAE,CAAEmB,QAAQ,CAAE,MAAO,CAAE,CAAAf,QAAA,eAE5B3J,KAAA,CAACnC,GAAG,EAAC0L,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfmB,mBAAmB,CAAE,CACnBC,EAAE,CAAE,mCAAmC,CACvCC,EAAE,CAAE,oCAAoC,CACxCC,EAAE,CAAE,qCACN,CAAC,CACDC,OAAO,CAAE5M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,IAAI,CAAC,CAChDkD,YAAY,CAAE,aAAa7M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,GAAG,CAAC,EAAE,CACnEmD,QAAQ,CAAE,QAAQ,CAClBC,GAAG,CAAE,CAAC,CACNC,MAAM,CAAE,EACV,CAAE,CAAAxB,QAAA,eACA7J,IAAA,CAACjC,GAAG,EAAC0L,EAAE,CAAE,CACPO,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC9BM,SAAS,CAAE,CAAER,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CACjDtB,OAAO,CAAE,MAAM,CACfa,UAAU,CAAE,QAAQ,CACpBZ,cAAc,CAAE,QAAQ,CACxB4B,WAAW,CAAE,aAAalN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,GAAG,CAAC,EAClE,CAAE,CAAA6B,QAAA,cACA3J,KAAA,CAAClC,UAAU,EAACoM,OAAO,CAAC,OAAO,CAACX,EAAE,CAAE,CAC9BiB,UAAU,CAAE,MAAM,CAClBL,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAAC0D,IAAI,CAACC,SAAS,CACnCC,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CACrD,CAAE,CAAAnB,QAAA,EAAC,SACC,CAACzI,CAAC,CAAC,cAAc,CAAC,EACV,CAAC,CACV,CAAC,CACLe,cAAc,CAACuD,GAAG,CAAC,CAAC3D,GAAG,CAAE4J,KAAK,GAAK,CAClC;AACA,KAAM,CAAAC,OAAO,CAAGnL,gBAAgB,CAAGf,OAAO,CAACe,gBAAgB,CAAEkL,KAAK,CAAC,CAAG,GAAI,CAAA7I,IAAI,CAAC,CAAC,CAEhF,mBACE5C,KAAA,CAACnC,GAAG,EAEF0L,EAAE,CAAE,CACFO,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC9BM,SAAS,CAAE,CAAER,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CACjDb,SAAS,CAAE,QAAQ,CACnBoB,WAAW,CAAE,aAAalN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,GAAG,CAAC,EAAE,CAClE,cAAc,CAAE,CAAEuD,WAAW,CAAE,MAAO,CAAC,CACvC7B,OAAO,CAAE,MAAM,CACfmC,aAAa,CAAE,QAAQ,CACvBlC,cAAc,CAAE,QAAQ,CACxBY,UAAU,CAAE,QACd,CAAE,CAAAV,QAAA,eAEF3J,KAAA,CAAClC,UAAU,EAACoM,OAAO,CAAC,IAAI,CAACX,EAAE,CAAE,CAC3BiB,UAAU,CAAE,MAAM,CAClBL,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CACjC0D,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACpDc,UAAU,CAAE,GACd,CAAE,CAAAjC,QAAA,eAEA7J,IAAA,CAACjC,GAAG,EAACgO,SAAS,CAAC,MAAM,CAACtC,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAEoB,EAAE,CAAE,MAAM,CAAEE,EAAE,CAAE,OAAQ,CAAE,CAAE,CAAAnB,QAAA,CAChE9H,GAAG,CAACoE,KAAK,CACP,CAAC,cACNnG,IAAA,CAACjC,GAAG,EAACgO,SAAS,CAAC,MAAM,CAACtC,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAEoB,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,MAAO,CAAE,CAAE,CAAAnB,QAAA,CAC5E9H,GAAG,CAACoE,KAAK,CAAC2D,MAAM,CAAG,CAAC,CAAG/H,GAAG,CAACoE,KAAK,CAACc,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAGlF,GAAG,CAACoE,KAAK,CAC1D,CAAC,cACNnG,IAAA,CAACjC,GAAG,EAACgO,SAAS,CAAC,MAAM,CAACtC,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAEoB,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,MAAO,CAAE,CAAE,CAAAlB,QAAA,CAChErD,qBAAqB,CAACzE,GAAG,CAACO,GAAG,CAAC,CAC5B,CAAC,EACI,CAAC,cAGbtC,IAAA,CAAChC,UAAU,EAACoM,OAAO,CAAC,SAAS,CAACX,EAAE,CAAE,CAChCC,OAAO,CAAE,OAAO,CAChBW,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAAC0D,IAAI,CAACC,SAAS,CACnCC,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtDgB,EAAE,CAAE,GACN,CAAE,CAAAnC,QAAA,CACCpK,MAAM,CAACmM,OAAO,CAAE,OAAO,CAAE,CAAEK,MAAM,CAAE1K,KAAK,CAAG5B,EAAE,CAAGC,IAAK,CAAC,CAAC,CAC9C,CAAC,GAvCRmC,GAAG,CAACO,GAwCN,CAAC,CAEV,CAAC,CAAC,EACC,CAAC,cAGNtC,IAAA,CAACjC,GAAG,EAAA8L,QAAA,CACD7D,SAAS,CAACN,GAAG,CAAC,CAAC1D,QAAQ,CAAE2J,KAAK,GAAK,CAClC,KAAM,CAAAO,WAAW,CAAGlK,QAAQ,CAACU,MAAM,GAAK,CAAC,CACzC,mBACExC,KAAA,CAACnC,GAAG,EAEF0L,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfmB,mBAAmB,CAAE,CACnBC,EAAE,CAAE,mCAAmC,CACvCC,EAAE,CAAE,oCAAoC,CACxCC,EAAE,CAAE,qCACN,CAAC,CACDE,YAAY,CAAE,aAAa7M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACqE,OAAO,CAAED,WAAW,CAAG,GAAG,CAAG,GAAG,CAAC,EAAE,CAClFjB,OAAO,CAAEU,KAAK,CAAG,CAAC,CAAG,CAAC,CAAGtN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,IAAI,CAAC,CAAG,aAAa,CAChF,SAAS,CAAE,CACTiD,OAAO,CAAE5M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,IAAI,CACjD,CACF,CAAE,CAAA6B,QAAA,eAGF7J,IAAA,CAACjC,GAAG,EAAC0L,EAAE,CAAE,CACPO,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC9BtB,OAAO,CAAE,MAAM,CACf0C,UAAU,CAAE,QAAQ,CACpBb,WAAW,CAAE,aAAalN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,GAAG,CAAC,EAAE,CAClEiD,OAAO,CAAEiB,WAAW,CAAG7N,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,IAAI,CAAC,CAAG,aAAa,CAC9EmD,QAAQ,CAAE,UACZ,CAAE,CAAAtB,QAAA,cACA7J,IAAA,CAACjC,GAAG,EAAC0L,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfmC,aAAa,CAAE,QAAQ,CACvBtB,UAAU,CAAE,QAAQ,CACpBE,GAAG,CAAE,GAAG,CACRU,QAAQ,CAAE,UAAU,CACpBkB,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7BlB,GAAG,CAAEpJ,QAAQ,CAACU,MAAM,GAAK,CAAC,CAAG,MAAM,CAAG,kBACxC,CAAE,CAAAmH,QAAA,cAEA7J,IAAA,CAAChC,UAAU,EACToM,OAAO,CAAC,OAAO,CACfX,EAAE,CAAE,CACFiB,UAAU,CAAE,MAAM,CAClBL,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CACjC0D,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CAAAnB,QAAA,CAED7H,QAAQ,CAACmE,KAAK,CACL,CAAC,CACV,CAAC,CACH,CAAC,CAGLhE,cAAc,CAACuD,GAAG,CAAE3D,GAAG,EAAK,CAC3B,KAAM,CAAAwK,iBAAiB,CAAGjE,mBAAmB,CAACvG,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAC,CAEhE;AACA;AACR,KAAM,CAAAwK,cAAc,CAAGD,iBAAiB,CAAC/D,MAAM,CAACrB,OAAO,EAAI,CACjD;AACA,GAAI,CAAAuB,WAAW,CAAG,CAAC,CACnB,GAAI,CAAAC,aAAa,CAAG,CAAC,CAErB,GAAI9H,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C,KAAM,CAAA2F,iBAAiB,CAAG9I,2BAA2B,CAACqH,OAAO,CAACE,QAAQ,CAAExG,cAAc,CAACoC,QAAQ,CAAE,qBAAqB,CAAC,CACvH,KAAM,EAAG6F,QAAQ,CAAC,CAAGF,iBAAiB,CAACnE,KAAK,CAAC,GAAG,CAAC,CACjD,KAAM,CAACsE,OAAO,CAAEC,SAAS,CAAC,CAAGF,QAAQ,CAACrE,KAAK,CAAC,GAAG,CAAC,CAChDiE,WAAW,CAAGxE,QAAQ,CAAC6E,OAAO,CAAC,CAC/BJ,aAAa,CAAGzE,QAAQ,CAAC8E,SAAS,CAAC,CACrC,CAAC,IAAM,CACL,KAAM,CAAAP,WAAW,CAAG,GAAI,CAAA3F,IAAI,CAACqE,OAAO,CAACE,QAAQ,CAAC,CAC9CqB,WAAW,CAAGD,WAAW,CAACS,QAAQ,CAAC,CAAC,CACpCP,aAAa,CAAGF,WAAW,CAACjB,UAAU,CAAC,CAAC,CAC1C,CAEA,KAAM,CAAAC,QAAQ,CAAGvD,QAAQ,CAACiD,OAAO,CAACM,QAAQ,CAAC,EAAI,EAAE,CACjD,KAAM,CAAA2B,mBAAmB,CAAGV,WAAW,CAAG,EAAE,CAAGC,aAAa,CAC5D,KAAM,CAAA8D,uBAAuB,CAAGzK,QAAQ,CAACS,IAAI,CAAG,EAAE,CAElD;AACA;AACA,GAAIgF,QAAQ,GAAK,EAAE,CAAE,CACnB,KAAM,CAAA8B,mBAAmB,CAAGH,mBAAmB,CAAGqD,uBAAuB,CACzE,MAAO,CAAClD,mBAAmB,CAAE;AAC/B,CAEA,MAAO,KAAI,CAAE;AACf,CAAC,CAAC,CAEF;AACA;AACA;AACA,KAAM,CAAAmD,kBAAkB,CAAGF,cAAc,CAACG,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAK,CAC9D,KAAM,CAAAvK,GAAG,CAAGuK,IAAI,CAACxF,QAAQ,CAAE;AAC3B,KAAM,CAAAyF,QAAQ,CAAGF,GAAG,CAACtK,GAAG,CAAC,CAEzB,GAAI,CAACwK,QAAQ,CAAE,CACbF,GAAG,CAACtK,GAAG,CAAC,CAAGuK,IAAI,CAAE;AACnB,CAAC,IAAM,CACL;AACA,GAAIC,QAAQ,CAACnF,MAAM,GAAK,WAAW,EAAIkF,IAAI,CAAClF,MAAM,GAAK,WAAW,CAAE,CAClEiF,GAAG,CAACtK,GAAG,CAAC,CAAGuK,IAAI,CACjB,CACA;AACA;AACF,CACA,MAAO,CAAAD,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAG,eAAe,CAAGC,MAAM,CAACC,MAAM,CAACP,kBAAkB,CAAC,CAEzD,mBACExM,KAAA,CAACnC,GAAG,EAEF0L,EAAE,CAAE,CACFO,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CAChCM,SAAS,CAAE,CAAER,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,OAAQ,CAAC,CAAE;AACrDtB,OAAO,CAAE,MAAM,CACfa,UAAU,CAAE,QAAQ,CACpBZ,cAAc,CAAE,QAAQ,CACxB4B,WAAW,CAAE,aAAalN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CAAE,GAAG,CAAC,EAAE,CAClE,cAAc,CAAE,CAAEuD,WAAW,CAAE,MAAO,CAAC,CACvCJ,QAAQ,CAAE,UACZ,CAAE,CAAAtB,QAAA,eAGF7J,IAAA,CAACjC,GAAG,EACF0L,EAAE,CAAE,CACF0B,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNiB,IAAI,CAAE,CAAC,CACPa,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,KAAK,CACblC,OAAO,CAAE3J,KAAK,CAACwG,OAAO,CAACwC,OAAO,CAACtC,IAAI,CACnCqD,MAAM,CAAE,CAAC,CACTnB,YAAY,CAAE,KAAK,CACnBkD,SAAS,CAAE,2BAA2B,CACtCC,aAAa,CAAE,MACjB,CAAE,CACH,CAAC,cAEFrN,IAAA,CAACjC,GAAG,EACF0L,EAAE,CAAE,CACF0B,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,KAAK,CACViB,IAAI,CAAE,CAAC,CACPa,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,KAAK,CACblC,OAAO,CAAE5M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACqE,OAAO,CAAE,GAAG,CAAC,CAAE;AAC5Cd,MAAM,CAAE,CAAC,CAAE;AACXnB,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,CAED6C,eAAe,CAACjD,MAAM,CAAG,CAAC,cACzB9J,IAAA,CAAAI,SAAA,EAAAyJ,QAAA,CACGkD,eAAe,CAACrH,GAAG,CAAC,CAACyB,OAAO,CAAEmG,YAAY,GAAK,CAC9C,KAAM,CAAAC,aAAa,CAAG3F,yBAAyB,CAACT,OAAO,CAAC,CACxD,KAAM,CAAAqG,YAAY,CAAGtJ,QAAQ,CAACiD,OAAO,CAACM,QAAQ,CAAC,GAAK,EAAE,CAEtD;AACA,GAAI,CAAAkB,aAAa,CAAG,CAAC,CACrB,GAAI,CAAAD,WAAW,CAAG,CAAC,CACnB,GAAI7H,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C,KAAM,CAAA2F,iBAAiB,CAAG9I,2BAA2B,CAACqH,OAAO,CAACE,QAAQ,CAAExG,cAAc,CAACoC,QAAQ,CAAE,qBAAqB,CAAC,CACvH,KAAM,EAAG6F,QAAQ,CAAC,CAAGF,iBAAiB,CAACnE,KAAK,CAAC,GAAG,CAAC,CACjD,KAAM,CAACsE,OAAO,CAAEC,SAAS,CAAC,CAAGF,QAAQ,CAACrE,KAAK,CAAC,GAAG,CAAC,CAChDiE,WAAW,CAAGxE,QAAQ,CAAC6E,OAAO,CAAC,CAC/BJ,aAAa,CAAGzE,QAAQ,CAAC8E,SAAS,CAAC,CACrC,CAAC,IAAM,CACL,KAAM,CAAAP,WAAW,CAAG,GAAI,CAAA3F,IAAI,CAACqE,OAAO,CAACE,QAAQ,CAAC,CAC9CqB,WAAW,CAAGD,WAAW,CAACS,QAAQ,CAAC,CAAC,CACpCP,aAAa,CAAGF,WAAW,CAACjB,UAAU,CAAC,CAAC,CAC1C,CAEA;AACA;AACA,GAAI,CAAAvF,WAAW,CAAG0G,aAAa,GAAK,CAAC,CAErC;AACA,GAAI6E,YAAY,CAAE,CAChB;AACA,KAAM,CAAApE,mBAAmB,CAAGV,WAAW,CAAG,EAAE,CAAGC,aAAa,CAC5D,KAAM,CAAAU,iBAAiB,CAAGD,mBAAmB,CAAG,EAAE,CAAE;AACpD,KAAM,CAAAqD,uBAAuB,CAAGzK,QAAQ,CAACS,IAAI,CAAG,EAAE,CAClD,KAAM,CAAAgL,qBAAqB,CAAGhB,uBAAuB,CAAG,EAAE,CAE1D;AACA,KAAM,CAAAnD,gBAAgB,CAAGF,mBAAmB,EAAIqD,uBAAuB,EAAIrD,mBAAmB,CAAGqE,qBAAqB,CAEtH,GAAInE,gBAAgB,CAAE,CACpB;AACA,KAAM,CAAAoE,iBAAiB,CAAGrE,iBAAiB,CAAGoE,qBAAqB,CACnE,GAAIC,iBAAiB,CAAE,CACrB;AACAzL,WAAW,CAAG,UAAU,CAAE;AAC5B,CAAC,IAAM,CACL;AACAA,WAAW,CAAG0G,aAAa,GAAK,CAAC,CAAG,IAAI,CAAG,gBAAgB,CAC7D,CACF,CACF,CAEA,mBACEzI,KAAA,CAACnC,GAAG,EAEF0L,EAAE,CAAE,CACFkE,KAAK,CAAE,KAAK,CACZR,MAAM,CAAE,CAAC,IAAM,CACb,GAAIlL,WAAW,GAAK,UAAU,CAAE,CAC9B;AACA,MAAO,MAAM,CAAE;AACjB,CACA,GAAIA,WAAW,GAAK,gBAAgB,CAAE,CACpC;AACA,MAAO,KAAK,CACd,CAEA,MAAO,CAAAuL,YAAY,CAAG,KAAK,CAAG,KAAK,CACrC,CAAC,EAAE,CAAC,CACJrC,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,IAAM,CACV,GAAInJ,WAAW,GAAK,UAAU,CAAE,CAC9B;AACA,MAAO,KAAK,CACd,CACA,GAAIA,WAAW,GAAK,gBAAgB,CAAE,CACpC;AACA,MAAO,KAAK,CACd,CAEA,MAAO,CAAAuL,YAAY,CAAG,MAAM,CAAIvL,WAAW,CAAG,IAAI,CAAG,KAAM,CAC7D,CAAC,EAAE,CAAC,CACJoK,IAAI,CAAE,IAAI,CACVhB,MAAM,CAAE,CAAC,IAAM,CACb,GAAIpJ,WAAW,GAAK,UAAU,CAAE,CAC9B,MAAO,IAAG,CAAE;AACd,CAEA,MAAO,GAAE,CAAGqL,YAAY,CAAE;AAC5B,CAAC,EAAE,CAAC,CACJpD,YAAY,CAAE,CAAC,CACfrC,UAAU,CAAE0F,aAAa,CAAC1F,UAAU,CACpC6B,OAAO,CAAE,MAAM,CACfmC,aAAa,CAAE,QAAQ,CACvBtB,UAAU,CAAE,QAAQ,CACpBZ,cAAc,CAAE,QAAQ,CACxBc,GAAG,CAAE,CAAEK,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CAClChB,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CAChC4C,MAAM,CAAE,SAAS,CACjB1F,MAAM,CAAEqF,aAAa,CAACrF,MAAM,CAC5BkF,SAAS,CAAE,2BAA2B,CACtC,SAAS,CAAE,CACTvF,UAAU,CAAE0F,aAAa,CAACpF,eAAe,CACzCmE,SAAS,CAAE,kBAAkB,CAC7Bc,SAAS,CAAE,6BACb,CAAC,CACDS,UAAU,CAAE,sBAAsB,CAClCjD,QAAQ,CAAE,QACZ,CAAE,CACFkD,OAAO,CAAEA,CAAA,GAAMnN,aAAa,CAACwG,OAAO,CAAE,CAAA0C,QAAA,eAItC7J,IAAA,CAAChC,UAAU,EACToM,OAAO,CAAC,OAAO,CACfX,EAAE,CAAE,CACFY,KAAK,CAAE,OAAO,CACdK,UAAU,CAAE,MAAM,CAClBgB,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,SAAU,CAAC,CACvDb,SAAS,CAAE,QAAQ,CACnB2B,UAAU,CAAE,GAAG,CACflB,QAAQ,CAAE,QAAQ,CAClBmD,YAAY,CAAE,UAAU,CACxBrE,OAAO,CAAE,aAAa,CACtBsE,eAAe,CAAE,CAAC,CAClBC,eAAe,CAAE,UAAU,CAC3BC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,2BAA2B,CACvClE,EAAE,CAAE,CAAEa,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAClC,CAAE,CAAAnB,QAAA,CAED7I,aAAa,CAAGmG,OAAO,CAACiH,YAAY,CAAGjH,OAAO,CAACkH,YAAY,CAClD,CAAC,cAEbnO,KAAA,CAACnC,GAAG,EAAC0L,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEe,GAAG,CAAE,CAAEK,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAE,CAAAnB,QAAA,eAC/D7J,IAAA,CAAC9B,MAAM,EACLoQ,IAAI,CAAC,OAAO,CACZlE,OAAO,CAAC,WAAW,CACnB0D,OAAO,CAAGS,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnB7N,aAAa,CAACwG,OAAO,CAAC,CACxB,CAAE,CACFsC,EAAE,CAAE,CACFgF,QAAQ,CAAE,MAAM,CAChBd,KAAK,CAAE,CAAE7C,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CACjCmC,MAAM,CAAE,CAAErC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAClChB,CAAC,CAAE,CAAC,CACJiB,OAAO,CAAE,0BAA0B,CACnCZ,KAAK,CAAE,OAAO,CACdH,YAAY,CAAE,GAAG,CACjB,SAAS,CAAE,CACTe,OAAO,CAAE,0BACX,CACF,CAAE,CAAApB,QAAA,cAEF7J,IAAA,CAAClB,QAAQ,EAAC2K,EAAE,CAAE,CAAEiC,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAE,CAAE,CAAE,CAAC,CACtE,CAAC,CACR7D,OAAO,CAACQ,MAAM,GAAK,WAAW,EAAI/G,eAAe,EAAI,CAACI,aAAa,eAClEhB,IAAA,CAAC9B,MAAM,EACLoQ,IAAI,CAAC,OAAO,CACZlE,OAAO,CAAC,WAAW,CACnB0D,OAAO,CAAGS,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnB5N,eAAe,CAACuG,OAAO,CAAC,CAC1B,CAAE,CACFsC,EAAE,CAAE,CACFgF,QAAQ,CAAE,MAAM,CAChBd,KAAK,CAAE,CAAE7C,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CACjCmC,MAAM,CAAE,CAAErC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAClChB,CAAC,CAAE,CAAC,CACJiB,OAAO,CAAE,wBAAwB,CACjCZ,KAAK,CAAE,OAAO,CACdH,YAAY,CAAE,GAAG,CACjB,SAAS,CAAE,CACTe,OAAO,CAAE,sBACX,CACF,CAAE,CAAApB,QAAA,cAEF7J,IAAA,CAAChB,UAAU,EAACyK,EAAE,CAAE,CAAEiC,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAE,CAAE,CAAE,CAAC,CACxE,CACT,EACE,CAAC,GA/HD,GAAG7D,OAAO,CAACuH,EAAE,IAAIpB,YAAY,EAgI/B,CAAC,CAEV,CAAC,CAAC,CACF,CAAC,cAEH;AACApN,KAAA,CAACnC,GAAG,EAAC0L,EAAE,CAAE,CACPkE,KAAK,CAAE,KAAK,CACZR,MAAM,CAAE,CAAErC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAQ,CAAC,CAC/Cd,YAAY,CAAE,GAAG,CACjBhC,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,EAAE,CAC1DsB,OAAO,CAAE,MAAM,CACfmC,aAAa,CAAE,QAAQ,CACvBjB,QAAQ,CAAE,QAAQ,CAClBS,MAAM,CAAE,CACV,CAAE,CAAAxB,QAAA,EAEC,CAAC,IAAM,CACN,KAAM,CAAA8E,kBAAkB,CAAGrL,uBAAuB,CAACvB,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,IAAI,CAAC,CAC3E,KAAM,CAAA4M,eAAe,CAAGhM,oBAAoB,CAACb,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,IAAI,CAAC,CAErE,mBACEhC,IAAA,CAACjC,GAAG,EAAC0L,EAAE,CAAE,CACPoF,IAAI,CAAE,CAAC,CACPnF,OAAO,CAAE,MAAM,CACfa,UAAU,CAAE,QAAQ,CACpBZ,cAAc,CAAE,QAAQ,CACxBuB,YAAY,CAAE,aAAa7M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,EAAE,CAChE6C,OAAO,CAAE2D,eAAe,CACpBvQ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,CACpCuG,kBAAkB,CAChBtQ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvC3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,CACzCyF,UAAU,CAAE,eACd,CAAE,CAAAhE,QAAA,CACC+E,eAAe,cACd5O,IAAA,CAACzB,OAAO,EAACuQ,KAAK,CAAE,GAAG9M,QAAQ,CAACS,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,SAAS3D,CAAC,CAAC,mBAAmB,CAAE,WAAW,CAAC,EAAG,CAAC2N,KAAK,MAAAlF,QAAA,cAC/G7J,IAAA,CAACZ,wBAAwB,EACvBqK,EAAE,CAAE,CACFY,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAC9BsD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CACH,CAAC,CACK,CAAC,CACR2D,kBAAkB,cACpBzO,KAAA,CAACnC,GAAG,EAAC0L,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEa,UAAU,CAAE,QAAQ,CAAEE,GAAG,CAAE,GAAI,CAAE,CAAAZ,QAAA,eAC3D7J,IAAA,CAACzB,OAAO,EAACuQ,KAAK,CAAE,GAAG9M,QAAQ,CAACS,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,SAAS3D,CAAC,CAAC,wBAAwB,CAAE,WAAW,CAAC,EAAG,CAAC2N,KAAK,MAAAlF,QAAA,cACpH7J,IAAA,CAACV,eAAe,EACdmK,EAAE,CAAE,CACFY,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CACjC0D,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACpD4C,MAAM,CAAE,SACV,CAAE,CACH,CAAC,CACK,CAAC,CACT1M,WAAW,eACVlB,IAAA,CAACzB,OAAO,EAACuQ,KAAK,CAAE1N,CAAC,CAAC,oBAAoB,CAAE,YAAY,CAAE,CAAC2N,KAAK,MAAAlF,QAAA,cAC1D7J,IAAA,CAACpB,UAAU,EACT0P,IAAI,CAAC,OAAO,CACZR,OAAO,CAAEA,CAAA,GAAMhM,eAAe,CAACC,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,IAAI,CAAE,CACxDyH,EAAE,CAAE,CACFY,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAACkH,OAAO,CAAChH,IAAI,CACjC0D,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtDiE,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,CACThE,OAAO,CAAE5M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACkH,OAAO,CAAChH,IAAI,CAAE,GAAG,CAChD,CACF,CAAE,CAAA6B,QAAA,cAEF7J,IAAA,CAACR,QAAQ,EAACiK,EAAE,CAAE,CAAEiC,QAAQ,CAAE,SAAU,CAAE,CAAE,CAAC,CAC/B,CAAC,CACN,CACV,EACE,CAAC,cAEN1L,IAAA,CAACZ,wBAAwB,EACvBqK,EAAE,CAAE,CACFY,KAAK,CAAEhM,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,CAC1CsD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CACH,CACF,CACE,CAAC,CAEV,CAAC,EAAE,CAAC,CAGH,CAAC,IAAM,CACN,KAAM,CAAAkE,mBAAmB,CAAG5L,uBAAuB,CAACvB,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,KAAK,CAAC,CAC7E,KAAM,CAAAmN,gBAAgB,CAAGvM,oBAAoB,CAACb,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,KAAK,CAAC,CAEvE,mBACEhC,IAAA,CAACjC,GAAG,EAAC0L,EAAE,CAAE,CACPoF,IAAI,CAAE,CAAC,CACPnF,OAAO,CAAE,MAAM,CACfa,UAAU,CAAE,QAAQ,CACpBZ,cAAc,CAAE,QAAQ,CACxBsB,OAAO,CAAEkE,gBAAgB,CACrB9Q,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,CACpC8G,mBAAmB,CACjB7Q,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvC3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,CACzCyF,UAAU,CAAE,eACd,CAAE,CAAAhE,QAAA,CACCsF,gBAAgB,cACfnP,IAAA,CAACzB,OAAO,EAACuQ,KAAK,CAAE,GAAG9M,QAAQ,CAACS,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAM/C,QAAQ,CAACS,IAAI,GAAK,EAAE,CAAG,QAAQ,CAAG,IAAI,CAACT,QAAQ,CAACS,IAAI,CAAG,CAAC,EAAEqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,MAAM3D,CAAC,CAAC,mBAAmB,CAAE,WAAW,CAAC,EAAG,CAAC2N,KAAK,MAAAlF,QAAA,cAC5M7J,IAAA,CAACZ,wBAAwB,EACvBqK,EAAE,CAAE,CACFY,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAC9BsD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CACH,CAAC,CACK,CAAC,CACRkE,mBAAmB,cACrBhP,KAAA,CAACnC,GAAG,EAAC0L,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEa,UAAU,CAAE,QAAQ,CAAEE,GAAG,CAAE,GAAI,CAAE,CAAAZ,QAAA,eAC3D7J,IAAA,CAACzB,OAAO,EAACuQ,KAAK,CAAE,GAAG9M,QAAQ,CAACS,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAM/C,QAAQ,CAACS,IAAI,GAAK,EAAE,CAAG,QAAQ,CAAG,IAAI,CAACT,QAAQ,CAACS,IAAI,CAAG,CAAC,EAAEqC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,MAAM3D,CAAC,CAAC,wBAAwB,CAAE,WAAW,CAAC,EAAG,CAAC2N,KAAK,MAAAlF,QAAA,cACjN7J,IAAA,CAACV,eAAe,EACdmK,EAAE,CAAE,CACFY,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CACjC0D,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACpD4C,MAAM,CAAE,SACV,CAAE,CACH,CAAC,CACK,CAAC,CACT1M,WAAW,eACVlB,IAAA,CAACzB,OAAO,EAACuQ,KAAK,CAAE1N,CAAC,CAAC,oBAAoB,CAAE,YAAY,CAAE,CAAC2N,KAAK,MAAAlF,QAAA,cAC1D7J,IAAA,CAACpB,UAAU,EACT0P,IAAI,CAAC,OAAO,CACZR,OAAO,CAAEA,CAAA,GAAMhM,eAAe,CAACC,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,KAAK,CAAE,CACzDyH,EAAE,CAAE,CACFY,KAAK,CAAE/I,KAAK,CAACwG,OAAO,CAACkH,OAAO,CAAChH,IAAI,CACjC0D,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtDiE,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,CACThE,OAAO,CAAE5M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACkH,OAAO,CAAChH,IAAI,CAAE,GAAG,CAChD,CACF,CAAE,CAAA6B,QAAA,cAEF7J,IAAA,CAACR,QAAQ,EAACiK,EAAE,CAAE,CAAEiC,QAAQ,CAAE,SAAU,CAAE,CAAE,CAAC,CAC/B,CAAC,CACN,CACV,EACE,CAAC,cAEN1L,IAAA,CAACZ,wBAAwB,EACvBqK,EAAE,CAAE,CACFY,KAAK,CAAEhM,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,CAC1CsD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CACH,CACF,CACE,CAAC,CAEV,CAAC,EAAE,CAAC,EACD,CACN,GAvXI,GAAGjJ,GAAG,CAACO,GAAG,IAAIN,QAAQ,CAACM,GAAG,EAwX5B,CAAC,CAEV,CAAC,CAAC,GAveGN,QAAQ,CAACM,GAweX,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CAAC,cAGNpC,KAAA,CAAC1B,MAAM,EACL4Q,IAAI,CAAE3N,eAAgB,CACtB4N,OAAO,CAAEA,CAAA,GAAM3N,kBAAkB,CAAC,KAAK,CAAE,CACzCwM,QAAQ,CAAC,IAAI,CACboB,SAAS,MAAAzF,QAAA,eAET3J,KAAA,CAACzB,WAAW,EAACgL,EAAE,CAAE,CAAEU,SAAS,CAAE,QAAQ,CAAEoF,EAAE,CAAE,CAAE,CAAE,CAAA1F,QAAA,eAC9C7J,IAAA,CAACR,QAAQ,EAACiK,EAAE,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAErB,KAAK,CAAE,cAAc,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACpEjK,IAAA,CAAChC,UAAU,EAACoM,OAAO,CAAC,IAAI,CAAC2B,SAAS,CAAC,KAAK,CAAAlC,QAAA,CACrCzI,CAAC,CAAC,yBAAyB,CAAE,oBAAoB,CAAC,CACzC,CAAC,EACF,CAAC,cAEdpB,IAAA,CAACtB,aAAa,EAAC+K,EAAE,CAAE,CAAEU,SAAS,CAAE,QAAQ,CAAEqF,EAAE,CAAE,CAAE,CAAE,CAAA3F,QAAA,CAC/CjI,iBAAiB,eAChB1B,KAAA,CAACnC,GAAG,EAAA8L,QAAA,eACF7J,IAAA,CAAChC,UAAU,EAACoM,OAAO,CAAC,OAAO,CAACX,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACvCzI,CAAC,CAAC,2BAA2B,CAAE,6BAA6B,CAAC,CACpD,CAAC,cAEblB,KAAA,CAACnC,GAAG,EAAC0L,EAAE,CAAE,CACPwB,OAAO,CAAE5M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACkH,OAAO,CAAChH,IAAI,CAAE,GAAG,CAAC,CAC/CgC,CAAC,CAAE,CAAC,CACJE,YAAY,CAAE,CAAC,CACfhC,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACkH,OAAO,CAAChH,IAAI,CAAE,GAAG,CAAC,EAC7D,CAAE,CAAA6B,QAAA,eACA3J,KAAA,CAAClC,UAAU,EAACoM,OAAO,CAAC,IAAI,CAACX,EAAE,CAAE,CAAEY,KAAK,CAAE,cAAc,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,EAAC,eAC1D,CAACpK,MAAM,CAACmC,iBAAiB,CAACW,IAAI,CAAE,mBAAmB,CAAE,CAAE0J,MAAM,CAAE1K,KAAK,CAAG5B,EAAE,CAAGC,IAAK,CAAC,CAAC,EAC5E,CAAC,cACbM,KAAA,CAAClC,UAAU,EAACoM,OAAO,CAAC,IAAI,CAACX,EAAE,CAAE,CAAEiB,UAAU,CAAE,MAAO,CAAE,CAAAb,QAAA,EAAC,eAChD,CAAC,CAAC,IAAM,CACT,KAAM,CAAArE,SAAS,CAAG5D,iBAAiB,CAACa,IAAI,CACxC,KAAM,CAAAgD,WAAW,CAAG7D,iBAAiB,CAACc,MAAM,CAC5C,KAAM,CAAAmD,SAAS,CAAGJ,WAAW,CAAG,EAAE,CAClC,KAAM,CAAAG,OAAO,CAAGC,SAAS,EAAI,EAAE,CAAGL,SAAS,CAAG,CAAC,CAAGA,SAAS,CAC3D,KAAM,CAAAiK,cAAc,CAAG5J,SAAS,EAAI,EAAE,CAAGA,SAAS,CAAG,EAAE,CAAGA,SAAS,CAEnE,MAAO,GAAGL,SAAS,CAACV,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIU,WAAW,CAACX,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAMa,OAAO,CAACd,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAI0K,cAAc,CAAC3K,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACrL,CAAC,EAAE,CAAC,EACM,CAAC,EACV,CAAC,cAEN/E,IAAA,CAAChC,UAAU,EAACoM,OAAO,CAAC,OAAO,CAACX,EAAE,CAAE,CAAEuC,EAAE,CAAE,CAAC,CAAE3B,KAAK,CAAE,gBAAiB,CAAE,CAAAR,QAAA,CAChEzI,CAAC,CAAC,wBAAwB,CAAE,iDAAiD,CAAC,CACrE,CAAC,EACV,CACN,CACY,CAAC,cAEhBlB,KAAA,CAACvB,aAAa,EAAC8K,EAAE,CAAE,CAAEE,cAAc,CAAE,QAAQ,CAAE4F,EAAE,CAAE,CAAE,CAAE,CAAA1F,QAAA,eACrD7J,IAAA,CAAC9B,MAAM,EACL4P,OAAO,CAAEA,CAAA,GAAMpM,kBAAkB,CAAC,KAAK,CAAE,CACzC0I,OAAO,CAAC,UAAU,CAClBX,EAAE,CAAE,CAAEgF,QAAQ,CAAE,GAAI,CAAE,CAAA5E,QAAA,CAErBzI,CAAC,CAAC,eAAe,CAAE,OAAO,CAAC,CACtB,CAAC,cACTpB,IAAA,CAAC9B,MAAM,EACL4P,OAAO,CAAEnL,gBAAiB,CAC1ByH,OAAO,CAAC,WAAW,CACnBC,KAAK,CAAC,SAAS,CACfZ,EAAE,CAAE,CAAEgF,QAAQ,CAAE,GAAG,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAA7F,QAAA,CAE5BzI,CAAC,CAAC,gBAAgB,CAAE,OAAO,CAAC,CACvB,CAAC,EACI,CAAC,EACV,CAAC,EACJ,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAf,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}