{"ast": null, "code": "import { useRef as t } from \"react\";\nimport { useIsoMorphicEffect as o } from './use-iso-morphic-effect.js';\nfunction s(e) {\n  let r = t(e);\n  return o(() => {\n    r.current = e;\n  }, [e]), r;\n}\nexport { s as useLatestValue };", "map": {"version": 3, "names": ["useRef", "t", "useIsoMorphicEffect", "o", "s", "e", "r", "current", "useLatestValue"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/use-latest-value.js"], "sourcesContent": ["import{useRef as t}from\"react\";import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';function s(e){let r=t(e);return o(()=>{r.current=e},[e]),r}export{s as useLatestValue};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACL,CAAC,CAACI,CAAC,CAAC;EAAC,OAAOF,CAAC,CAAC,MAAI;IAACG,CAAC,CAACC,OAAO,GAACF,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,EAACC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAII,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}