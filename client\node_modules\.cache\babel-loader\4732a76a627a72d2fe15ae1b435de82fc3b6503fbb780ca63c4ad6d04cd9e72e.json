{"ast": null, "code": "import { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $e7801be82b4b2a53$export$4debdb1a3f0fa79e(context, ref) {\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    if (context && context.ref && ref) {\n      context.ref.current = ref.current;\n      return () => {\n        if (context.ref) context.ref.current = null;\n      };\n    }\n  });\n}\nexport { $e7801be82b4b2a53$export$4debdb1a3f0fa79e as useSyncRef };", "map": {"version": 3, "names": ["$e7801be82b4b2a53$export$4debdb1a3f0fa79e", "context", "ref", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "current"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useSyncRef.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from './';\n\ninterface ContextValue<T> {\n  ref?: MutableRefObject<T | null>\n}\n\n// Syncs ref from context with ref passed to hook\nexport function useSyncRef<T>(context?: ContextValue<T> | null, ref?: RefObject<T | null>): void {\n  useLayoutEffect(() => {\n    if (context && context.ref && ref) {\n      context.ref.current = ref.current;\n      return () => {\n        if (context.ref) {\n          context.ref.current = null;\n        }\n      };\n    }\n  });\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAqBO,SAASA,0CAAcC,OAAgC,EAAEC,GAAyB;EACvF,IAAAC,yCAAc,EAAE;IACd,IAAIF,OAAA,IAAWA,OAAA,CAAQC,GAAG,IAAIA,GAAA,EAAK;MACjCD,OAAA,CAAQC,GAAG,CAACE,OAAO,GAAGF,GAAA,CAAIE,OAAO;MACjC,OAAO;QACL,IAAIH,OAAA,CAAQC,GAAG,EACbD,OAAA,CAAQC,GAAG,CAACE,OAAO,GAAG;MAE1B;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}