{"ast": null, "code": "import { disposables as s } from '../../utils/disposables.js';\nimport { createStore as i } from '../../utils/store.js';\nimport { adjustScrollbarPadding as l } from './adjust-scrollbar-padding.js';\nimport { handleIOSLocking as d } from './handle-ios-locking.js';\nimport { preventScroll as p } from './prevent-scroll.js';\nfunction m(e) {\n  let n = {};\n  for (let t of e) Object.assign(n, t(n));\n  return n;\n}\nlet a = i(() => new Map(), {\n  PUSH(e, n) {\n    var o;\n    let t = (o = this.get(e)) != null ? o : {\n      doc: e,\n      count: 0,\n      d: s(),\n      meta: new Set()\n    };\n    return t.count++, t.meta.add(n), this.set(e, t), this;\n  },\n  POP(e, n) {\n    let t = this.get(e);\n    return t && (t.count--, t.meta.delete(n)), this;\n  },\n  SCROLL_PREVENT({\n    doc: e,\n    d: n,\n    meta: t\n  }) {\n    let o = {\n        doc: e,\n        d: n,\n        meta: m(t)\n      },\n      c = [d(), l(), p()];\n    c.forEach(({\n      before: r\n    }) => r == null ? void 0 : r(o)), c.forEach(({\n      after: r\n    }) => r == null ? void 0 : r(o));\n  },\n  SCROLL_ALLOW({\n    d: e\n  }) {\n    e.dispose();\n  },\n  TEARDOWN({\n    doc: e\n  }) {\n    this.delete(e);\n  }\n});\na.subscribe(() => {\n  let e = a.getSnapshot(),\n    n = new Map();\n  for (let [t] of e) n.set(t, t.documentElement.style.overflow);\n  for (let t of e.values()) {\n    let o = n.get(t.doc) === \"hidden\",\n      c = t.count !== 0;\n    (c && !o || !c && o) && a.dispatch(t.count > 0 ? \"SCROLL_PREVENT\" : \"SCROLL_ALLOW\", t), t.count === 0 && a.dispatch(\"TEARDOWN\", t);\n  }\n});\nexport { a as overflows };", "map": {"version": 3, "names": ["disposables", "s", "createStore", "i", "adjustScrollbarPadding", "l", "handleIOSLocking", "d", "preventScroll", "p", "m", "e", "n", "t", "Object", "assign", "a", "Map", "PUSH", "o", "get", "doc", "count", "meta", "Set", "add", "set", "POP", "delete", "SCROLL_PREVENT", "c", "for<PERSON>ach", "before", "r", "after", "SCROLL_ALLOW", "dispose", "TEARDOWN", "subscribe", "getSnapshot", "documentElement", "style", "overflow", "values", "dispatch", "overflows"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js"], "sourcesContent": ["import{disposables as s}from'../../utils/disposables.js';import{createStore as i}from'../../utils/store.js';import{adjustScrollbarPadding as l}from'./adjust-scrollbar-padding.js';import{handleIOSLocking as d}from'./handle-ios-locking.js';import{preventScroll as p}from'./prevent-scroll.js';function m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=i(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:s(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[d(),l(),p()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});export{a as overflows};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,sBAAsB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC,CAAC,CAAC;EAAC,KAAI,IAAIC,CAAC,IAAIF,CAAC,EAACG,MAAM,CAACC,MAAM,CAACH,CAAC,EAACC,CAAC,CAACD,CAAC,CAAC,CAAC;EAAC,OAAOA,CAAC;AAAA;AAAC,IAAII,CAAC,GAACb,CAAC,CAAC,MAAI,IAAIc,GAAG,CAAD,CAAC,EAAC;EAACC,IAAIA,CAACP,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIO,CAAC;IAAC,IAAIN,CAAC,GAAC,CAACM,CAAC,GAAC,IAAI,CAACC,GAAG,CAACT,CAAC,CAAC,KAAG,IAAI,GAACQ,CAAC,GAAC;MAACE,GAAG,EAACV,CAAC;MAACW,KAAK,EAAC,CAAC;MAACf,CAAC,EAACN,CAAC,CAAC,CAAC;MAACsB,IAAI,EAAC,IAAIC,GAAG,CAAD;IAAC,CAAC;IAAC,OAAOX,CAAC,CAACS,KAAK,EAAE,EAACT,CAAC,CAACU,IAAI,CAACE,GAAG,CAACb,CAAC,CAAC,EAAC,IAAI,CAACc,GAAG,CAACf,CAAC,EAACE,CAAC,CAAC,EAAC,IAAI;EAAA,CAAC;EAACc,GAAGA,CAAChB,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACO,GAAG,CAACT,CAAC,CAAC;IAAC,OAAOE,CAAC,KAAGA,CAAC,CAACS,KAAK,EAAE,EAACT,CAAC,CAACU,IAAI,CAACK,MAAM,CAAChB,CAAC,CAAC,CAAC,EAAC,IAAI;EAAA,CAAC;EAACiB,cAAcA,CAAC;IAACR,GAAG,EAACV,CAAC;IAACJ,CAAC,EAACK,CAAC;IAACW,IAAI,EAACV;EAAC,CAAC,EAAC;IAAC,IAAIM,CAAC,GAAC;QAACE,GAAG,EAACV,CAAC;QAACJ,CAAC,EAACK,CAAC;QAACW,IAAI,EAACb,CAAC,CAACG,CAAC;MAAC,CAAC;MAACiB,CAAC,GAAC,CAACvB,CAAC,CAAC,CAAC,EAACF,CAAC,CAAC,CAAC,EAACI,CAAC,CAAC,CAAC,CAAC;IAACqB,CAAC,CAACC,OAAO,CAAC,CAAC;MAACC,MAAM,EAACC;IAAC,CAAC,KAAGA,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACd,CAAC,CAAC,CAAC,EAACW,CAAC,CAACC,OAAO,CAAC,CAAC;MAACG,KAAK,EAACD;IAAC,CAAC,KAAGA,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACd,CAAC,CAAC,CAAC;EAAA,CAAC;EAACgB,YAAYA,CAAC;IAAC5B,CAAC,EAACI;EAAC,CAAC,EAAC;IAACA,CAAC,CAACyB,OAAO,CAAC,CAAC;EAAA,CAAC;EAACC,QAAQA,CAAC;IAAChB,GAAG,EAACV;EAAC,CAAC,EAAC;IAAC,IAAI,CAACiB,MAAM,CAACjB,CAAC,CAAC;EAAA;AAAC,CAAC,CAAC;AAACK,CAAC,CAACsB,SAAS,CAAC,MAAI;EAAC,IAAI3B,CAAC,GAACK,CAAC,CAACuB,WAAW,CAAC,CAAC;IAAC3B,CAAC,GAAC,IAAIK,GAAG,CAAD,CAAC;EAAC,KAAI,IAAG,CAACJ,CAAC,CAAC,IAAGF,CAAC,EAACC,CAAC,CAACc,GAAG,CAACb,CAAC,EAACA,CAAC,CAAC2B,eAAe,CAACC,KAAK,CAACC,QAAQ,CAAC;EAAC,KAAI,IAAI7B,CAAC,IAAIF,CAAC,CAACgC,MAAM,CAAC,CAAC,EAAC;IAAC,IAAIxB,CAAC,GAACP,CAAC,CAACQ,GAAG,CAACP,CAAC,CAACQ,GAAG,CAAC,KAAG,QAAQ;MAACS,CAAC,GAACjB,CAAC,CAACS,KAAK,KAAG,CAAC;IAAC,CAACQ,CAAC,IAAE,CAACX,CAAC,IAAE,CAACW,CAAC,IAAEX,CAAC,KAAGH,CAAC,CAAC4B,QAAQ,CAAC/B,CAAC,CAACS,KAAK,GAAC,CAAC,GAAC,gBAAgB,GAAC,cAAc,EAACT,CAAC,CAAC,EAACA,CAAC,CAACS,KAAK,KAAG,CAAC,IAAEN,CAAC,CAAC4B,QAAQ,CAAC,UAAU,EAAC/B,CAAC,CAAC;EAAA;AAAC,CAAC,CAAC;AAAC,SAAOG,CAAC,IAAI6B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}