{"ast": null, "code": "import { isMac as $c87311424ea30a05$export$9ac100e40613ea10 } from \"./platform.mjs\";\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $21f1aa98acb08317$export$16792effe837dba3(e) {\n  if ((0, $c87311424ea30a05$export$9ac100e40613ea10)()) return e.metaKey;\n  return e.ctrlKey;\n}\nexport { $21f1aa98acb08317$export$16792effe837dba3 as isCtrlKeyPressed };", "map": {"version": 3, "names": ["$21f1aa98acb08317$export$16792effe837dba3", "e", "$c87311424ea30a05$export$9ac100e40613ea10", "metaKey", "ctrl<PERSON>ey"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\keyboard.tsx"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isMac} from './platform';\n\ninterface Event {\n  altKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean\n}\n\nexport function isCtrlKeyPressed(e: Event): boolean {\n  if (isMac()) {\n    return e.metaKey;\n  }\n\n  return e.ctrlKey;\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAoBO,SAASA,0CAAiBC,CAAQ;EACvC,IAAI,IAAAC,yCAAI,KACN,OAAOD,CAAA,CAAEE,OAAO;EAGlB,OAAOF,CAAA,CAAEG,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}