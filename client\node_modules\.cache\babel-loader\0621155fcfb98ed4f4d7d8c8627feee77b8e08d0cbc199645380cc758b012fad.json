{"ast": null, "code": "import { focusSafely as $3ad3f6e1647bc98d$export$80f3e147d781571c } from \"./focusSafely.mjs\";\nimport { useFocus as $a1ea59d68270f0dd$export$f8168d8dd8fd66e6 } from \"./useFocus.mjs\";\nimport { useKeyboard as $46d819fcbaf35654$export$8f71654801c2f7cd } from \"./useKeyboard.mjs\";\nimport { useSyncRef as $fcPuG$useSyncRef, useObjectRef as $fcPuG$useObjectRef, mergeProps as $fcPuG$mergeProps, getOwnerWindow as $fcPuG$getOwnerWindow, isFocusable as $fcPuG$isFocusable, mergeRefs as $fcPuG$mergeRefs } from \"@react-aria/utils\";\nimport $fcPuG$react, { useContext as $fcPuG$useContext, useRef as $fcPuG$useRef, useEffect as $fcPuG$useEffect, forwardRef as $fcPuG$forwardRef } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet $f645667febf57a63$export$f9762fab77588ecb = /*#__PURE__*/(0, $fcPuG$react).createContext(null);\nfunction $f645667febf57a63$var$useFocusableContext(ref) {\n  let context = (0, $fcPuG$useContext)($f645667febf57a63$export$f9762fab77588ecb) || {};\n  (0, $fcPuG$useSyncRef)(context, ref);\n  // eslint-disable-next-line\n  let {\n    ref: _,\n    ...otherProps\n  } = context;\n  return otherProps;\n}\nconst $f645667febf57a63$export$13f3202a3e5ddd5 = /*#__PURE__*/(0, $fcPuG$react).forwardRef(function FocusableProvider(props, ref) {\n  let {\n    children: children,\n    ...otherProps\n  } = props;\n  let objRef = (0, $fcPuG$useObjectRef)(ref);\n  let context = {\n    ...otherProps,\n    ref: objRef\n  };\n  return /*#__PURE__*/(0, $fcPuG$react).createElement($f645667febf57a63$export$f9762fab77588ecb.Provider, {\n    value: context\n  }, children);\n});\nfunction $f645667febf57a63$export$4c014de7c8940b4c(props, domRef) {\n  let {\n    focusProps: focusProps\n  } = (0, $a1ea59d68270f0dd$export$f8168d8dd8fd66e6)(props);\n  let {\n    keyboardProps: keyboardProps\n  } = (0, $46d819fcbaf35654$export$8f71654801c2f7cd)(props);\n  let interactions = (0, $fcPuG$mergeProps)(focusProps, keyboardProps);\n  let domProps = $f645667febf57a63$var$useFocusableContext(domRef);\n  let interactionProps = props.isDisabled ? {} : domProps;\n  let autoFocusRef = (0, $fcPuG$useRef)(props.autoFocus);\n  (0, $fcPuG$useEffect)(() => {\n    if (autoFocusRef.current && domRef.current) (0, $3ad3f6e1647bc98d$export$80f3e147d781571c)(domRef.current);\n    autoFocusRef.current = false;\n  }, [domRef]);\n  // Always set a tabIndex so that Safari allows focusing native buttons and inputs.\n  let tabIndex = props.excludeFromTabOrder ? -1 : 0;\n  if (props.isDisabled) tabIndex = undefined;\n  return {\n    focusableProps: (0, $fcPuG$mergeProps)({\n      ...interactions,\n      tabIndex: tabIndex\n    }, interactionProps)\n  };\n}\nconst $f645667febf57a63$export$35a3bebf7ef2d934 = /*#__PURE__*/(0, $fcPuG$forwardRef)(({\n  children: children,\n  ...props\n}, ref) => {\n  ref = (0, $fcPuG$useObjectRef)(ref);\n  let {\n    focusableProps: focusableProps\n  } = $f645667febf57a63$export$4c014de7c8940b4c(props, ref);\n  let child = (0, $fcPuG$react).Children.only(children);\n  (0, $fcPuG$useEffect)(() => {\n    if (process.env.NODE_ENV === 'production') return;\n    let el = ref.current;\n    if (!el || !(el instanceof (0, $fcPuG$getOwnerWindow)(el).Element)) {\n      console.error('<Focusable> child must forward its ref to a DOM element.');\n      return;\n    }\n    if (!props.isDisabled && !(0, $fcPuG$isFocusable)(el)) {\n      console.warn('<Focusable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n    if (el.localName !== 'button' && el.localName !== 'input' && el.localName !== 'select' && el.localName !== 'textarea' && el.localName !== 'a' && el.localName !== 'area' && el.localName !== 'summary' && el.localName !== 'img' && el.localName !== 'svg') {\n      let role = el.getAttribute('role');\n      if (!role) console.warn('<Focusable> child must have an interactive ARIA role.');else if (\n      // https://w3c.github.io/aria/#widget_roles\n      role !== 'application' && role !== 'button' && role !== 'checkbox' && role !== 'combobox' && role !== 'gridcell' && role !== 'link' && role !== 'menuitem' && role !== 'menuitemcheckbox' && role !== 'menuitemradio' && role !== 'option' && role !== 'radio' && role !== 'searchbox' && role !== 'separator' && role !== 'slider' && role !== 'spinbutton' && role !== 'switch' && role !== 'tab' && role !== 'tabpanel' && role !== 'textbox' && role !== 'treeitem' &&\n      // aria-describedby is also announced on these roles\n      role !== 'img' && role !== 'meter' && role !== 'progressbar') console.warn(`<Focusable> child must have an interactive ARIA role. Got \"${role}\".`);\n    }\n  }, [ref, props.isDisabled]);\n  // @ts-ignore\n  let childRef = parseInt((0, $fcPuG$react).version, 10) < 19 ? child.ref : child.props.ref;\n  return /*#__PURE__*/(0, $fcPuG$react).cloneElement(child, {\n    ...(0, $fcPuG$mergeProps)(focusableProps, child.props),\n    // @ts-ignore\n    ref: (0, $fcPuG$mergeRefs)(childRef, ref)\n  });\n});\nexport { $f645667febf57a63$export$f9762fab77588ecb as FocusableContext, $f645667febf57a63$export$13f3202a3e5ddd5 as FocusableProvider, $f645667febf57a63$export$4c014de7c8940b4c as useFocusable, $f645667febf57a63$export$35a3bebf7ef2d934 as Focusable };", "map": {"version": 3, "names": ["$f645667febf57a63$export$f9762fab77588ecb", "$fcPuG$react", "createContext", "$f645667febf57a63$var$useFocusableContext", "ref", "context", "$fcPuG$useContext", "$fcPuG$useSyncRef", "_", "otherProps", "$f645667febf57a63$export$13f3202a3e5ddd5", "forwardRef", "FocusableProvider", "props", "children", "objRef", "$fcPuG$useObjectRef", "createElement", "Provider", "value", "$f645667febf57a63$export$4c014de7c8940b4c", "domRef", "focusProps", "$a1ea59d68270f0dd$export$f8168d8dd8fd66e6", "keyboardProps", "$46d819fcbaf35654$export$8f71654801c2f7cd", "interactions", "$fcPuG$mergeProps", "domProps", "interactionProps", "isDisabled", "autoFocusRef", "$fcPuG$useRef", "autoFocus", "$fcPuG$useEffect", "current", "$3ad3f6e1647bc98d$export$80f3e147d781571c", "tabIndex", "excludeFromTabOrder", "undefined", "focusableProps", "$f645667febf57a63$export$35a3bebf7ef2d934", "$fcPuG$forwardRef", "child", "Children", "only", "process", "env", "NODE_ENV", "el", "$fcPuG$getOwnerWindow", "Element", "console", "error", "$fcPuG$isFocusable", "warn", "localName", "role", "getAttribute", "childRef", "parseInt", "version", "cloneElement", "$fcPuG$mergeRefs"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useFocusable.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableDOMProps, FocusableElement, FocusableProps, RefObject} from '@react-types/shared';\nimport {focusSafely} from './';\nimport {getOwnerWindow, isFocusable, mergeProps, mergeRefs, useObjectRef, useSyncRef} from '@react-aria/utils';\nimport React, {ForwardedRef, forwardRef, MutableRefObject, ReactElement, ReactNode, useContext, useEffect, useRef} from 'react';\nimport {useFocus} from './useFocus';\nimport {useKeyboard} from './useKeyboard';\n\nexport interface FocusableOptions<T = FocusableElement> extends FocusableProps<T>, FocusableDOMProps {\n  /** Whether focus should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusableProviderProps extends DOMAttributes {\n  /** The child element to provide DOM props to. */\n  children?: ReactNode\n}\n\ninterface FocusableContextValue extends FocusableProviderProps {\n  ref?: MutableRefObject<FocusableElement | null>\n}\n\n// Exported for @react-aria/collections, which forwards this context.\n/** @private */\nexport let FocusableContext = React.createContext<FocusableContextValue | null>(null);\n\nfunction useFocusableContext(ref: RefObject<FocusableElement | null>): FocusableContextValue {\n  let context = useContext(FocusableContext) || {};\n  useSyncRef(context, ref);\n\n  // eslint-disable-next-line\n  let {ref: _, ...otherProps} = context;\n  return otherProps;\n}\n\n/**\n * Provides DOM props to the nearest focusable child.\n */\nexport const FocusableProvider = React.forwardRef(function FocusableProvider(props: FocusableProviderProps, ref: ForwardedRef<FocusableElement>) {\n  let {children, ...otherProps} = props;\n  let objRef = useObjectRef(ref);\n  let context = {\n    ...otherProps,\n    ref: objRef\n  };\n\n  return (\n    <FocusableContext.Provider value={context}>\n      {children}\n    </FocusableContext.Provider>\n  );\n});\n\nexport interface FocusableAria {\n  /** Props for the focusable element. */\n  focusableProps: DOMAttributes\n}\n\n/**\n * Used to make an element focusable and capable of auto focus.\n */\nexport function useFocusable<T extends FocusableElement = FocusableElement>(props: FocusableOptions<T>, domRef: RefObject<FocusableElement | null>): FocusableAria {\n  let {focusProps} = useFocus(props);\n  let {keyboardProps} = useKeyboard(props);\n  let interactions = mergeProps(focusProps, keyboardProps);\n  let domProps = useFocusableContext(domRef);\n  let interactionProps = props.isDisabled ? {} : domProps;\n  let autoFocusRef = useRef(props.autoFocus);\n\n  useEffect(() => {\n    if (autoFocusRef.current && domRef.current) {\n      focusSafely(domRef.current);\n    }\n    autoFocusRef.current = false;\n  }, [domRef]);\n\n  // Always set a tabIndex so that Safari allows focusing native buttons and inputs.\n  let tabIndex: number | undefined = props.excludeFromTabOrder ? -1 : 0;\n  if (props.isDisabled) {\n    tabIndex = undefined;\n  }\n\n  return {\n    focusableProps: mergeProps(\n      {\n        ...interactions,\n        tabIndex\n      },\n      interactionProps\n    )\n  };\n}\n\nexport interface FocusableComponentProps extends FocusableOptions {\n  children: ReactElement<DOMAttributes, string>\n}\n\nexport const Focusable = forwardRef(({children, ...props}: FocusableComponentProps, ref: ForwardedRef<FocusableElement>) => {\n  ref = useObjectRef(ref);\n  let {focusableProps} = useFocusable(props, ref);\n  let child = React.Children.only(children);\n\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n\n    let el = ref.current;\n    if (!el || !(el instanceof getOwnerWindow(el).Element)) {\n      console.error('<Focusable> child must forward its ref to a DOM element.');\n      return;\n    }\n\n    if (!props.isDisabled && !isFocusable(el)) {\n      console.warn('<Focusable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n\n    if (\n      el.localName !== 'button' &&\n      el.localName !== 'input' &&\n      el.localName !== 'select' &&\n      el.localName !== 'textarea' &&\n      el.localName !== 'a' &&\n      el.localName !== 'area' &&\n      el.localName !== 'summary' &&\n      el.localName !== 'img' &&\n      el.localName !== 'svg'\n    ) {\n      let role = el.getAttribute('role');\n      if (!role) {\n        console.warn('<Focusable> child must have an interactive ARIA role.');\n      } else if (\n        // https://w3c.github.io/aria/#widget_roles\n        role !== 'application' &&\n        role !== 'button' &&\n        role !== 'checkbox' &&\n        role !== 'combobox' &&\n        role !== 'gridcell' &&\n        role !== 'link' &&\n        role !== 'menuitem' &&\n        role !== 'menuitemcheckbox' &&\n        role !== 'menuitemradio' &&\n        role !== 'option' &&\n        role !== 'radio' &&\n        role !== 'searchbox' &&\n        role !== 'separator' &&\n        role !== 'slider' &&\n        role !== 'spinbutton' &&\n        role !== 'switch' &&\n        role !== 'tab' &&\n        role !== 'tabpanel' &&\n        role !== 'textbox' &&\n        role !== 'treeitem' &&\n        // aria-describedby is also announced on these roles\n        role !== 'img' &&\n        role !== 'meter' &&\n        role !== 'progressbar'\n      ) {\n        console.warn(`<Focusable> child must have an interactive ARIA role. Got \"${role}\".`);\n      }\n    }\n  }, [ref, props.isDisabled]);\n\n  // @ts-ignore\n  let childRef = parseInt(React.version, 10) < 19 ? child.ref : child.props.ref;\n\n  return React.cloneElement(\n    child,\n    {\n      ...mergeProps(focusableProps, child.props),\n      // @ts-ignore\n      ref: mergeRefs(childRef, ref)\n    }\n  );\n});\n"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;AAmCO,IAAIA,yCAAA,gBAAmB,IAAAC,YAAI,EAAEC,aAAa,CAA+B;AAEhF,SAASC,0CAAoBC,GAAuC;EAClE,IAAIC,OAAA,GAAU,IAAAC,iBAAS,EAAEN,yCAAA,KAAqB,CAAC;EAC/C,IAAAO,iBAAS,EAAEF,OAAA,EAASD,GAAA;EAEpB;EACA,IAAI;IAACA,GAAA,EAAKI,CAAC;IAAE,GAAGC;EAAA,CAAW,GAAGJ,OAAA;EAC9B,OAAOI,UAAA;AACT;AAKO,MAAMC,wCAAA,gBAAoB,IAAAT,YAAI,EAAEU,UAAU,CAAC,SAASC,kBAAkBC,KAA6B,EAAET,GAAmC;EAC7I,IAAI;IAAAU,QAAA,EAACA,QAAQ;IAAE,GAAGL;EAAA,CAAW,GAAGI,KAAA;EAChC,IAAIE,MAAA,GAAS,IAAAC,mBAAW,EAAEZ,GAAA;EAC1B,IAAIC,OAAA,GAAU;IACZ,GAAGI,UAAU;IACbL,GAAA,EAAKW;EACP;EAEA,oBACE,IAAAd,YAAA,EAAAgB,aAAA,CAACjB,yCAAA,CAAiBkB,QAAQ;IAACC,KAAA,EAAOd;KAC/BS,QAAA;AAGP;AAUO,SAASM,0CAA4DP,KAA0B,EAAEQ,MAA0C;EAChJ,IAAI;IAAAC,UAAA,EAACA;EAAU,CAAC,GAAG,IAAAC,yCAAO,EAAEV,KAAA;EAC5B,IAAI;IAAAW,aAAA,EAACA;EAAa,CAAC,GAAG,IAAAC,yCAAU,EAAEZ,KAAA;EAClC,IAAIa,YAAA,GAAe,IAAAC,iBAAS,EAAEL,UAAA,EAAYE,aAAA;EAC1C,IAAII,QAAA,GAAWzB,yCAAA,CAAoBkB,MAAA;EACnC,IAAIQ,gBAAA,GAAmBhB,KAAA,CAAMiB,UAAU,GAAG,CAAC,IAAIF,QAAA;EAC/C,IAAIG,YAAA,GAAe,IAAAC,aAAK,EAAEnB,KAAA,CAAMoB,SAAS;EAEzC,IAAAC,gBAAQ,EAAE;IACR,IAAIH,YAAA,CAAaI,OAAO,IAAId,MAAA,CAAOc,OAAO,EACxC,IAAAC,yCAAU,EAAEf,MAAA,CAAOc,OAAO;IAE5BJ,YAAA,CAAaI,OAAO,GAAG;EACzB,GAAG,CAACd,MAAA,CAAO;EAEX;EACA,IAAIgB,QAAA,GAA+BxB,KAAA,CAAMyB,mBAAmB,GAAG,KAAK;EACpE,IAAIzB,KAAA,CAAMiB,UAAU,EAClBO,QAAA,GAAWE,SAAA;EAGb,OAAO;IACLC,cAAA,EAAgB,IAAAb,iBAAS,EACvB;MACE,GAAGD,YAAY;gBACfW;IACF,GACAR,gBAAA;EAEJ;AACF;AAMO,MAAMY,yCAAA,gBAAY,IAAAC,iBAAS,EAAE,CAAC;EAAA5B,QAAA,EAACA,QAAQ;EAAE,GAAGD;AAAA,CAA+B,EAAET,GAAA;EAClFA,GAAA,GAAM,IAAAY,mBAAW,EAAEZ,GAAA;EACnB,IAAI;IAAAoC,cAAA,EAACA;EAAc,CAAC,GAAGpB,yCAAA,CAAaP,KAAA,EAAOT,GAAA;EAC3C,IAAIuC,KAAA,GAAQ,IAAA1C,YAAI,EAAE2C,QAAQ,CAACC,IAAI,CAAC/B,QAAA;EAEhC,IAAAoB,gBAAQ,EAAE;IACR,IAAIY,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,cAC3B;IAGF,IAAIC,EAAA,GAAK7C,GAAA,CAAI+B,OAAO;IACpB,IAAI,CAACc,EAAA,IAAM,EAAEA,EAAA,YAAc,IAAAC,qBAAa,EAAED,EAAA,EAAIE,OAAO,CAAD,EAAI;MACtDC,OAAA,CAAQC,KAAK,CAAC;MACd;IACF;IAEA,IAAI,CAACxC,KAAA,CAAMiB,UAAU,IAAI,CAAC,IAAAwB,kBAAU,EAAEL,EAAA,GAAK;MACzCG,OAAA,CAAQG,IAAI,CAAC;MACb;IACF;IAEA,IACEN,EAAA,CAAGO,SAAS,KAAK,YACjBP,EAAA,CAAGO,SAAS,KAAK,WACjBP,EAAA,CAAGO,SAAS,KAAK,YACjBP,EAAA,CAAGO,SAAS,KAAK,cACjBP,EAAA,CAAGO,SAAS,KAAK,OACjBP,EAAA,CAAGO,SAAS,KAAK,UACjBP,EAAA,CAAGO,SAAS,KAAK,aACjBP,EAAA,CAAGO,SAAS,KAAK,SACjBP,EAAA,CAAGO,SAAS,KAAK,OACjB;MACA,IAAIC,IAAA,GAAOR,EAAA,CAAGS,YAAY,CAAC;MAC3B,IAAI,CAACD,IAAA,EACHL,OAAA,CAAQG,IAAI,CAAC,8DACR;MACL;MACAE,IAAA,KAAS,iBACTA,IAAA,KAAS,YACTA,IAAA,KAAS,cACTA,IAAA,KAAS,cACTA,IAAA,KAAS,cACTA,IAAA,KAAS,UACTA,IAAA,KAAS,cACTA,IAAA,KAAS,sBACTA,IAAA,KAAS,mBACTA,IAAA,KAAS,YACTA,IAAA,KAAS,WACTA,IAAA,KAAS,eACTA,IAAA,KAAS,eACTA,IAAA,KAAS,YACTA,IAAA,KAAS,gBACTA,IAAA,KAAS,YACTA,IAAA,KAAS,SACTA,IAAA,KAAS,cACTA,IAAA,KAAS,aACTA,IAAA,KAAS;MACT;MACAA,IAAA,KAAS,SACTA,IAAA,KAAS,WACTA,IAAA,KAAS,eAETL,OAAA,CAAQG,IAAI,CAAC,8DAA8DE,IAAA,IAAQ;IAEvF;EACF,GAAG,CAACrD,GAAA,EAAKS,KAAA,CAAMiB,UAAU,CAAC;EAE1B;EACA,IAAI6B,QAAA,GAAWC,QAAA,CAAS,IAAA3D,YAAI,EAAE4D,OAAO,EAAE,MAAM,KAAKlB,KAAA,CAAMvC,GAAG,GAAGuC,KAAA,CAAM9B,KAAK,CAACT,GAAG;EAE7E,oBAAO,IAAAH,YAAI,EAAE6D,YAAY,CACvBnB,KAAA,EACA;IACE,GAAG,IAAAhB,iBAAS,EAAEa,cAAA,EAAgBG,KAAA,CAAM9B,KAAK,CAAC;IAC1C;IACAT,GAAA,EAAK,IAAA2D,gBAAQ,EAAEJ,QAAA,EAAUvD,GAAA;EAC3B;AAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}