{"ast": null, "code": "import { SSRProvider as $b5e257d569688ac6$export$9f8ac96af4b1b2ae, useIsSSR as $b5e257d569688ac6$export$535bd6ca7f90a273, useSSRSafeId as $b5e257d569688ac6$export$619500959fc48b26 } from \"./SSRProvider.mjs\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport { $b5e257d569688ac6$export$9f8ac96af4b1b2ae as SSR<PERSON>rovider, $b5e257d569688ac6$export$619500959fc48b26 as useSSRSafeId, $b5e257d569688ac6$export$535bd6ca7f90a273 as useIsSSR };", "map": {"version": 3, "names": [], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\packages\\@react-aria\\ssr\\src\\index.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {SSRProvider, useSSRSafeId, useIsSSR} from './SSRProvider';\nexport type {SSRProviderProps} from './SSRProvider';\n"], "mappings": ";;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}