{"ast": null, "code": "import { useFocusRing as $f7dceffc5ad7768b$export$4e328f61c538687f } from \"./useFocusRing.mjs\";\nimport $hAmeg$clsx from \"clsx\";\nimport { mergeProps as $hAmeg$mergeProps } from \"@react-aria/utils\";\nimport $hAmeg$react from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $907718708eab68af$export$1a38b4ad7f578e1d(props) {\n  let {\n    children: children,\n    focusClass: focusClass,\n    focusRingClass: focusRingClass\n  } = props;\n  let {\n    isFocused: isFocused,\n    isFocusVisible: isFocusVisible,\n    focusProps: focusProps\n  } = (0, $f7dceffc5ad7768b$export$4e328f61c538687f)(props);\n  let child = (0, $hAmeg$react).Children.only(children);\n  return /*#__PURE__*/(0, $hAmeg$react).cloneElement(child, (0, $hAmeg$mergeProps)(child.props, {\n    ...focusProps,\n    className: (0, $hAmeg$clsx)({\n      [focusClass || '']: isFocused,\n      [focusRingClass || '']: isFocusVisible\n    })\n  }));\n}\nexport { $907718708eab68af$export$1a38b4ad7f578e1d as FocusRing };", "map": {"version": 3, "names": ["$907718708eab68af$export$1a38b4ad7f578e1d", "props", "children", "focusClass", "focusRingClass", "isFocused", "isFocusVisible", "focusProps", "$f7dceffc5ad7768b$export$4e328f61c538687f", "child", "$hAmeg$react", "Children", "only", "cloneElement", "$hAmeg$mergeProps", "className", "$hAmeg$clsx"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\focus\\dist\\packages\\@react-aria\\focus\\src\\FocusRing.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport clsx from 'clsx';\nimport {mergeProps} from '@react-aria/utils';\nimport React, {ReactElement} from 'react';\nimport {useFocusRing} from './useFocusRing';\n\nexport interface FocusRingProps {\n  /** Child element to apply CSS classes to. */\n  children: ReactElement,\n  /** CSS class to apply when the element is focused. */\n  focusClass?: string,\n  /** CSS class to apply when the element has keyboard focus. */\n  focusRingClass?: string,\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default false\n   */\n  within?: boolean,\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\n/**\n * A utility component that applies a CSS class when an element has keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function FocusRing(props: FocusRingProps): React.ReactElement<unknown, string | React.JSXElementConstructor<any>> {\n  let {children, focusClass, focusRingClass} = props;\n  let {isFocused, isFocusVisible, focusProps} = useFocusRing(props);\n  let child = React.Children.only(children);\n\n  return React.cloneElement(child, mergeProps(child.props as any, {\n    ...focusProps,\n    className: clsx({\n      [focusClass || '']: isFocused,\n      [focusRingClass || '']: isFocusVisible\n    })\n  }));\n}\n"], "mappings": ";;;;;AAAA;;;;;;;;;;;;AA0CO,SAASA,0CAAUC,KAAqB;EAC7C,IAAI;IAAAC,QAAA,EAACA,QAAQ;IAAAC,UAAA,EAAEA,UAAU;IAAAC,cAAA,EAAEA;EAAc,CAAC,GAAGH,KAAA;EAC7C,IAAI;IAAAI,SAAA,EAACA,SAAS;IAAAC,cAAA,EAAEA,cAAc;IAAAC,UAAA,EAAEA;EAAU,CAAC,GAAG,IAAAC,yCAAW,EAAEP,KAAA;EAC3D,IAAIQ,KAAA,GAAQ,IAAAC,YAAI,EAAEC,QAAQ,CAACC,IAAI,CAACV,QAAA;EAEhC,oBAAO,IAAAQ,YAAI,EAAEG,YAAY,CAACJ,KAAA,EAAO,IAAAK,iBAAS,EAAEL,KAAA,CAAMR,KAAK,EAAS;IAC9D,GAAGM,UAAU;IACbQ,SAAA,EAAW,IAAAC,WAAG,EAAE;MACd,CAACb,UAAA,IAAc,KAAKE,SAAA;MACpB,CAACD,cAAA,IAAkB,KAAKE;IAC1B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}