{"ast": null, "code": "import { useState as u } from \"react\";\nfunction l(e) {\n  let [t] = u(e);\n  return t;\n}\nexport { l as useDefaultValue };", "map": {"version": 3, "names": ["useState", "u", "l", "e", "t", "useDefaultValue"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/use-default-value.js"], "sourcesContent": ["import{useState as u}from\"react\";function l(e){let[t]=u(e);return t}export{l as useDefaultValue};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,CAAC,GAACH,CAAC,CAACE,CAAC,CAAC;EAAC,OAAOC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}