{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{usePara<PERSON>,useNavigate,Link}from'react-router-dom';import{useTranslation}from'react-i18next';import{Container,Grid,Paper,Typography,Box,Avatar,Divider,Button,Chip,Rating,Card,CardContent,List,ListItem,ListItemText,ListItemAvatar,CircularProgress,Alert,useTheme,useMediaQuery,IconButton,Tooltip,Dialog,DialogContent,DialogTitle,DialogActions,IconButton as MuiIconButton,TextField}from'@mui/material';import{Language as LanguageIcon,School as SchoolIcon,AttachMoney as MoneyIcon,AccessTime as AccessTimeIcon,PlayCircleOutline as PlayCircleOutlineIcon,ArrowBack as ArrowBackIcon,Message as MessageIcon,CalendarMonth as CalendarMonthIcon,Verified as VerifiedIcon,Close as CloseIcon,Send as SendIcon}from'@mui/icons-material';import axios from'../../utils/axios';import{useAuth}from'../../contexts/AuthContext';import{useSocket}from'../../contexts/SocketContext';import{format}from'date-fns';import{ar,enUS}from'date-fns/locale';import Layout from'../../components/Layout';import AvailableHoursTable from'../../components/AvailableHoursTable';import ProfileCompletionAlert from'../../components/student/ProfileCompletionAlert';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const TeacherProfile=()=>{var _studentProfile2;const{id}=useParams();const{t,i18n}=useTranslation();const theme=useTheme();const navigate=useNavigate();const{currentUser,token}=useAuth();const isMobile=useMediaQuery(theme.breakpoints.down('sm'));const isRtl=i18n.language==='ar';const[teacher,setTeacher]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[availableSlots,setAvailableSlots]=useState([]);const[loadingSlots,setLoadingSlots]=useState(false);useEffect(()=>{const fetchTeacherDetails=async()=>{try{setLoading(true);const{data}=await axios.get(`/teachers/${id}`,{headers:{'Authorization':`Bearer ${token}`}});if(data.success){setTeacher(data.data);}}catch(error){console.error('Error fetching teacher details:',error);setError(t('teacherDetails.errorFetching'));}finally{setLoading(false);}};if(token){fetchTeacherDetails();}},[id,t,token]);// Fetch available slots\nuseEffect(()=>{const fetchAvailableSlots=async()=>{if(!id||!token)return;try{var _studentProfile;setLoadingSlots(true);// Build URL with student timezone if available\nlet url=`/teachers/${id}/available-slots`;if((_studentProfile=studentProfile)!==null&&_studentProfile!==void 0&&_studentProfile.timezone){url+=`?student_timezone=${encodeURIComponent(studentProfile.timezone)}`;}const{data}=await axios.get(url,{headers:{'Authorization':`Bearer ${token}`}});if(data.success){setAvailableSlots(data.data);}else{console.error('Error in API response:',data.message);setAvailableSlots([]);}}catch(error){console.error('Error fetching available slots:',error);setAvailableSlots([]);}finally{setLoadingSlots(false);}};fetchAvailableSlots();},[id,token,t,(_studentProfile2=studentProfile)===null||_studentProfile2===void 0?void 0:_studentProfile2.timezone]);const[showChat,setShowChat]=useState(false);const[chatId,setChatId]=useState(null);const[chatLoading,setChatLoading]=useState(false);const[chatMessages,setChatMessages]=useState([]);const[messageInput,setMessageInput]=useState('');const{socket}=useSocket();const handleStartChat=async()=>{if(!currentUser||!token){navigate('/login',{state:{from:`/student/teacher/${id}`}});return;}try{setChatLoading(true);const{data}=await axios.get(`/chat/conversation/${id}`,{headers:{'Authorization':`Bearer ${token}`}});if(data.success){setChatId(data.data.id);setShowChat(true);}else{console.error('Failed to start chat:',data.message);}}catch(error){console.error('Error starting chat:',error);}finally{setChatLoading(false);}};const handleCloseChat=()=>{setShowChat(false);setChatMessages([]);setMessageInput('');};const handleSendClick=()=>{if(messageInput.trim()){handleSendMessage(messageInput);setMessageInput('');}};const handleSendMessage=useCallback(content=>{if(!socket||!chatId||!content.trim()||!currentUser)return;// Create a temporary message with a unique ID\nconst tempId=`temp-${Date.now()}`;// Get current timestamp in seconds\nconst timestamp=Math.floor(Date.now()/1000);const tempMessage={id:tempId,conversation_id:chatId,sender_id:currentUser.id,content,created_at:timestamp,is_read:false,is_temp:true};// Add to local messages immediately\nsetChatMessages(prev=>[...prev,tempMessage]);// Send to server\nsocket.emit('send_message',{chatId:chatId,content,tempId,timestamp});},[socket,chatId,id,currentUser]);// Listen for new messages\nuseEffect(()=>{if(!socket||!chatId)return;// Listen for new messages\nsocket.on('new_message',message=>{// Convert MySQL timestamp to Unix timestamp\nif(message.created_at&&typeof message.created_at==='string'){message.created_at=Math.floor(new Date(message.created_at.replace(' ','T')).getTime()/1000);}// Add message to messages list if it's for the current chat\nif(message.conversation_id===chatId){setChatMessages(prev=>[...prev,message]);// Mark message as read\nsocket.emit('mark_messages_read',{chatId:chatId});}});// Listen for message sent confirmation\nsocket.on('message_sent',_ref=>{let{success,message,tempId}=_ref;if(success){// Convert MySQL timestamp to Unix timestamp\nif(message.created_at&&typeof message.created_at==='string'){message.created_at=Math.floor(new Date(message.created_at.replace(' ','T')).getTime()/1000);}setChatMessages(prev=>prev.map(msg=>msg.id===tempId?{...message}:msg));}else{// Remove temporary message if sending failed\nsetChatMessages(prev=>prev.filter(msg=>msg.id!==tempId));}});return()=>{socket.off('new_message');socket.off('message_sent');};},[socket,chatId]);// Fetch chat messages when chat is opened\nuseEffect(()=>{if(!socket||!chatId||!showChat)return;setChatLoading(true);socket.emit('get_chat_messages',{chatId:chatId},response=>{if(response.success){// Convert all MySQL timestamps to Unix timestamps\nconst convertedMessages=response.messages.map(msg=>{if(msg.created_at&&typeof msg.created_at==='string'){return{...msg,created_at:Math.floor(new Date(msg.created_at.replace(' ','T')).getTime()/1000)};}return msg;});setChatMessages(convertedMessages);// Mark messages as read\nsocket.emit('mark_messages_read',{chatId:chatId});}else{console.error('Error fetching messages:',response.error);}setChatLoading(false);});},[socket,chatId,showChat]);const handleBookLesson=()=>{if(!currentUser||!token){navigate('/login',{state:{from:`/student/teacher/${id}`}});return;}// Navigate to booking page\nnavigate(`/student/book/${id}`);};const handleBookSlot=async(slot,date)=>{if(!currentUser||!token){navigate('/login',{state:{from:`/student/teacher/${id}`}});return;}try{const{data}=await axios.post('/bookings',{teacher_id:id,slot_id:slot.id,date:date},{headers:{'Authorization':`Bearer ${token}`}});if(data.success){// Show success message\nalert(t('booking.bookingSuccess'));// Refresh available slots\nfetchAvailableSlots();}else{alert(data.message||t('booking.bookingFailed'));}}catch(error){var _error$response,_error$response$data;console.error('Error booking slot:',error);alert(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||t('booking.bookingFailed'));}};const fetchAvailableSlots=async()=>{if(!id||!token)return;try{var _studentProfile3;setLoadingSlots(true);// Build URL with student timezone if available\nlet url=`/teachers/${id}/available-slots`;if((_studentProfile3=studentProfile)!==null&&_studentProfile3!==void 0&&_studentProfile3.timezone){url+=`?student_timezone=${encodeURIComponent(studentProfile.timezone)}`;}const{data}=await axios.get(url,{headers:{'Authorization':`Bearer ${token}`}});if(data.success){setAvailableSlots(data.data);}else{console.error('Error in API response:',data.message);setAvailableSlots([]);}}catch(error){console.error('Error fetching available slots:',error);setAvailableSlots([]);}finally{setLoadingSlots(false);}};// Function to check if a URL is a local video file\nconst isLocalVideoFile=url=>{if(!url)return false;return url.startsWith('/uploads/')&&(url.endsWith('.mp4')||url.endsWith('.webm')||url.endsWith('.ogg'));};// Function to check if a URL is a YouTube video\nconst isYoutubeVideo=url=>{if(!url)return false;return url.includes('youtube.com')||url.includes('youtu.be');};// Function to extract YouTube video ID from URL\nconst getYoutubeVideoId=url=>{if(!url)return null;try{// Regular expressions to match different YouTube URL formats\nconst regExp=/^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;const match=url.match(regExp);if(match&&match[2]&&match[2].length===11){return match[2];}// If the URL is already a video ID (11 characters)\nif(url.length===11&&/^[a-zA-Z0-9_-]{11}$/.test(url)){return url;}console.log('Could not extract YouTube video ID from:',url);return null;}catch(error){console.error('Error extracting YouTube video ID:',error);return null;}};const content=/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",sx:{py:4},children:loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',minHeight:'50vh'},children:/*#__PURE__*/_jsx(CircularProgress,{})}):error||!teacher?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:error||t('teacherDetails.teacherNotFound')}),/*#__PURE__*/_jsx(Box,{sx:{mt:2,display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),component:Link,to:\"/student/find-teacher\",children:t('teacherDetails.backToSearch')})})]}):/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(ProfileCompletionAlert,{exemptPages:['/student/complete-profile','/student/dashboard'],children:[/*#__PURE__*/_jsx(Box,{sx:{mb:3,display:'flex',justifyContent:'flex-start'},children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),component:Link,to:\"/student/find-teacher\",children:t('teacherDetails.backToSearch')})}),/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:3,mb:4,borderRadius:2},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:3,sx:{display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(Avatar,{src:teacher.profile_picture_url?teacher.profile_picture_url.startsWith('http')?teacher.profile_picture_url:`https://allemnionline.com${teacher.profile_picture_url}`:'',alt:teacher.full_name,sx:{width:{xs:120,md:150},height:{xs:120,md:150},mb:2,border:'4px solid',borderColor:'primary.main'}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"h1\",sx:{fontWeight:'bold',mr:1},children:teacher.full_name}),teacher.is_verified&&/*#__PURE__*/_jsx(Tooltip,{title:t('search.verifiedTeacher'),children:/*#__PURE__*/_jsx(VerifiedIcon,{color:\"primary\"})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Rating,{value:teacher.average_rating,precision:0.5,readOnly:true}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{ml:1},children:[\"(\",teacher.review_count,\" \",t('teacherDetails.reviews'),\")\"]})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:'100%',display:'flex',flexDirection:'column',gap:1},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(MessageIcon,{}),onClick:handleStartChat,fullWidth:true,children:t('chat.startChat')}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"secondary\",startIcon:/*#__PURE__*/_jsx(CalendarMonthIcon,{}),onClick:handleBookLesson,fullWidth:true,children:t('teacherDetails.bookLesson')})]})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:9,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,sx:{display:'flex',alignItems:'center',gap:1,borderBottom:1,borderColor:'divider',pb:1},children:[/*#__PURE__*/_jsx(SchoolIcon,{color:\"primary\"}),t('teacherDetails.teachingSubjects')]}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:1,ml:4},children:teacher.subjects.map((subject,index)=>/*#__PURE__*/_jsx(Chip,{label:subject,color:\"primary\",variant:\"outlined\"},index))})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,sx:{display:'flex',alignItems:'center',gap:1,borderBottom:1,borderColor:'divider',pb:1},children:[/*#__PURE__*/_jsx(LanguageIcon,{color:\"primary\"}),t('teacherDetails.languages')]}),/*#__PURE__*/_jsxs(Box,{sx:{ml:4},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{mb:1},children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('teacherDetails.nativeLanguage'),\":\"]}),\" \",teacher.native_language]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:1},children:/*#__PURE__*/_jsxs(\"strong\",{children:[t('teacherDetails.teachingLanguages'),\":\"]})}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:1,ml:2},children:teacher.teaching_languages.map((language,index)=>/*#__PURE__*/_jsx(Chip,{label:language,size:\"small\",variant:\"outlined\"},index))})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,sx:{display:'flex',alignItems:'center',gap:1,borderBottom:1,borderColor:'divider',pb:1},children:[/*#__PURE__*/_jsx(AccessTimeIcon,{color:\"primary\"}),t('teacherDetails.experience')]}),/*#__PURE__*/_jsxs(Box,{sx:{ml:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:1},children:t('teacherDetails.yearsOfExperience',{years:teacher.teaching_experience})}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:1},children:/*#__PURE__*/_jsxs(\"strong\",{children:[t('teacherDetails.qualifications'),\":\"]})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{ml:2},children:teacher.qualifications})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,sx:{display:'flex',alignItems:'center',gap:1,borderBottom:1,borderColor:'divider',pb:1},children:[/*#__PURE__*/_jsx(MoneyIcon,{color:\"primary\"}),t('teacherDetails.paymentInfo')]}),/*#__PURE__*/_jsxs(Box,{sx:{ml:4},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{mb:0.5,fontWeight:'bold',fontSize:'1.2rem',color:'primary.main'},children:[\"$\",teacher.price_per_lesson,\"/hr\"]}),teacher.trial_lesson_price&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{ml:0.2},children:[\"Trial: $\",teacher.trial_lesson_price]})]})]})})]})})]})}),teacher.intro_video_url&&/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:3,mb:4,borderRadius:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",gutterBottom:true,sx:{borderBottom:1,borderColor:'divider',pb:1,display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(PlayCircleOutlineIcon,{color:\"primary\"}),t('teacherDetails.introVideo')]}),/*#__PURE__*/_jsx(Box,{sx:{mt:2,position:'relative',paddingTop:'56.25%',width:'100%'},children:isYoutubeVideo(teacher.intro_video_url)?/*#__PURE__*/// YouTube video embed\n_jsx(\"iframe\",{src:`https://www.youtube.com/embed/${getYoutubeVideoId(teacher.intro_video_url)}`,title:\"YouTube video player\",frameBorder:\"0\",allow:\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",allowFullScreen:true,style:{position:'absolute',top:0,left:0,width:'100%',height:'100%',borderRadius:'8px'}}):isLocalVideoFile(teacher.intro_video_url)?/*#__PURE__*/// Local video file\n_jsx(\"video\",{style:{position:'absolute',top:0,left:0,width:'100%',height:'100%',borderRadius:'8px'},src:`https://allemnionline.com${teacher.intro_video_url}`,controls:true,preload:\"metadata\"}):/*#__PURE__*/// External video URL (not YouTube)\n_jsx(Box,{sx:{position:'absolute',top:'50%',left:'50%',transform:'translate(-50%, -50%)',textAlign:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:/*#__PURE__*/_jsx(\"a\",{href:teacher.intro_video_url,target:\"_blank\",rel:\"noopener noreferrer\",children:t('teacherDetails.openVideo')})})})})]}),teacher.available_hours&&(()=>{try{const parsedHours=typeof teacher.available_hours==='string'?JSON.parse(teacher.available_hours):teacher.available_hours;return/*#__PURE__*/_jsx(AvailableHoursTable,{availableHours:parsedHours,loading:loading,showStats:true});}catch(error){console.error('Error parsing available hours:',error);return null;}})(),/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:3,mb:4,borderRadius:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",gutterBottom:true,sx:{borderBottom:1,borderColor:'divider',pb:1},children:t('teacherDetails.studentReviews')}),teacher.reviews&&teacher.reviews.length>0?/*#__PURE__*/_jsx(List,{children:teacher.reviews.map(review=>/*#__PURE__*/_jsxs(ListItem,{alignItems:\"flex-start\",sx:{borderBottom:'1px solid',borderColor:'divider',py:2},children:[/*#__PURE__*/_jsx(ListItemAvatar,{children:/*#__PURE__*/_jsx(Avatar,{src:review.student_profile_picture,alt:review.student_name,children:review.student_name.charAt(0)})}),/*#__PURE__*/_jsx(ListItemText,{primary:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",component:\"span\",children:review.student_name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:format(new Date(review.created_at),'PPP',{locale:isRtl?ar:enUS})})]}),secondary:/*#__PURE__*/_jsxs(Box,{sx:{mt:1},children:[/*#__PURE__*/_jsx(Rating,{value:review.rating,readOnly:true,size:\"small\",sx:{mb:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",component:\"p\",sx:{mb:2},children:review.comment}),review.reply_text&&/*#__PURE__*/_jsxs(Box,{sx:{bgcolor:'primary.light',p:2,borderRadius:1,mt:2,border:'1px solid',borderColor:'primary.main'},children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle2\",color:\"primary\",sx:{fontWeight:'bold'},children:[t('reviews.teacherReply'),\":\"]})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'primary.dark'},children:review.reply_text}),review.reply_created_at&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{mt:1,display:'block'},children:format(new Date(review.reply_created_at),'PPP',{locale:isRtl?ar:enUS})})]})]})})]},review.id))}):/*#__PURE__*/_jsx(Box,{sx:{py:4,textAlign:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:t('teacherDetails.noReviews')})})]}),/*#__PURE__*/_jsxs(Dialog,{open:showChat,onClose:handleCloseChat,maxWidth:\"md\",fullWidth:true,PaperProps:{sx:{height:'80vh',maxHeight:'700px',display:'flex',flexDirection:'column'}},children:[/*#__PURE__*/_jsx(DialogTitle,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Avatar,{src:teacher.profile_picture_url?teacher.profile_picture_url.startsWith('http')?teacher.profile_picture_url:`https://allemnionline.com${teacher.profile_picture_url}`:'',alt:teacher.full_name,sx:{mr:1},children:teacher.full_name.charAt(0)}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:teacher.full_name})]}),/*#__PURE__*/_jsx(IconButton,{onClick:handleCloseChat,size:\"small\",children:/*#__PURE__*/_jsx(CloseIcon,{})})]})}),/*#__PURE__*/_jsx(DialogContent,{sx:{p:0,flexGrow:1,display:'flex',flexDirection:'column'},children:chatLoading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',height:'100%'},children:/*#__PURE__*/_jsx(CircularProgress,{})}):/*#__PURE__*/_jsxs(Box,{sx:{height:'100%',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Box,{sx:{flexGrow:1,overflow:'auto',p:2},children:chatMessages.length===0?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',height:'100%'},children:/*#__PURE__*/_jsx(Typography,{color:\"text.secondary\",children:t('chat.startConversation')})}):chatMessages.map(message=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:message.sender_id===(currentUser===null||currentUser===void 0?void 0:currentUser.id)?'flex-end':'flex-start',mb:2},children:[message.sender_id!==(currentUser===null||currentUser===void 0?void 0:currentUser.id)&&/*#__PURE__*/_jsx(Avatar,{src:teacher.profile_picture_url?teacher.profile_picture_url.startsWith('http')?teacher.profile_picture_url:`https://allemnionline.com${teacher.profile_picture_url}`:'',alt:teacher.full_name,sx:{mr:1,width:32,height:32},children:teacher.full_name.charAt(0)}),/*#__PURE__*/_jsxs(Box,{sx:{maxWidth:'70%',p:2,bgcolor:message.sender_id===(currentUser===null||currentUser===void 0?void 0:currentUser.id)?'primary.main':'grey.100',color:message.sender_id===(currentUser===null||currentUser===void 0?void 0:currentUser.id)?'white':'text.primary',borderRadius:2,position:'relative'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:message.content}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:'block',textAlign:message.sender_id===(currentUser===null||currentUser===void 0?void 0:currentUser.id)?'right':'left',mt:0.5,color:message.sender_id===(currentUser===null||currentUser===void 0?void 0:currentUser.id)?'rgba(255,255,255,0.7)':'text.secondary'},children:new Date(message.created_at*1000).toLocaleTimeString()})]})]},message.id))}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(Box,{sx:{p:2,backgroundColor:'background.default'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:t('chat.typeMessage'),variant:\"outlined\",size:\"small\",value:messageInput,onChange:e=>setMessageInput(e.target.value),onKeyPress:e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();handleSendClick();}},sx:{mr:1}}),/*#__PURE__*/_jsx(IconButton,{color:\"primary\",onClick:handleSendClick,disabled:!messageInput.trim(),children:/*#__PURE__*/_jsx(SendIcon,{})})]})})]})})]})]})})});return/*#__PURE__*/_jsx(Layout,{children:content});};export default TeacherProfile;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "Link", "useTranslation", "Container", "Grid", "Paper", "Typography", "Box", "Avatar", "Divider", "<PERSON><PERSON>", "Chip", "Rating", "Card", "<PERSON><PERSON><PERSON><PERSON>", "List", "ListItem", "ListItemText", "ListItemAvatar", "CircularProgress", "<PERSON><PERSON>", "useTheme", "useMediaQuery", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "DialogActions", "MuiIconButton", "TextField", "Language", "LanguageIcon", "School", "SchoolIcon", "AttachMoney", "MoneyIcon", "AccessTime", "AccessTimeIcon", "PlayCircleOutline", "PlayCircleOutlineIcon", "ArrowBack", "ArrowBackIcon", "Message", "MessageIcon", "CalendarMonth", "CalendarMonthIcon", "Verified", "VerifiedIcon", "Close", "CloseIcon", "Send", "SendIcon", "axios", "useAuth", "useSocket", "format", "ar", "enUS", "Layout", "AvailableHoursTable", "ProfileCompletionAlert", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TeacherP<PERSON><PERSON>le", "_studentProfile2", "id", "t", "i18n", "theme", "navigate", "currentUser", "token", "isMobile", "breakpoints", "down", "isRtl", "language", "teacher", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "availableSlots", "setAvailableSlots", "loadingSlots", "setLoadingSlots", "fetchTeacherDetails", "data", "get", "headers", "success", "console", "fetchAvailableSlots", "_studentProfile", "url", "studentProfile", "timezone", "encodeURIComponent", "message", "showChat", "setShowChat", "chatId", "setChatId", "chatLoading", "setChatLoading", "chatMessages", "setChatMessages", "messageInput", "setMessageInput", "socket", "handleStartChat", "state", "from", "handleCloseChat", "handleSendClick", "trim", "handleSendMessage", "content", "tempId", "Date", "now", "timestamp", "Math", "floor", "tempMessage", "conversation_id", "sender_id", "created_at", "is_read", "is_temp", "prev", "emit", "on", "replace", "getTime", "_ref", "map", "msg", "filter", "off", "response", "convertedMessages", "messages", "handleBookLesson", "handleBookSlot", "slot", "date", "post", "teacher_id", "slot_id", "alert", "_error$response", "_error$response$data", "_studentProfile3", "isLocalVideoFile", "startsWith", "endsWith", "isYoutubeVideo", "includes", "getYoutubeVideoId", "regExp", "match", "length", "test", "log", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "display", "justifyContent", "alignItems", "minHeight", "severity", "mt", "variant", "startIcon", "component", "to", "exemptPages", "mb", "elevation", "p", "borderRadius", "container", "spacing", "item", "xs", "md", "flexDirection", "src", "profile_picture_url", "alt", "full_name", "width", "height", "border", "borderColor", "fontWeight", "mr", "is_verified", "title", "color", "value", "average_rating", "precision", "readOnly", "ml", "review_count", "gap", "onClick", "fullWidth", "gutterBottom", "borderBottom", "pb", "flexWrap", "subjects", "subject", "index", "label", "native_language", "teaching_languages", "size", "years", "teaching_experience", "qualifications", "fontSize", "price_per_lesson", "trial_lesson_price", "intro_video_url", "position", "paddingTop", "frameBorder", "allow", "allowFullScreen", "style", "top", "left", "controls", "preload", "transform", "textAlign", "href", "target", "rel", "available_hours", "parsedHours", "JSON", "parse", "availableHours", "showStats", "reviews", "review", "student_profile_picture", "student_name", "char<PERSON>t", "primary", "locale", "secondary", "rating", "comment", "reply_text", "bgcolor", "reply_created_at", "open", "onClose", "PaperProps", "maxHeight", "flexGrow", "overflow", "toLocaleTimeString", "backgroundColor", "placeholder", "onChange", "e", "onKeyPress", "key", "shift<PERSON>ey", "preventDefault", "disabled"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/student/TeacherProfile.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Container,\n  Grid,\n  Paper,\n  Typography,\n  Box,\n  Avatar,\n  Divider,\n  Button,\n  Chip,\n  Rating,\n  Card,\n  CardContent,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  CircularProgress,\n  Alert,\n  useTheme,\n  useMediaQuery,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogContent,\n  DialogTitle,\n  DialogActions,\n  IconButton as MuiIconButton,\n  TextField\n} from '@mui/material';\nimport {\n  Language as LanguageIcon,\n  School as SchoolIcon,\n  AttachMoney as MoneyIcon,\n  AccessTime as AccessTimeIcon,\n\n  PlayCircleOutline as PlayCircleOutlineIcon,\n  ArrowBack as ArrowBackIcon,\n  Message as MessageIcon,\n  CalendarMonth as CalendarMonthIcon,\n  Verified as VerifiedIcon,\n  Close as CloseIcon,\n  Send as SendIcon\n} from '@mui/icons-material';\nimport axios from '../../utils/axios';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useSocket } from '../../contexts/SocketContext';\nimport { format } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport Layout from '../../components/Layout';\nimport AvailableHoursTable from '../../components/AvailableHoursTable';\nimport ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';\n\nconst TeacherProfile = () => {\n  const { id } = useParams();\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { currentUser, token } = useAuth();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const isRtl = i18n.language === 'ar';\n\n  const [teacher, setTeacher] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n\n  const [availableSlots, setAvailableSlots] = useState([]);\n  const [loadingSlots, setLoadingSlots] = useState(false);\n\n  useEffect(() => {\n    const fetchTeacherDetails = async () => {\n      try {\n        setLoading(true);\n        const { data } = await axios.get(`/teachers/${id}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (data.success) {\n          setTeacher(data.data);\n        }\n      } catch (error) {\n        console.error('Error fetching teacher details:', error);\n        setError(t('teacherDetails.errorFetching'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (token) {\n      fetchTeacherDetails();\n    }\n  }, [id, t, token]);\n\n  // Fetch available slots\n  useEffect(() => {\n    const fetchAvailableSlots = async () => {\n      if (!id || !token) return;\n\n      try {\n        setLoadingSlots(true);\n\n        // Build URL with student timezone if available\n        let url = `/teachers/${id}/available-slots`;\n        if (studentProfile?.timezone) {\n          url += `?student_timezone=${encodeURIComponent(studentProfile.timezone)}`;\n        }\n\n        const { data } = await axios.get(url, {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success) {\n          setAvailableSlots(data.data);\n        } else {\n          console.error('Error in API response:', data.message);\n          setAvailableSlots([]);\n        }\n      } catch (error) {\n        console.error('Error fetching available slots:', error);\n        setAvailableSlots([]);\n      } finally {\n        setLoadingSlots(false);\n      }\n    };\n\n    fetchAvailableSlots();\n  }, [id, token, t, studentProfile?.timezone]);\n\n  const [showChat, setShowChat] = useState(false);\n  const [chatId, setChatId] = useState(null);\n  const [chatLoading, setChatLoading] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [messageInput, setMessageInput] = useState('');\n  const { socket } = useSocket();\n\n  const handleStartChat = async () => {\n    if (!currentUser || !token) {\n      navigate('/login', { state: { from: `/student/teacher/${id}` } });\n      return;\n    }\n\n    try {\n      setChatLoading(true);\n      const { data } = await axios.get(`/chat/conversation/${id}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        setChatId(data.data.id);\n        setShowChat(true);\n      } else {\n        console.error('Failed to start chat:', data.message);\n      }\n    } catch (error) {\n      console.error('Error starting chat:', error);\n    } finally {\n      setChatLoading(false);\n    }\n  };\n\n  const handleCloseChat = () => {\n    setShowChat(false);\n    setChatMessages([]);\n    setMessageInput('');\n  };\n\n  const handleSendClick = () => {\n    if (messageInput.trim()) {\n      handleSendMessage(messageInput);\n      setMessageInput('');\n    }\n  };\n\n  const handleSendMessage = useCallback((content) => {\n    if (!socket || !chatId || !content.trim() || !currentUser) return;\n\n    // Create a temporary message with a unique ID\n    const tempId = `temp-${Date.now()}`;\n\n    // Get current timestamp in seconds\n    const timestamp = Math.floor(Date.now() / 1000);\n\n    const tempMessage = {\n      id: tempId,\n      conversation_id: chatId,\n      sender_id: currentUser.id,\n      content,\n      created_at: timestamp,\n      is_read: false,\n      is_temp: true\n    };\n\n    // Add to local messages immediately\n    setChatMessages(prev => [...prev, tempMessage]);\n\n    // Send to server\n    socket.emit('send_message', {\n      chatId: chatId,\n      content,\n      tempId,\n      timestamp\n    });\n  }, [socket, chatId, id, currentUser]);\n\n  // Listen for new messages\n  useEffect(() => {\n    if (!socket || !chatId) return;\n\n    // Listen for new messages\n    socket.on('new_message', (message) => {\n      // Convert MySQL timestamp to Unix timestamp\n      if (message.created_at && typeof message.created_at === 'string') {\n        message.created_at = Math.floor(new Date(message.created_at.replace(' ', 'T')).getTime() / 1000);\n      }\n\n      // Add message to messages list if it's for the current chat\n      if (message.conversation_id === chatId) {\n        setChatMessages(prev => [...prev, message]);\n        // Mark message as read\n        socket.emit('mark_messages_read', { chatId: chatId });\n      }\n    });\n\n    // Listen for message sent confirmation\n    socket.on('message_sent', ({ success, message, tempId }) => {\n      if (success) {\n        // Convert MySQL timestamp to Unix timestamp\n        if (message.created_at && typeof message.created_at === 'string') {\n          message.created_at = Math.floor(new Date(message.created_at.replace(' ', 'T')).getTime() / 1000);\n        }\n\n        setChatMessages(prev => prev.map(msg =>\n          msg.id === tempId ? { ...message } : msg\n        ));\n      } else {\n        // Remove temporary message if sending failed\n        setChatMessages(prev => prev.filter(msg => msg.id !== tempId));\n      }\n    });\n\n    return () => {\n      socket.off('new_message');\n      socket.off('message_sent');\n    };\n  }, [socket, chatId]);\n\n  // Fetch chat messages when chat is opened\n  useEffect(() => {\n    if (!socket || !chatId || !showChat) return;\n\n    setChatLoading(true);\n    socket.emit('get_chat_messages', { chatId: chatId }, (response) => {\n      if (response.success) {\n        // Convert all MySQL timestamps to Unix timestamps\n        const convertedMessages = response.messages.map(msg => {\n          if (msg.created_at && typeof msg.created_at === 'string') {\n            return {\n              ...msg,\n              created_at: Math.floor(new Date(msg.created_at.replace(' ', 'T')).getTime() / 1000)\n            };\n          }\n          return msg;\n        });\n        setChatMessages(convertedMessages);\n\n        // Mark messages as read\n        socket.emit('mark_messages_read', { chatId: chatId });\n      } else {\n        console.error('Error fetching messages:', response.error);\n      }\n      setChatLoading(false);\n    });\n  }, [socket, chatId, showChat]);\n\n  const handleBookLesson = () => {\n    if (!currentUser || !token) {\n      navigate('/login', { state: { from: `/student/teacher/${id}` } });\n      return;\n    }\n\n    // Navigate to booking page\n    navigate(`/student/book/${id}`);\n  };\n\n  const handleBookSlot = async (slot, date) => {\n    if (!currentUser || !token) {\n      navigate('/login', { state: { from: `/student/teacher/${id}` } });\n      return;\n    }\n\n    try {\n      const { data } = await axios.post('/bookings', {\n        teacher_id: id,\n        slot_id: slot.id,\n        date: date\n      }, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Show success message\n        alert(t('booking.bookingSuccess'));\n\n        // Refresh available slots\n        fetchAvailableSlots();\n      } else {\n        alert(data.message || t('booking.bookingFailed'));\n      }\n    } catch (error) {\n      console.error('Error booking slot:', error);\n      alert(error.response?.data?.message || t('booking.bookingFailed'));\n    }\n  };\n\n  const fetchAvailableSlots = async () => {\n    if (!id || !token) return;\n\n    try {\n      setLoadingSlots(true);\n\n      // Build URL with student timezone if available\n      let url = `/teachers/${id}/available-slots`;\n      if (studentProfile?.timezone) {\n        url += `?student_timezone=${encodeURIComponent(studentProfile.timezone)}`;\n      }\n\n      const { data } = await axios.get(url, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        setAvailableSlots(data.data);\n      } else {\n        console.error('Error in API response:', data.message);\n        setAvailableSlots([]);\n      }\n    } catch (error) {\n      console.error('Error fetching available slots:', error);\n      setAvailableSlots([]);\n    } finally {\n      setLoadingSlots(false);\n    }\n  };\n\n\n\n\n\n  // Function to check if a URL is a local video file\n  const isLocalVideoFile = (url) => {\n    if (!url) return false;\n    return url.startsWith('/uploads/') && (\n      url.endsWith('.mp4') ||\n      url.endsWith('.webm') ||\n      url.endsWith('.ogg')\n    );\n  };\n\n  // Function to check if a URL is a YouTube video\n  const isYoutubeVideo = (url) => {\n    if (!url) return false;\n    return url.includes('youtube.com') || url.includes('youtu.be');\n  };\n\n  // Function to extract YouTube video ID from URL\n  const getYoutubeVideoId = (url) => {\n    if (!url) return null;\n\n    try {\n      // Regular expressions to match different YouTube URL formats\n      const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;\n      const match = url.match(regExp);\n\n      if (match && match[2] && match[2].length === 11) {\n        return match[2];\n      }\n\n      // If the URL is already a video ID (11 characters)\n      if (url.length === 11 && /^[a-zA-Z0-9_-]{11}$/.test(url)) {\n        return url;\n      }\n\n      console.log('Could not extract YouTube video ID from:', url);\n      return null;\n    } catch (error) {\n      console.error('Error extracting YouTube video ID:', error);\n      return null;\n    }\n  };\n\n  const content = (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      {loading ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>\n          <CircularProgress />\n        </Box>\n      ) : error || !teacher ? (\n        <Box>\n          <Alert severity=\"error\">{error || t('teacherDetails.teacherNotFound')}</Alert>\n          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>\n            <Button\n              variant=\"contained\"\n              startIcon={<ArrowBackIcon />}\n              component={Link}\n              to=\"/student/find-teacher\"\n            >\n              {t('teacherDetails.backToSearch')}\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <>\n          <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>\n            {/* Back Button */}\n            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-start' }}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<ArrowBackIcon />}\n                component={Link}\n                to=\"/student/find-teacher\"\n              >\n                {t('teacherDetails.backToSearch')}\n              </Button>\n            </Box>\n\n          {/* Teacher Header */}\n          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={3} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n                <Avatar\n                  src={teacher.profile_picture_url ? (\n                    teacher.profile_picture_url.startsWith('http')\n                      ? teacher.profile_picture_url\n                      : `https://allemnionline.com${teacher.profile_picture_url}`\n                  ) : ''}\n                  alt={teacher.full_name}\n                  sx={{\n                    width: { xs: 120, md: 150 },\n                    height: { xs: 120, md: 150 },\n                    mb: 2,\n                    border: '4px solid',\n                    borderColor: 'primary.main'\n                  }}\n                />\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <Typography variant=\"h5\" component=\"h1\" sx={{ fontWeight: 'bold', mr: 1 }}>\n                    {teacher.full_name}\n                  </Typography>\n                  {teacher.is_verified && (\n                    <Tooltip title={t('search.verifiedTeacher')}>\n                      <VerifiedIcon color=\"primary\" />\n                    </Tooltip>\n                  )}\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <Rating value={teacher.average_rating} precision={0.5} readOnly />\n                  <Typography variant=\"body2\" sx={{ ml: 1 }}>\n                    ({teacher.review_count} {t('teacherDetails.reviews')})\n                  </Typography>\n                </Box>\n                <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1 }}>\n\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    startIcon={<MessageIcon />}\n                    onClick={handleStartChat}\n                    fullWidth\n                  >\n                    {t('chat.startChat')}\n                  </Button>\n                  <Button\n                    variant=\"contained\"\n                    color=\"secondary\"\n                    startIcon={<CalendarMonthIcon />}\n                    onClick={handleBookLesson}\n                    fullWidth\n                  >\n                    {t('teacherDetails.bookLesson')}\n                  </Button>\n                </Box>\n              </Grid>\n\n              <Grid item xs={12} md={9}>\n                <Grid container spacing={3}>\n                  {/* Subjects */}\n                  <Grid item xs={12}>\n                    <Box>\n                      <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>\n                        <SchoolIcon color=\"primary\" />\n                        {t('teacherDetails.teachingSubjects')}\n                      </Typography>\n                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 4 }}>\n                        {teacher.subjects.map((subject, index) => (\n                          <Chip\n                            key={index}\n                            label={subject}\n                            color=\"primary\"\n                            variant=\"outlined\"\n                          />\n                        ))}\n                      </Box>\n                    </Box>\n                  </Grid>\n\n\n\n                  {/* Languages */}\n                  <Grid item xs={12} md={6}>\n                    <Box>\n                      <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>\n                        <LanguageIcon color=\"primary\" />\n                        {t('teacherDetails.languages')}\n                      </Typography>\n                      <Box sx={{ ml: 4 }}>\n                        <Typography variant=\"body1\" sx={{ mb: 1 }}>\n                          <strong>{t('teacherDetails.nativeLanguage')}:</strong> {teacher.native_language}\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ mb: 1 }}>\n                          <strong>{t('teacherDetails.teachingLanguages')}:</strong>\n                        </Typography>\n                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 2 }}>\n                          {teacher.teaching_languages.map((language, index) => (\n                            <Chip\n                              key={index}\n                              label={language}\n                              size=\"small\"\n                              variant=\"outlined\"\n                            />\n                          ))}\n                        </Box>\n                      </Box>\n                    </Box>\n                  </Grid>\n\n                  {/* Qualifications and Experience */}\n                  <Grid item xs={12} md={6}>\n                    <Box>\n                      <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>\n                        <AccessTimeIcon color=\"primary\" />\n                        {t('teacherDetails.experience')}\n                      </Typography>\n                      <Box sx={{ ml: 4 }}>\n                        <Typography variant=\"body1\" sx={{ mb: 1 }}>\n                          {t('teacherDetails.yearsOfExperience', { years: teacher.teaching_experience })}\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ mb: 1 }}>\n                          <strong>{t('teacherDetails.qualifications')}:</strong>\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ ml: 2 }}>\n                          {teacher.qualifications}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n\n                  {/* Price */}\n                  <Grid item xs={12} md={6}>\n                    <Box>\n                      <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>\n                        <MoneyIcon color=\"primary\" />\n                        {t('teacherDetails.paymentInfo')}\n                      </Typography>\n                      <Box sx={{ ml: 4 }}>\n                        <Typography variant=\"body1\" sx={{ mb: 0.5, fontWeight: 'bold', fontSize: '1.2rem', color: 'primary.main' }}>\n                          ${teacher.price_per_lesson}/hr\n                        </Typography>\n                        {teacher.trial_lesson_price && (\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ ml: 0.2 }}>\n                            Trial: ${teacher.trial_lesson_price}\n                          </Typography>\n                        )}\n                      </Box>\n                    </Box>\n                  </Grid>\n                </Grid>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          {/* Introduction Video Section */}\n          {teacher.intro_video_url && (\n            <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>\n              <Typography variant=\"h5\" gutterBottom sx={{ borderBottom: 1, borderColor: 'divider', pb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>\n                <PlayCircleOutlineIcon color=\"primary\" />\n                {t('teacherDetails.introVideo')}\n              </Typography>\n              <Box sx={{ mt: 2, position: 'relative', paddingTop: '56.25%', width: '100%' }}>\n                {isYoutubeVideo(teacher.intro_video_url) ? (\n                  // YouTube video embed\n                  <iframe\n                    src={`https://www.youtube.com/embed/${getYoutubeVideoId(teacher.intro_video_url)}`}\n                    title=\"YouTube video player\"\n                    frameBorder=\"0\"\n                    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                    allowFullScreen\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      width: '100%',\n                      height: '100%',\n                      borderRadius: '8px'\n                    }}\n                  />\n                ) : isLocalVideoFile(teacher.intro_video_url) ? (\n                  // Local video file\n                  <video\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      width: '100%',\n                      height: '100%',\n                      borderRadius: '8px'\n                    }}\n                    src={`https://allemnionline.com${teacher.intro_video_url}`}\n                    controls\n                    preload=\"metadata\"\n                  />\n                ) : (\n                  // External video URL (not YouTube)\n                  <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', textAlign: 'center' }}>\n                    <Typography variant=\"body1\">\n                      <a href={teacher.intro_video_url} target=\"_blank\" rel=\"noopener noreferrer\">\n                        {t('teacherDetails.openVideo')}\n                      </a>\n                    </Typography>\n                  </Box>\n                )}\n              </Box>\n            </Paper>\n          )}\n\n          {/* Available Hours Section */}\n          {teacher.available_hours && (() => {\n            try {\n              const parsedHours = typeof teacher.available_hours === 'string'\n                ? JSON.parse(teacher.available_hours)\n                : teacher.available_hours;\n              return (\n                <AvailableHoursTable\n                  availableHours={parsedHours}\n                  loading={loading}\n                  showStats={true}\n                />\n              );\n            } catch (error) {\n              console.error('Error parsing available hours:', error);\n              return null;\n            }\n          })()}\n\n\n\n          {/* Reviews Section */}\n          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>\n            <Typography variant=\"h5\" gutterBottom sx={{ borderBottom: 1, borderColor: 'divider', pb: 1 }}>\n              {t('teacherDetails.studentReviews')}\n            </Typography>\n            {teacher.reviews && teacher.reviews.length > 0 ? (\n              <List>\n                {teacher.reviews.map((review) => (\n                  <ListItem key={review.id} alignItems=\"flex-start\" sx={{ borderBottom: '1px solid', borderColor: 'divider', py: 2 }}>\n                    <ListItemAvatar>\n                      <Avatar src={review.student_profile_picture} alt={review.student_name}>\n                        {review.student_name.charAt(0)}\n                      </Avatar>\n                    </ListItemAvatar>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\" component=\"span\">\n                            {review.student_name}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            {format(new Date(review.created_at), 'PPP', { locale: isRtl ? ar : enUS })}\n                          </Typography>\n                        </Box>\n                      }\n                      secondary={\n                        <Box sx={{ mt: 1 }}>\n                          <Rating value={review.rating} readOnly size=\"small\" sx={{ mb: 1 }} />\n                          <Typography variant=\"body2\" component=\"p\" sx={{ mb: 2 }}>\n                            {review.comment}\n                          </Typography>\n\n                          {/* Teacher Reply */}\n                          {review.reply_text && (\n                            <Box sx={{\n                              bgcolor: 'primary.light',\n                              p: 2,\n                              borderRadius: 1,\n                              mt: 2,\n                              border: '1px solid',\n                              borderColor: 'primary.main'\n                            }}>\n                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                                <Typography variant=\"subtitle2\" color=\"primary\" sx={{ fontWeight: 'bold' }}>\n                                  {t('reviews.teacherReply')}:\n                                </Typography>\n                              </Box>\n                              <Typography variant=\"body2\" sx={{ color: 'primary.dark' }}>\n                                {review.reply_text}\n                              </Typography>\n                              {review.reply_created_at && (\n                                <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n                                  {format(new Date(review.reply_created_at), 'PPP', { locale: isRtl ? ar : enUS })}\n                                </Typography>\n                              )}\n                            </Box>\n                          )}\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            ) : (\n              <Box sx={{ py: 4, textAlign: 'center' }}>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  {t('teacherDetails.noReviews')}\n                </Typography>\n              </Box>\n            )}\n          </Paper>\n\n\n\n          {/* Chat Dialog */}\n          <Dialog\n            open={showChat}\n            onClose={handleCloseChat}\n            maxWidth=\"md\"\n            fullWidth\n            PaperProps={{\n              sx: {\n                height: '80vh',\n                maxHeight: '700px',\n                display: 'flex',\n                flexDirection: 'column'\n              }\n            }}\n          >\n            <DialogTitle>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                  <Avatar\n                    src={teacher.profile_picture_url ? (\n                      teacher.profile_picture_url.startsWith('http')\n                        ? teacher.profile_picture_url\n                        : `https://allemnionline.com${teacher.profile_picture_url}`\n                    ) : ''}\n                    alt={teacher.full_name}\n                    sx={{ mr: 1 }}\n                  >\n                    {teacher.full_name.charAt(0)}\n                  </Avatar>\n                  <Typography variant=\"h6\">{teacher.full_name}</Typography>\n                </Box>\n                <IconButton onClick={handleCloseChat} size=\"small\">\n                  <CloseIcon />\n                </IconButton>\n              </Box>\n            </DialogTitle>\n            <DialogContent sx={{ p: 0, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>\n              {chatLoading ? (\n                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>\n                  <CircularProgress />\n                </Box>\n              ) : (\n                <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n                  <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>\n                    {chatMessages.length === 0 ? (\n                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>\n                        <Typography color=\"text.secondary\">{t('chat.startConversation')}</Typography>\n                      </Box>\n                    ) : (\n                      chatMessages.map((message) => (\n                        <Box\n                          key={message.id}\n                          sx={{\n                            display: 'flex',\n                            justifyContent: message.sender_id === currentUser?.id ? 'flex-end' : 'flex-start',\n                            mb: 2\n                          }}\n                        >\n                          {message.sender_id !== currentUser?.id && (\n                            <Avatar\n                              src={teacher.profile_picture_url ? (\n                                teacher.profile_picture_url.startsWith('http')\n                                  ? teacher.profile_picture_url\n                                  : `https://allemnionline.com${teacher.profile_picture_url}`\n                              ) : ''}\n                              alt={teacher.full_name}\n                              sx={{ mr: 1, width: 32, height: 32 }}\n                            >\n                              {teacher.full_name.charAt(0)}\n                            </Avatar>\n                          )}\n                          <Box\n                            sx={{\n                              maxWidth: '70%',\n                              p: 2,\n                              bgcolor: message.sender_id === currentUser?.id ? 'primary.main' : 'grey.100',\n                              color: message.sender_id === currentUser?.id ? 'white' : 'text.primary',\n                              borderRadius: 2,\n                              position: 'relative'\n                            }}\n                          >\n                            <Typography variant=\"body1\">{message.content}</Typography>\n                            <Typography\n                              variant=\"caption\"\n                              sx={{\n                                display: 'block',\n                                textAlign: message.sender_id === currentUser?.id ? 'right' : 'left',\n                                mt: 0.5,\n                                color: message.sender_id === currentUser?.id ? 'rgba(255,255,255,0.7)' : 'text.secondary'\n                              }}\n                            >\n                              {new Date(message.created_at * 1000).toLocaleTimeString()}\n                            </Typography>\n                          </Box>\n                        </Box>\n                      ))\n                    )}\n                  </Box>\n                  <Divider />\n                  <Box sx={{ p: 2, backgroundColor: 'background.default' }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <TextField\n                        fullWidth\n                        placeholder={t('chat.typeMessage')}\n                        variant=\"outlined\"\n                        size=\"small\"\n                        value={messageInput}\n                        onChange={(e) => setMessageInput(e.target.value)}\n                        onKeyPress={(e) => {\n                          if (e.key === 'Enter' && !e.shiftKey) {\n                            e.preventDefault();\n                            handleSendClick();\n                          }\n                        }}\n                        sx={{ mr: 1 }}\n                      />\n                      <IconButton\n                        color=\"primary\"\n                        onClick={handleSendClick}\n                        disabled={!messageInput.trim()}\n                      >\n                        <SendIcon />\n                      </IconButton>\n                    </Box>\n                  </Box>\n                </Box>\n              )}\n            </DialogContent>\n          </Dialog>\n          </ProfileCompletionAlert>\n        </>\n      )}\n    </Container>\n  );\n\n  return <Layout>{content}</Layout>;\n};\n\nexport default TeacherProfile;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OAASC,SAAS,CAAEC,WAAW,CAAEC,IAAI,KAAQ,kBAAkB,CAC/D,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,UAAU,CACVC,GAAG,CACHC,MAAM,CACNC,OAAO,CACPC,MAAM,CACNC,IAAI,CACJC,MAAM,CACNC,IAAI,CACJC,WAAW,CACXC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,cAAc,CACdC,gBAAgB,CAChBC,KAAK,CACLC,QAAQ,CACRC,aAAa,CACbC,UAAU,CACVC,OAAO,CACPC,MAAM,CACNC,aAAa,CACbC,WAAW,CACXC,aAAa,CACbL,UAAU,GAAI,CAAAM,aAAa,CAC3BC,SAAS,KACJ,eAAe,CACtB,OACEC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,WAAW,GAAI,CAAAC,SAAS,CACxBC,UAAU,GAAI,CAAAC,cAAc,CAE5BC,iBAAiB,GAAI,CAAAC,qBAAqB,CAC1CC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,OAAO,GAAI,CAAAC,WAAW,CACtBC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,IAAI,GAAI,CAAAC,QAAQ,KACX,qBAAqB,CAC5B,MAAO,CAAAC,KAAK,KAAM,mBAAmB,CACrC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,SAAS,KAAQ,8BAA8B,CACxD,OAASC,MAAM,KAAQ,UAAU,CACjC,OAASC,EAAE,CAAEC,IAAI,KAAQ,iBAAiB,CAC1C,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,mBAAmB,KAAM,sCAAsC,CACtE,MAAO,CAAAC,sBAAsB,KAAM,iDAAiD,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErF,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,KAAAC,gBAAA,CAC3B,KAAM,CAAEC,EAAG,CAAC,CAAGvE,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAEwE,CAAC,CAAEC,IAAK,CAAC,CAAGtE,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAuE,KAAK,CAAGpD,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAqD,QAAQ,CAAG1E,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAE2E,WAAW,CAAEC,KAAM,CAAC,CAAGtB,OAAO,CAAC,CAAC,CACxC,KAAM,CAAAuB,QAAQ,CAAGvD,aAAa,CAACmD,KAAK,CAACK,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAAC,KAAK,CAAGR,IAAI,CAACS,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGvF,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwF,OAAO,CAAEC,UAAU,CAAC,CAAGzF,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC0F,KAAK,CAAEC,QAAQ,CAAC,CAAG3F,QAAQ,CAAC,IAAI,CAAC,CAGxC,KAAM,CAAC4F,cAAc,CAAEC,iBAAiB,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC8F,YAAY,CAAEC,eAAe,CAAC,CAAG/F,QAAQ,CAAC,KAAK,CAAC,CAEvDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+F,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACFP,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEQ,IAAK,CAAC,CAAG,KAAM,CAAAxC,KAAK,CAACyC,GAAG,CAAC,aAAaxB,EAAE,EAAE,CAAE,CAClDyB,OAAO,CAAE,CACP,eAAe,CAAE,UAAUnB,KAAK,EAClC,CACF,CAAC,CAAC,CACF,GAAIiB,IAAI,CAACG,OAAO,CAAE,CAChBb,UAAU,CAACU,IAAI,CAACA,IAAI,CAAC,CACvB,CACF,CAAE,MAAOP,KAAK,CAAE,CACdW,OAAO,CAACX,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDC,QAAQ,CAAChB,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAC7C,CAAC,OAAS,CACRc,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIT,KAAK,CAAE,CACTgB,mBAAmB,CAAC,CAAC,CACvB,CACF,CAAC,CAAE,CAACtB,EAAE,CAAEC,CAAC,CAAEK,KAAK,CAAC,CAAC,CAElB;AACA/E,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqG,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAAC5B,EAAE,EAAI,CAACM,KAAK,CAAE,OAEnB,GAAI,KAAAuB,eAAA,CACFR,eAAe,CAAC,IAAI,CAAC,CAErB;AACA,GAAI,CAAAS,GAAG,CAAG,aAAa9B,EAAE,kBAAkB,CAC3C,IAAA6B,eAAA,CAAIE,cAAc,UAAAF,eAAA,WAAdA,eAAA,CAAgBG,QAAQ,CAAE,CAC5BF,GAAG,EAAI,qBAAqBG,kBAAkB,CAACF,cAAc,CAACC,QAAQ,CAAC,EAAE,CAC3E,CAEA,KAAM,CAAET,IAAK,CAAC,CAAG,KAAM,CAAAxC,KAAK,CAACyC,GAAG,CAACM,GAAG,CAAE,CACpCL,OAAO,CAAE,CACP,eAAe,CAAE,UAAUnB,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIiB,IAAI,CAACG,OAAO,CAAE,CAChBP,iBAAiB,CAACI,IAAI,CAACA,IAAI,CAAC,CAC9B,CAAC,IAAM,CACLI,OAAO,CAACX,KAAK,CAAC,wBAAwB,CAAEO,IAAI,CAACW,OAAO,CAAC,CACrDf,iBAAiB,CAAC,EAAE,CAAC,CACvB,CACF,CAAE,MAAOH,KAAK,CAAE,CACdW,OAAO,CAACX,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDG,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,OAAS,CACRE,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAEDO,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAAC5B,EAAE,CAAEM,KAAK,CAAEL,CAAC,EAAAF,gBAAA,CAAEgC,cAAc,UAAAhC,gBAAA,iBAAdA,gBAAA,CAAgBiC,QAAQ,CAAC,CAAC,CAE5C,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAG9G,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC+G,MAAM,CAAEC,SAAS,CAAC,CAAGhH,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAACiH,WAAW,CAAEC,cAAc,CAAC,CAAGlH,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACmH,YAAY,CAAEC,eAAe,CAAC,CAAGpH,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACqH,YAAY,CAAEC,eAAe,CAAC,CAAGtH,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAEuH,MAAO,CAAC,CAAG5D,SAAS,CAAC,CAAC,CAE9B,KAAM,CAAA6D,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAACzC,WAAW,EAAI,CAACC,KAAK,CAAE,CAC1BF,QAAQ,CAAC,QAAQ,CAAE,CAAE2C,KAAK,CAAE,CAAEC,IAAI,CAAE,oBAAoBhD,EAAE,EAAG,CAAE,CAAC,CAAC,CACjE,OACF,CAEA,GAAI,CACFwC,cAAc,CAAC,IAAI,CAAC,CACpB,KAAM,CAAEjB,IAAK,CAAC,CAAG,KAAM,CAAAxC,KAAK,CAACyC,GAAG,CAAC,sBAAsBxB,EAAE,EAAE,CAAE,CAC3DyB,OAAO,CAAE,CACP,eAAe,CAAE,UAAUnB,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIiB,IAAI,CAACG,OAAO,CAAE,CAChBY,SAAS,CAACf,IAAI,CAACA,IAAI,CAACvB,EAAE,CAAC,CACvBoC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,IAAM,CACLT,OAAO,CAACX,KAAK,CAAC,uBAAuB,CAAEO,IAAI,CAACW,OAAO,CAAC,CACtD,CACF,CAAE,MAAOlB,KAAK,CAAE,CACdW,OAAO,CAACX,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CAAC,OAAS,CACRwB,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAS,eAAe,CAAGA,CAAA,GAAM,CAC5Bb,WAAW,CAAC,KAAK,CAAC,CAClBM,eAAe,CAAC,EAAE,CAAC,CACnBE,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAAM,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIP,YAAY,CAACQ,IAAI,CAAC,CAAC,CAAE,CACvBC,iBAAiB,CAACT,YAAY,CAAC,CAC/BC,eAAe,CAAC,EAAE,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAQ,iBAAiB,CAAG5H,WAAW,CAAE6H,OAAO,EAAK,CACjD,GAAI,CAACR,MAAM,EAAI,CAACR,MAAM,EAAI,CAACgB,OAAO,CAACF,IAAI,CAAC,CAAC,EAAI,CAAC9C,WAAW,CAAE,OAE3D;AACA,KAAM,CAAAiD,MAAM,CAAG,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAEnC;AACA,KAAM,CAAAC,SAAS,CAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAC,CAE/C,KAAM,CAAAI,WAAW,CAAG,CAClB5D,EAAE,CAAEsD,MAAM,CACVO,eAAe,CAAExB,MAAM,CACvByB,SAAS,CAAEzD,WAAW,CAACL,EAAE,CACzBqD,OAAO,CACPU,UAAU,CAAEN,SAAS,CACrBO,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,IACX,CAAC,CAED;AACAvB,eAAe,CAACwB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEN,WAAW,CAAC,CAAC,CAE/C;AACAf,MAAM,CAACsB,IAAI,CAAC,cAAc,CAAE,CAC1B9B,MAAM,CAAEA,MAAM,CACdgB,OAAO,CACPC,MAAM,CACNG,SACF,CAAC,CAAC,CACJ,CAAC,CAAE,CAACZ,MAAM,CAAER,MAAM,CAAErC,EAAE,CAAEK,WAAW,CAAC,CAAC,CAErC;AACA9E,SAAS,CAAC,IAAM,CACd,GAAI,CAACsH,MAAM,EAAI,CAACR,MAAM,CAAE,OAExB;AACAQ,MAAM,CAACuB,EAAE,CAAC,aAAa,CAAGlC,OAAO,EAAK,CACpC;AACA,GAAIA,OAAO,CAAC6B,UAAU,EAAI,MAAO,CAAA7B,OAAO,CAAC6B,UAAU,GAAK,QAAQ,CAAE,CAChE7B,OAAO,CAAC6B,UAAU,CAAGL,IAAI,CAACC,KAAK,CAAC,GAAI,CAAAJ,IAAI,CAACrB,OAAO,CAAC6B,UAAU,CAACM,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAG,IAAI,CAAC,CAClG,CAEA;AACA,GAAIpC,OAAO,CAAC2B,eAAe,GAAKxB,MAAM,CAAE,CACtCK,eAAe,CAACwB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEhC,OAAO,CAAC,CAAC,CAC3C;AACAW,MAAM,CAACsB,IAAI,CAAC,oBAAoB,CAAE,CAAE9B,MAAM,CAAEA,MAAO,CAAC,CAAC,CACvD,CACF,CAAC,CAAC,CAEF;AACAQ,MAAM,CAACuB,EAAE,CAAC,cAAc,CAAEG,IAAA,EAAkC,IAAjC,CAAE7C,OAAO,CAAEQ,OAAO,CAAEoB,MAAO,CAAC,CAAAiB,IAAA,CACrD,GAAI7C,OAAO,CAAE,CACX;AACA,GAAIQ,OAAO,CAAC6B,UAAU,EAAI,MAAO,CAAA7B,OAAO,CAAC6B,UAAU,GAAK,QAAQ,CAAE,CAChE7B,OAAO,CAAC6B,UAAU,CAAGL,IAAI,CAACC,KAAK,CAAC,GAAI,CAAAJ,IAAI,CAACrB,OAAO,CAAC6B,UAAU,CAACM,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAG,IAAI,CAAC,CAClG,CAEA5B,eAAe,CAACwB,IAAI,EAAIA,IAAI,CAACM,GAAG,CAACC,GAAG,EAClCA,GAAG,CAACzE,EAAE,GAAKsD,MAAM,CAAG,CAAE,GAAGpB,OAAQ,CAAC,CAAGuC,GACvC,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA/B,eAAe,CAACwB,IAAI,EAAIA,IAAI,CAACQ,MAAM,CAACD,GAAG,EAAIA,GAAG,CAACzE,EAAE,GAAKsD,MAAM,CAAC,CAAC,CAChE,CACF,CAAC,CAAC,CAEF,MAAO,IAAM,CACXT,MAAM,CAAC8B,GAAG,CAAC,aAAa,CAAC,CACzB9B,MAAM,CAAC8B,GAAG,CAAC,cAAc,CAAC,CAC5B,CAAC,CACH,CAAC,CAAE,CAAC9B,MAAM,CAAER,MAAM,CAAC,CAAC,CAEpB;AACA9G,SAAS,CAAC,IAAM,CACd,GAAI,CAACsH,MAAM,EAAI,CAACR,MAAM,EAAI,CAACF,QAAQ,CAAE,OAErCK,cAAc,CAAC,IAAI,CAAC,CACpBK,MAAM,CAACsB,IAAI,CAAC,mBAAmB,CAAE,CAAE9B,MAAM,CAAEA,MAAO,CAAC,CAAGuC,QAAQ,EAAK,CACjE,GAAIA,QAAQ,CAAClD,OAAO,CAAE,CACpB;AACA,KAAM,CAAAmD,iBAAiB,CAAGD,QAAQ,CAACE,QAAQ,CAACN,GAAG,CAACC,GAAG,EAAI,CACrD,GAAIA,GAAG,CAACV,UAAU,EAAI,MAAO,CAAAU,GAAG,CAACV,UAAU,GAAK,QAAQ,CAAE,CACxD,MAAO,CACL,GAAGU,GAAG,CACNV,UAAU,CAAEL,IAAI,CAACC,KAAK,CAAC,GAAI,CAAAJ,IAAI,CAACkB,GAAG,CAACV,UAAU,CAACM,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAG,IAAI,CACpF,CAAC,CACH,CACA,MAAO,CAAAG,GAAG,CACZ,CAAC,CAAC,CACF/B,eAAe,CAACmC,iBAAiB,CAAC,CAElC;AACAhC,MAAM,CAACsB,IAAI,CAAC,oBAAoB,CAAE,CAAE9B,MAAM,CAAEA,MAAO,CAAC,CAAC,CACvD,CAAC,IAAM,CACLV,OAAO,CAACX,KAAK,CAAC,0BAA0B,CAAE4D,QAAQ,CAAC5D,KAAK,CAAC,CAC3D,CACAwB,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAAC,CACJ,CAAC,CAAE,CAACK,MAAM,CAAER,MAAM,CAAEF,QAAQ,CAAC,CAAC,CAE9B,KAAM,CAAA4C,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI,CAAC1E,WAAW,EAAI,CAACC,KAAK,CAAE,CAC1BF,QAAQ,CAAC,QAAQ,CAAE,CAAE2C,KAAK,CAAE,CAAEC,IAAI,CAAE,oBAAoBhD,EAAE,EAAG,CAAE,CAAC,CAAC,CACjE,OACF,CAEA;AACAI,QAAQ,CAAC,iBAAiBJ,EAAE,EAAE,CAAC,CACjC,CAAC,CAED,KAAM,CAAAgF,cAAc,CAAG,KAAAA,CAAOC,IAAI,CAAEC,IAAI,GAAK,CAC3C,GAAI,CAAC7E,WAAW,EAAI,CAACC,KAAK,CAAE,CAC1BF,QAAQ,CAAC,QAAQ,CAAE,CAAE2C,KAAK,CAAE,CAAEC,IAAI,CAAE,oBAAoBhD,EAAE,EAAG,CAAE,CAAC,CAAC,CACjE,OACF,CAEA,GAAI,CACF,KAAM,CAAEuB,IAAK,CAAC,CAAG,KAAM,CAAAxC,KAAK,CAACoG,IAAI,CAAC,WAAW,CAAE,CAC7CC,UAAU,CAAEpF,EAAE,CACdqF,OAAO,CAAEJ,IAAI,CAACjF,EAAE,CAChBkF,IAAI,CAAEA,IACR,CAAC,CAAE,CACDzD,OAAO,CAAE,CACP,eAAe,CAAE,UAAUnB,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIiB,IAAI,CAACG,OAAO,CAAE,CAChB;AACA4D,KAAK,CAACrF,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAElC;AACA2B,mBAAmB,CAAC,CAAC,CACvB,CAAC,IAAM,CACL0D,KAAK,CAAC/D,IAAI,CAACW,OAAO,EAAIjC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CACnD,CACF,CAAE,MAAOe,KAAK,CAAE,KAAAuE,eAAA,CAAAC,oBAAA,CACd7D,OAAO,CAACX,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CsE,KAAK,CAAC,EAAAC,eAAA,CAAAvE,KAAK,CAAC4D,QAAQ,UAAAW,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBhE,IAAI,UAAAiE,oBAAA,iBAApBA,oBAAA,CAAsBtD,OAAO,GAAIjC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CACpE,CACF,CAAC,CAED,KAAM,CAAA2B,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAAC5B,EAAE,EAAI,CAACM,KAAK,CAAE,OAEnB,GAAI,KAAAmF,gBAAA,CACFpE,eAAe,CAAC,IAAI,CAAC,CAErB;AACA,GAAI,CAAAS,GAAG,CAAG,aAAa9B,EAAE,kBAAkB,CAC3C,IAAAyF,gBAAA,CAAI1D,cAAc,UAAA0D,gBAAA,WAAdA,gBAAA,CAAgBzD,QAAQ,CAAE,CAC5BF,GAAG,EAAI,qBAAqBG,kBAAkB,CAACF,cAAc,CAACC,QAAQ,CAAC,EAAE,CAC3E,CAEA,KAAM,CAAET,IAAK,CAAC,CAAG,KAAM,CAAAxC,KAAK,CAACyC,GAAG,CAACM,GAAG,CAAE,CACpCL,OAAO,CAAE,CACP,eAAe,CAAE,UAAUnB,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIiB,IAAI,CAACG,OAAO,CAAE,CAChBP,iBAAiB,CAACI,IAAI,CAACA,IAAI,CAAC,CAC9B,CAAC,IAAM,CACLI,OAAO,CAACX,KAAK,CAAC,wBAAwB,CAAEO,IAAI,CAACW,OAAO,CAAC,CACrDf,iBAAiB,CAAC,EAAE,CAAC,CACvB,CACF,CAAE,MAAOH,KAAK,CAAE,CACdW,OAAO,CAACX,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDG,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,OAAS,CACRE,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAMD;AACA,KAAM,CAAAqE,gBAAgB,CAAI5D,GAAG,EAAK,CAChC,GAAI,CAACA,GAAG,CAAE,MAAO,MAAK,CACtB,MAAO,CAAAA,GAAG,CAAC6D,UAAU,CAAC,WAAW,CAAC,GAChC7D,GAAG,CAAC8D,QAAQ,CAAC,MAAM,CAAC,EACpB9D,GAAG,CAAC8D,QAAQ,CAAC,OAAO,CAAC,EACrB9D,GAAG,CAAC8D,QAAQ,CAAC,MAAM,CAAC,CACrB,CACH,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAI/D,GAAG,EAAK,CAC9B,GAAI,CAACA,GAAG,CAAE,MAAO,MAAK,CACtB,MAAO,CAAAA,GAAG,CAACgE,QAAQ,CAAC,aAAa,CAAC,EAAIhE,GAAG,CAACgE,QAAQ,CAAC,UAAU,CAAC,CAChE,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIjE,GAAG,EAAK,CACjC,GAAI,CAACA,GAAG,CAAE,MAAO,KAAI,CAErB,GAAI,CACF;AACA,KAAM,CAAAkE,MAAM,CAAG,iEAAiE,CAChF,KAAM,CAAAC,KAAK,CAAGnE,GAAG,CAACmE,KAAK,CAACD,MAAM,CAAC,CAE/B,GAAIC,KAAK,EAAIA,KAAK,CAAC,CAAC,CAAC,EAAIA,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAK,EAAE,CAAE,CAC/C,MAAO,CAAAD,KAAK,CAAC,CAAC,CAAC,CACjB,CAEA;AACA,GAAInE,GAAG,CAACoE,MAAM,GAAK,EAAE,EAAI,qBAAqB,CAACC,IAAI,CAACrE,GAAG,CAAC,CAAE,CACxD,MAAO,CAAAA,GAAG,CACZ,CAEAH,OAAO,CAACyE,GAAG,CAAC,0CAA0C,CAAEtE,GAAG,CAAC,CAC5D,MAAO,KAAI,CACb,CAAE,MAAOd,KAAK,CAAE,CACdW,OAAO,CAACX,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,MAAO,KAAI,CACb,CACF,CAAC,CAED,KAAM,CAAAqC,OAAO,cACX5D,IAAA,CAAC5D,SAAS,EAACwK,QAAQ,CAAC,IAAI,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,CACpC1F,OAAO,cACNrB,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAJ,QAAA,cAC9F/G,IAAA,CAAC5C,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACJmE,KAAK,EAAI,CAACJ,OAAO,cACnBjB,KAAA,CAAC1D,GAAG,EAAAuK,QAAA,eACF/G,IAAA,CAAC3C,KAAK,EAAC+J,QAAQ,CAAC,OAAO,CAAAL,QAAA,CAAExF,KAAK,EAAIf,CAAC,CAAC,gCAAgC,CAAC,CAAQ,CAAC,cAC9ER,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAC,CAAEL,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAF,QAAA,cAC5D/G,IAAA,CAACrD,MAAM,EACL2K,OAAO,CAAC,WAAW,CACnBC,SAAS,cAAEvH,IAAA,CAACrB,aAAa,GAAE,CAAE,CAC7B6I,SAAS,CAAEtL,IAAK,CAChBuL,EAAE,CAAC,uBAAuB,CAAAV,QAAA,CAEzBvG,CAAC,CAAC,6BAA6B,CAAC,CAC3B,CAAC,CACN,CAAC,EACH,CAAC,cAENR,IAAA,CAAAI,SAAA,EAAA2G,QAAA,cACE7G,KAAA,CAACJ,sBAAsB,EAAC4H,WAAW,CAAE,CAAC,2BAA2B,CAAE,oBAAoB,CAAE,CAAAX,QAAA,eAEvF/G,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAC,CAAEX,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,YAAa,CAAE,CAAAF,QAAA,cAChE/G,IAAA,CAACrD,MAAM,EACL2K,OAAO,CAAC,UAAU,CAClBC,SAAS,cAAEvH,IAAA,CAACrB,aAAa,GAAE,CAAE,CAC7B6I,SAAS,CAAEtL,IAAK,CAChBuL,EAAE,CAAC,uBAAuB,CAAAV,QAAA,CAEzBvG,CAAC,CAAC,6BAA6B,CAAC,CAC3B,CAAC,CACN,CAAC,cAGRR,IAAA,CAAC1D,KAAK,EAACsL,SAAS,CAAE,CAAE,CAACf,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAC,CAAEG,YAAY,CAAE,CAAE,CAAE,CAAAf,QAAA,cACxD7G,KAAA,CAAC7D,IAAI,EAAC0L,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAjB,QAAA,eACzB7G,KAAA,CAAC7D,IAAI,EAAC4L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACtB,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEoB,aAAa,CAAE,QAAQ,CAAElB,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eAC/F/G,IAAA,CAACvD,MAAM,EACL4L,GAAG,CAAElH,OAAO,CAACmH,mBAAmB,CAC9BnH,OAAO,CAACmH,mBAAmB,CAACpC,UAAU,CAAC,MAAM,CAAC,CAC1C/E,OAAO,CAACmH,mBAAmB,CAC3B,4BAA4BnH,OAAO,CAACmH,mBAAmB,EAAE,CAC3D,EAAG,CACPC,GAAG,CAAEpH,OAAO,CAACqH,SAAU,CACvB3B,EAAE,CAAE,CACF4B,KAAK,CAAE,CAAEP,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC3BO,MAAM,CAAE,CAAER,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC5BR,EAAE,CAAE,CAAC,CACLgB,MAAM,CAAE,WAAW,CACnBC,WAAW,CAAE,cACf,CAAE,CACH,CAAC,cACF1I,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAES,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,eACxD/G,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,IAAI,CAACE,SAAS,CAAC,IAAI,CAACX,EAAE,CAAE,CAAEgC,UAAU,CAAE,MAAM,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA/B,QAAA,CACvE5F,OAAO,CAACqH,SAAS,CACR,CAAC,CACZrH,OAAO,CAAC4H,WAAW,eAClB/I,IAAA,CAACvC,OAAO,EAACuL,KAAK,CAAExI,CAAC,CAAC,wBAAwB,CAAE,CAAAuG,QAAA,cAC1C/G,IAAA,CAACf,YAAY,EAACgK,KAAK,CAAC,SAAS,CAAE,CAAC,CACzB,CACV,EACE,CAAC,cACN/I,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAES,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,eACxD/G,IAAA,CAACnD,MAAM,EAACqM,KAAK,CAAE/H,OAAO,CAACgI,cAAe,CAACC,SAAS,CAAE,GAAI,CAACC,QAAQ,MAAE,CAAC,cAClEnJ,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEyC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,EAAC,GACxC,CAAC5F,OAAO,CAACoI,YAAY,CAAC,GAAC,CAAC/I,CAAC,CAAC,wBAAwB,CAAC,CAAC,GACvD,EAAY,CAAC,EACV,CAAC,cACNN,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAE4B,KAAK,CAAE,MAAM,CAAEzB,OAAO,CAAE,MAAM,CAAEoB,aAAa,CAAE,QAAQ,CAAEoB,GAAG,CAAE,CAAE,CAAE,CAAAzC,QAAA,eAE3E/G,IAAA,CAACrD,MAAM,EACL2K,OAAO,CAAC,WAAW,CACnB2B,KAAK,CAAC,SAAS,CACf1B,SAAS,cAAEvH,IAAA,CAACnB,WAAW,GAAE,CAAE,CAC3B4K,OAAO,CAAEpG,eAAgB,CACzBqG,SAAS,MAAA3C,QAAA,CAERvG,CAAC,CAAC,gBAAgB,CAAC,CACd,CAAC,cACTR,IAAA,CAACrD,MAAM,EACL2K,OAAO,CAAC,WAAW,CACnB2B,KAAK,CAAC,WAAW,CACjB1B,SAAS,cAAEvH,IAAA,CAACjB,iBAAiB,GAAE,CAAE,CACjC0K,OAAO,CAAEnE,gBAAiB,CAC1BoE,SAAS,MAAA3C,QAAA,CAERvG,CAAC,CAAC,2BAA2B,CAAC,CACzB,CAAC,EACN,CAAC,EACF,CAAC,cAEPR,IAAA,CAAC3D,IAAI,EAAC4L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cACvB7G,KAAA,CAAC7D,IAAI,EAAC0L,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAjB,QAAA,eAEzB/G,IAAA,CAAC3D,IAAI,EAAC4L,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAnB,QAAA,cAChB7G,KAAA,CAAC1D,GAAG,EAAAuK,QAAA,eACF7G,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,IAAI,CAACqC,YAAY,MAAC9C,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEsC,GAAG,CAAE,CAAC,CAAEI,YAAY,CAAE,CAAC,CAAEhB,WAAW,CAAE,SAAS,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAA9C,QAAA,eAC1I/G,IAAA,CAAC7B,UAAU,EAAC8K,KAAK,CAAC,SAAS,CAAE,CAAC,CAC7BzI,CAAC,CAAC,iCAAiC,CAAC,EAC3B,CAAC,cACbR,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAE8C,QAAQ,CAAE,MAAM,CAAEN,GAAG,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,CAC3D5F,OAAO,CAAC4I,QAAQ,CAAChF,GAAG,CAAC,CAACiF,OAAO,CAAEC,KAAK,gBACnCjK,IAAA,CAACpD,IAAI,EAEHsN,KAAK,CAAEF,OAAQ,CACff,KAAK,CAAC,SAAS,CACf3B,OAAO,CAAC,UAAU,EAHb2C,KAIN,CACF,CAAC,CACC,CAAC,EACH,CAAC,CACF,CAAC,cAKPjK,IAAA,CAAC3D,IAAI,EAAC4L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cACvB7G,KAAA,CAAC1D,GAAG,EAAAuK,QAAA,eACF7G,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,IAAI,CAACqC,YAAY,MAAC9C,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEsC,GAAG,CAAE,CAAC,CAAEI,YAAY,CAAE,CAAC,CAAEhB,WAAW,CAAE,SAAS,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAA9C,QAAA,eAC1I/G,IAAA,CAAC/B,YAAY,EAACgL,KAAK,CAAC,SAAS,CAAE,CAAC,CAC/BzI,CAAC,CAAC,0BAA0B,CAAC,EACpB,CAAC,cACbN,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEyC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,eACjB7G,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,eACxC7G,KAAA,WAAA6G,QAAA,EAASvG,CAAC,CAAC,+BAA+B,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACW,OAAO,CAACgJ,eAAe,EACrE,CAAC,cACbnK,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,cACxC7G,KAAA,WAAA6G,QAAA,EAASvG,CAAC,CAAC,kCAAkC,CAAC,CAAC,GAAC,EAAQ,CAAC,CAC/C,CAAC,cACbR,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAE8C,QAAQ,CAAE,MAAM,CAAEN,GAAG,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,CAC3D5F,OAAO,CAACiJ,kBAAkB,CAACrF,GAAG,CAAC,CAAC7D,QAAQ,CAAE+I,KAAK,gBAC9CjK,IAAA,CAACpD,IAAI,EAEHsN,KAAK,CAAEhJ,QAAS,CAChBmJ,IAAI,CAAC,OAAO,CACZ/C,OAAO,CAAC,UAAU,EAHb2C,KAIN,CACF,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,cAGPjK,IAAA,CAAC3D,IAAI,EAAC4L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cACvB7G,KAAA,CAAC1D,GAAG,EAAAuK,QAAA,eACF7G,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,IAAI,CAACqC,YAAY,MAAC9C,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEsC,GAAG,CAAE,CAAC,CAAEI,YAAY,CAAE,CAAC,CAAEhB,WAAW,CAAE,SAAS,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAA9C,QAAA,eAC1I/G,IAAA,CAACzB,cAAc,EAAC0K,KAAK,CAAC,SAAS,CAAE,CAAC,CACjCzI,CAAC,CAAC,2BAA2B,CAAC,EACrB,CAAC,cACbN,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEyC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,eACjB/G,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,CACvCvG,CAAC,CAAC,kCAAkC,CAAE,CAAE8J,KAAK,CAAEnJ,OAAO,CAACoJ,mBAAoB,CAAC,CAAC,CACpE,CAAC,cACbvK,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,cACxC7G,KAAA,WAAA6G,QAAA,EAASvG,CAAC,CAAC,+BAA+B,CAAC,CAAC,GAAC,EAAQ,CAAC,CAC5C,CAAC,cACbR,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEyC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,CACvC5F,OAAO,CAACqJ,cAAc,CACb,CAAC,EACV,CAAC,EACH,CAAC,CACF,CAAC,cAGPxK,IAAA,CAAC3D,IAAI,EAAC4L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cACvB7G,KAAA,CAAC1D,GAAG,EAAAuK,QAAA,eACF7G,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,IAAI,CAACqC,YAAY,MAAC9C,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEsC,GAAG,CAAE,CAAC,CAAEI,YAAY,CAAE,CAAC,CAAEhB,WAAW,CAAE,SAAS,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAA9C,QAAA,eAC1I/G,IAAA,CAAC3B,SAAS,EAAC4K,KAAK,CAAC,SAAS,CAAE,CAAC,CAC5BzI,CAAC,CAAC,4BAA4B,CAAC,EACtB,CAAC,cACbN,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEyC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,eACjB7G,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEkB,UAAU,CAAE,MAAM,CAAE4B,QAAQ,CAAE,QAAQ,CAAExB,KAAK,CAAE,cAAe,CAAE,CAAAlC,QAAA,EAAC,GACzG,CAAC5F,OAAO,CAACuJ,gBAAgB,CAAC,KAC7B,EAAY,CAAC,CACZvJ,OAAO,CAACwJ,kBAAkB,eACzBzK,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAAC2B,KAAK,CAAC,gBAAgB,CAACpC,EAAE,CAAE,CAAEyC,EAAE,CAAE,GAAI,CAAE,CAAAvC,QAAA,EAAC,UAC1D,CAAC5F,OAAO,CAACwJ,kBAAkB,EACzB,CACb,EACE,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,CAGPxJ,OAAO,CAACyJ,eAAe,eACtB1K,KAAA,CAAC5D,KAAK,EAACsL,SAAS,CAAE,CAAE,CAACf,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAC,CAAEG,YAAY,CAAE,CAAE,CAAE,CAAAf,QAAA,eACxD7G,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,IAAI,CAACqC,YAAY,MAAC9C,EAAE,CAAE,CAAE+C,YAAY,CAAE,CAAC,CAAEhB,WAAW,CAAE,SAAS,CAAEiB,EAAE,CAAE,CAAC,CAAE7C,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEsC,GAAG,CAAE,CAAE,CAAE,CAAAzC,QAAA,eAC1I/G,IAAA,CAACvB,qBAAqB,EAACwK,KAAK,CAAC,SAAS,CAAE,CAAC,CACxCzI,CAAC,CAAC,2BAA2B,CAAC,EACrB,CAAC,cACbR,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAC,CAAEwD,QAAQ,CAAE,UAAU,CAAEC,UAAU,CAAE,QAAQ,CAAErC,KAAK,CAAE,MAAO,CAAE,CAAA1B,QAAA,CAC3EX,cAAc,CAACjF,OAAO,CAACyJ,eAAe,CAAC,cACtC;AACA5K,IAAA,WACEqI,GAAG,CAAE,iCAAiC/B,iBAAiB,CAACnF,OAAO,CAACyJ,eAAe,CAAC,EAAG,CACnF5B,KAAK,CAAC,sBAAsB,CAC5B+B,WAAW,CAAC,GAAG,CACfC,KAAK,CAAC,0FAA0F,CAChGC,eAAe,MACfC,KAAK,CAAE,CACLL,QAAQ,CAAE,UAAU,CACpBM,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACP3C,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdZ,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,CACA7B,gBAAgB,CAAC9E,OAAO,CAACyJ,eAAe,CAAC,cAC3C;AACA5K,IAAA,UACEkL,KAAK,CAAE,CACLL,QAAQ,CAAE,UAAU,CACpBM,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACP3C,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdZ,YAAY,CAAE,KAChB,CAAE,CACFO,GAAG,CAAE,4BAA4BlH,OAAO,CAACyJ,eAAe,EAAG,CAC3DS,QAAQ,MACRC,OAAO,CAAC,UAAU,CACnB,CAAC,cAEF;AACAtL,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEgE,QAAQ,CAAE,UAAU,CAAEM,GAAG,CAAE,KAAK,CAAEC,IAAI,CAAE,KAAK,CAAEG,SAAS,CAAE,uBAAuB,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAzE,QAAA,cAClH/G,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAAAP,QAAA,cACzB/G,IAAA,MAAGyL,IAAI,CAAEtK,OAAO,CAACyJ,eAAgB,CAACc,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAAA5E,QAAA,CACxEvG,CAAC,CAAC,0BAA0B,CAAC,CAC7B,CAAC,CACM,CAAC,CACV,CACN,CACE,CAAC,EACD,CACR,CAGAW,OAAO,CAACyK,eAAe,EAAI,CAAC,IAAM,CACjC,GAAI,CACF,KAAM,CAAAC,WAAW,CAAG,MAAO,CAAA1K,OAAO,CAACyK,eAAe,GAAK,QAAQ,CAC3DE,IAAI,CAACC,KAAK,CAAC5K,OAAO,CAACyK,eAAe,CAAC,CACnCzK,OAAO,CAACyK,eAAe,CAC3B,mBACE5L,IAAA,CAACH,mBAAmB,EAClBmM,cAAc,CAAEH,WAAY,CAC5BxK,OAAO,CAAEA,OAAQ,CACjB4K,SAAS,CAAE,IAAK,CACjB,CAAC,CAEN,CAAE,MAAO1K,KAAK,CAAE,CACdW,OAAO,CAACX,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,MAAO,KAAI,CACb,CACF,CAAC,EAAE,CAAC,cAKJrB,KAAA,CAAC5D,KAAK,EAACsL,SAAS,CAAE,CAAE,CAACf,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAC,CAAEG,YAAY,CAAE,CAAE,CAAE,CAAAf,QAAA,eACxD/G,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,IAAI,CAACqC,YAAY,MAAC9C,EAAE,CAAE,CAAE+C,YAAY,CAAE,CAAC,CAAEhB,WAAW,CAAE,SAAS,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAA9C,QAAA,CAC1FvG,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,CACZW,OAAO,CAAC+K,OAAO,EAAI/K,OAAO,CAAC+K,OAAO,CAACzF,MAAM,CAAG,CAAC,cAC5CzG,IAAA,CAAChD,IAAI,EAAA+J,QAAA,CACF5F,OAAO,CAAC+K,OAAO,CAACnH,GAAG,CAAEoH,MAAM,eAC1BjM,KAAA,CAACjD,QAAQ,EAAiBiK,UAAU,CAAC,YAAY,CAACL,EAAE,CAAE,CAAE+C,YAAY,CAAE,WAAW,CAAEhB,WAAW,CAAE,SAAS,CAAE9B,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACjH/G,IAAA,CAAC7C,cAAc,EAAA4J,QAAA,cACb/G,IAAA,CAACvD,MAAM,EAAC4L,GAAG,CAAE8D,MAAM,CAACC,uBAAwB,CAAC7D,GAAG,CAAE4D,MAAM,CAACE,YAAa,CAAAtF,QAAA,CACnEoF,MAAM,CAACE,YAAY,CAACC,MAAM,CAAC,CAAC,CAAC,CACxB,CAAC,CACK,CAAC,cACjBtM,IAAA,CAAC9C,YAAY,EACXqP,OAAO,cACLrM,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eAClF/G,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,WAAW,CAACE,SAAS,CAAC,MAAM,CAAAT,QAAA,CAC7CoF,MAAM,CAACE,YAAY,CACV,CAAC,cACbrM,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAAC2B,KAAK,CAAC,gBAAgB,CAAAlC,QAAA,CAC/CtH,MAAM,CAAC,GAAI,CAAAqE,IAAI,CAACqI,MAAM,CAAC7H,UAAU,CAAC,CAAE,KAAK,CAAE,CAAEkI,MAAM,CAAEvL,KAAK,CAAGvB,EAAE,CAAGC,IAAK,CAAC,CAAC,CAChE,CAAC,EACV,CACN,CACD8M,SAAS,cACPvM,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,eACjB/G,IAAA,CAACnD,MAAM,EAACqM,KAAK,CAAEiD,MAAM,CAACO,MAAO,CAACrD,QAAQ,MAACgB,IAAI,CAAC,OAAO,CAACxD,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACrE3H,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACE,SAAS,CAAC,GAAG,CAACX,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,CACrDoF,MAAM,CAACQ,OAAO,CACL,CAAC,CAGZR,MAAM,CAACS,UAAU,eAChB1M,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CACPgG,OAAO,CAAE,eAAe,CACxBhF,CAAC,CAAE,CAAC,CACJC,YAAY,CAAE,CAAC,CACfT,EAAE,CAAE,CAAC,CACLsB,MAAM,CAAE,WAAW,CACnBC,WAAW,CAAE,cACf,CAAE,CAAA7B,QAAA,eACA/G,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAES,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,cACxD7G,KAAA,CAAC3D,UAAU,EAAC+K,OAAO,CAAC,WAAW,CAAC2B,KAAK,CAAC,SAAS,CAACpC,EAAE,CAAE,CAAEgC,UAAU,CAAE,MAAO,CAAE,CAAA9B,QAAA,EACxEvG,CAAC,CAAC,sBAAsB,CAAC,CAAC,GAC7B,EAAY,CAAC,CACV,CAAC,cACNR,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEoC,KAAK,CAAE,cAAe,CAAE,CAAAlC,QAAA,CACvDoF,MAAM,CAACS,UAAU,CACR,CAAC,CACZT,MAAM,CAACW,gBAAgB,eACtB9M,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,SAAS,CAAC2B,KAAK,CAAC,gBAAgB,CAACpC,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAC,CAAEL,OAAO,CAAE,OAAQ,CAAE,CAAAD,QAAA,CAClFtH,MAAM,CAAC,GAAI,CAAAqE,IAAI,CAACqI,MAAM,CAACW,gBAAgB,CAAC,CAAE,KAAK,CAAE,CAAEN,MAAM,CAAEvL,KAAK,CAAGvB,EAAE,CAAGC,IAAK,CAAC,CAAC,CACtE,CACb,EACE,CACN,EACE,CACN,CACF,CAAC,GAnDWwM,MAAM,CAAC5L,EAoDZ,CACX,CAAC,CACE,CAAC,cAEPP,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE0E,SAAS,CAAE,QAAS,CAAE,CAAAzE,QAAA,cACtC/G,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAAC2B,KAAK,CAAC,gBAAgB,CAAAlC,QAAA,CAC/CvG,CAAC,CAAC,0BAA0B,CAAC,CACpB,CAAC,CACV,CACN,EACI,CAAC,cAKRN,KAAA,CAACxC,MAAM,EACLqP,IAAI,CAAErK,QAAS,CACfsK,OAAO,CAAExJ,eAAgB,CACzBoD,QAAQ,CAAC,IAAI,CACb8C,SAAS,MACTuD,UAAU,CAAE,CACVpG,EAAE,CAAE,CACF6B,MAAM,CAAE,MAAM,CACdwE,SAAS,CAAE,OAAO,CAClBlG,OAAO,CAAE,MAAM,CACfoB,aAAa,CAAE,QACjB,CACF,CAAE,CAAArB,QAAA,eAEF/G,IAAA,CAACpC,WAAW,EAAAmJ,QAAA,cACV7G,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eAClF7G,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD/G,IAAA,CAACvD,MAAM,EACL4L,GAAG,CAAElH,OAAO,CAACmH,mBAAmB,CAC9BnH,OAAO,CAACmH,mBAAmB,CAACpC,UAAU,CAAC,MAAM,CAAC,CAC1C/E,OAAO,CAACmH,mBAAmB,CAC3B,4BAA4BnH,OAAO,CAACmH,mBAAmB,EAAE,CAC3D,EAAG,CACPC,GAAG,CAAEpH,OAAO,CAACqH,SAAU,CACvB3B,EAAE,CAAE,CAAEiC,EAAE,CAAE,CAAE,CAAE,CAAA/B,QAAA,CAEb5F,OAAO,CAACqH,SAAS,CAAC8D,MAAM,CAAC,CAAC,CAAC,CACtB,CAAC,cACTtM,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,IAAI,CAAAP,QAAA,CAAE5F,OAAO,CAACqH,SAAS,CAAa,CAAC,EACtD,CAAC,cACNxI,IAAA,CAACxC,UAAU,EAACiM,OAAO,CAAEjG,eAAgB,CAAC6G,IAAI,CAAC,OAAO,CAAAtD,QAAA,cAChD/G,IAAA,CAACb,SAAS,GAAE,CAAC,CACH,CAAC,EACV,CAAC,CACK,CAAC,cACda,IAAA,CAACrC,aAAa,EAACkJ,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAC,CAAEsF,QAAQ,CAAE,CAAC,CAAEnG,OAAO,CAAE,MAAM,CAAEoB,aAAa,CAAE,QAAS,CAAE,CAAArB,QAAA,CAChFjE,WAAW,cACV9C,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEwB,MAAM,CAAE,MAAO,CAAE,CAAA3B,QAAA,cAC3F/G,IAAA,CAAC5C,gBAAgB,GAAE,CAAC,CACjB,CAAC,cAEN8C,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAE6B,MAAM,CAAE,MAAM,CAAE1B,OAAO,CAAE,MAAM,CAAEoB,aAAa,CAAE,QAAS,CAAE,CAAArB,QAAA,eACpE/G,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEsG,QAAQ,CAAE,CAAC,CAAEC,QAAQ,CAAE,MAAM,CAAEvF,CAAC,CAAE,CAAE,CAAE,CAAAd,QAAA,CAC9C/D,YAAY,CAACyD,MAAM,GAAK,CAAC,cACxBzG,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEwB,MAAM,CAAE,MAAO,CAAE,CAAA3B,QAAA,cAC3F/G,IAAA,CAACzD,UAAU,EAAC0M,KAAK,CAAC,gBAAgB,CAAAlC,QAAA,CAAEvG,CAAC,CAAC,wBAAwB,CAAC,CAAa,CAAC,CAC1E,CAAC,CAENwC,YAAY,CAAC+B,GAAG,CAAEtC,OAAO,eACvBvC,KAAA,CAAC1D,GAAG,EAEFqK,EAAE,CAAE,CACFG,OAAO,CAAE,MAAM,CACfC,cAAc,CAAExE,OAAO,CAAC4B,SAAS,IAAKzD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEL,EAAE,EAAG,UAAU,CAAG,YAAY,CACjFoH,EAAE,CAAE,CACN,CAAE,CAAAZ,QAAA,EAEDtE,OAAO,CAAC4B,SAAS,IAAKzD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEL,EAAE,gBACpCP,IAAA,CAACvD,MAAM,EACL4L,GAAG,CAAElH,OAAO,CAACmH,mBAAmB,CAC9BnH,OAAO,CAACmH,mBAAmB,CAACpC,UAAU,CAAC,MAAM,CAAC,CAC1C/E,OAAO,CAACmH,mBAAmB,CAC3B,4BAA4BnH,OAAO,CAACmH,mBAAmB,EAAE,CAC3D,EAAG,CACPC,GAAG,CAAEpH,OAAO,CAACqH,SAAU,CACvB3B,EAAE,CAAE,CAAEiC,EAAE,CAAE,CAAC,CAAEL,KAAK,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAG,CAAE,CAAA3B,QAAA,CAEpC5F,OAAO,CAACqH,SAAS,CAAC8D,MAAM,CAAC,CAAC,CAAC,CACtB,CACT,cACDpM,KAAA,CAAC1D,GAAG,EACFqK,EAAE,CAAE,CACFD,QAAQ,CAAE,KAAK,CACfiB,CAAC,CAAE,CAAC,CACJgF,OAAO,CAAEpK,OAAO,CAAC4B,SAAS,IAAKzD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEL,EAAE,EAAG,cAAc,CAAG,UAAU,CAC5E0I,KAAK,CAAExG,OAAO,CAAC4B,SAAS,IAAKzD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEL,EAAE,EAAG,OAAO,CAAG,cAAc,CACvEuH,YAAY,CAAE,CAAC,CACf+C,QAAQ,CAAE,UACZ,CAAE,CAAA9D,QAAA,eAEF/G,IAAA,CAACzD,UAAU,EAAC+K,OAAO,CAAC,OAAO,CAAAP,QAAA,CAAEtE,OAAO,CAACmB,OAAO,CAAa,CAAC,cAC1D5D,IAAA,CAACzD,UAAU,EACT+K,OAAO,CAAC,SAAS,CACjBT,EAAE,CAAE,CACFG,OAAO,CAAE,OAAO,CAChBwE,SAAS,CAAE/I,OAAO,CAAC4B,SAAS,IAAKzD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEL,EAAE,EAAG,OAAO,CAAG,MAAM,CACnE8G,EAAE,CAAE,GAAG,CACP4B,KAAK,CAAExG,OAAO,CAAC4B,SAAS,IAAKzD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEL,EAAE,EAAG,uBAAuB,CAAG,gBAC3E,CAAE,CAAAwG,QAAA,CAED,GAAI,CAAAjD,IAAI,CAACrB,OAAO,CAAC6B,UAAU,CAAG,IAAI,CAAC,CAAC+I,kBAAkB,CAAC,CAAC,CAC/C,CAAC,EACV,CAAC,GA1CD5K,OAAO,CAAClC,EA2CV,CACN,CACF,CACE,CAAC,cACNP,IAAA,CAACtD,OAAO,GAAE,CAAC,cACXsD,IAAA,CAACxD,GAAG,EAACqK,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAC,CAAEyF,eAAe,CAAE,oBAAqB,CAAE,CAAAvG,QAAA,cACvD7G,KAAA,CAAC1D,GAAG,EAACqK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD/G,IAAA,CAACjC,SAAS,EACR2L,SAAS,MACT6D,WAAW,CAAE/M,CAAC,CAAC,kBAAkB,CAAE,CACnC8G,OAAO,CAAC,UAAU,CAClB+C,IAAI,CAAC,OAAO,CACZnB,KAAK,CAAEhG,YAAa,CACpBsK,QAAQ,CAAGC,CAAC,EAAKtK,eAAe,CAACsK,CAAC,CAAC/B,MAAM,CAACxC,KAAK,CAAE,CACjDwE,UAAU,CAAGD,CAAC,EAAK,CACjB,GAAIA,CAAC,CAACE,GAAG,GAAK,OAAO,EAAI,CAACF,CAAC,CAACG,QAAQ,CAAE,CACpCH,CAAC,CAACI,cAAc,CAAC,CAAC,CAClBpK,eAAe,CAAC,CAAC,CACnB,CACF,CAAE,CACFoD,EAAE,CAAE,CAAEiC,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACF9I,IAAA,CAACxC,UAAU,EACTyL,KAAK,CAAC,SAAS,CACfQ,OAAO,CAAEhG,eAAgB,CACzBqK,QAAQ,CAAE,CAAC5K,YAAY,CAACQ,IAAI,CAAC,CAAE,CAAAqD,QAAA,cAE/B/G,IAAA,CAACX,QAAQ,GAAE,CAAC,CACF,CAAC,EACV,CAAC,CACH,CAAC,EACH,CACN,CACY,CAAC,EACV,CAAC,EACe,CAAC,CACzB,CACH,CACQ,CACZ,CAED,mBAAOW,IAAA,CAACJ,MAAM,EAAAmH,QAAA,CAAEnD,OAAO,CAAS,CAAC,CACnC,CAAC,CAED,cAAe,CAAAvD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}