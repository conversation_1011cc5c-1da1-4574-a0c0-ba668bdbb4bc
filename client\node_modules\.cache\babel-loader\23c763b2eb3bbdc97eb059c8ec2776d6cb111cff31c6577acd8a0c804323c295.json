{"ast": null, "code": "import { usePress as $f6c31cce2adf654f$export$45712eceda6fad21 } from \"./usePress.mjs\";\nimport { useFocusable as $f645667febf57a63$export$4c014de7c8940b4c } from \"./useFocusable.mjs\";\nimport { useObjectRef as $hhDyF$useObjectRef, getOwnerWindow as $hhDyF$getOwnerWindow, isFocusable as $hhDyF$isFocusable, mergeProps as $hhDyF$mergeProps, mergeRefs as $hhDyF$mergeRefs } from \"@react-aria/utils\";\nimport $hhDyF$react, { useEffect as $hhDyF$useEffect } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nconst $3b117e43dc0ca95d$export$27c701ed9e449e99 = /*#__PURE__*/(0, $hhDyF$react).forwardRef(({\n  children: children,\n  ...props\n}, ref) => {\n  ref = (0, $hhDyF$useObjectRef)(ref);\n  let {\n    pressProps: pressProps\n  } = (0, $f6c31cce2adf654f$export$45712eceda6fad21)({\n    ...props,\n    ref: ref\n  });\n  let {\n    focusableProps: focusableProps\n  } = (0, $f645667febf57a63$export$4c014de7c8940b4c)(props, ref);\n  let child = (0, $hhDyF$react).Children.only(children);\n  (0, $hhDyF$useEffect)(() => {\n    if (process.env.NODE_ENV === 'production') return;\n    let el = ref.current;\n    if (!el || !(el instanceof (0, $hhDyF$getOwnerWindow)(el).Element)) {\n      console.error('<Pressable> child must forward its ref to a DOM element.');\n      return;\n    }\n    if (!props.isDisabled && !(0, $hhDyF$isFocusable)(el)) {\n      console.warn('<Pressable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n    if (el.localName !== 'button' && el.localName !== 'input' && el.localName !== 'select' && el.localName !== 'textarea' && el.localName !== 'a' && el.localName !== 'area' && el.localName !== 'summary') {\n      let role = el.getAttribute('role');\n      if (!role) console.warn('<Pressable> child must have an interactive ARIA role.');else if (\n      // https://w3c.github.io/aria/#widget_roles\n      role !== 'application' && role !== 'button' && role !== 'checkbox' && role !== 'combobox' && role !== 'gridcell' && role !== 'link' && role !== 'menuitem' && role !== 'menuitemcheckbox' && role !== 'menuitemradio' && role !== 'option' && role !== 'radio' && role !== 'searchbox' && role !== 'separator' && role !== 'slider' && role !== 'spinbutton' && role !== 'switch' && role !== 'tab' && role !== 'textbox' && role !== 'treeitem') console.warn(`<Pressable> child must have an interactive ARIA role. Got \"${role}\".`);\n    }\n  }, [ref, props.isDisabled]);\n  // @ts-ignore\n  let childRef = parseInt((0, $hhDyF$react).version, 10) < 19 ? child.ref : child.props.ref;\n  return /*#__PURE__*/(0, $hhDyF$react).cloneElement(child, {\n    ...(0, $hhDyF$mergeProps)(pressProps, focusableProps, child.props),\n    // @ts-ignore\n    ref: (0, $hhDyF$mergeRefs)(childRef, ref)\n  });\n});\nexport { $3b117e43dc0ca95d$export$27c701ed9e449e99 as Pressable };", "map": {"version": 3, "names": ["$3b117e43dc0ca95d$export$27c701ed9e449e99", "$hhDyF$react", "forwardRef", "children", "props", "ref", "$hhDyF$useObjectRef", "pressProps", "$f6c31cce2adf654f$export$45712eceda6fad21", "focusableProps", "$f645667febf57a63$export$4c014de7c8940b4c", "child", "Children", "only", "$hhDyF$useEffect", "process", "env", "NODE_ENV", "el", "current", "$hhDyF$getOwnerWindow", "Element", "console", "error", "isDisabled", "$hhDyF$isFocusable", "warn", "localName", "role", "getAttribute", "childRef", "parseInt", "version", "cloneElement", "$hhDyF$mergeProps", "$hhDyF$mergeRefs"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\Pressable.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement} from '@react-types/shared';\nimport {getOwnerWindow, isFocusable, mergeProps, mergeRefs, useObjectRef} from '@react-aria/utils';\nimport {PressProps, usePress} from './usePress';\nimport React, {ForwardedRef, ReactElement, useEffect} from 'react';\nimport {useFocusable} from './useFocusable';\n\ninterface PressableProps extends PressProps {\n  children: ReactElement<DOMAttributes, string>\n}\n\nexport const Pressable = React.forwardRef(({children, ...props}: PressableProps, ref: ForwardedRef<FocusableElement>) => {\n  ref = useObjectRef(ref);\n  let {pressProps} = usePress({...props, ref});\n  let {focusableProps} = useFocusable(props, ref);\n  let child = React.Children.only(children);\n\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n\n    let el = ref.current;\n    if (!el || !(el instanceof getOwnerWindow(el).Element)) {\n      console.error('<Pressable> child must forward its ref to a DOM element.');\n      return;\n    }\n\n    if (!props.isDisabled && !isFocusable(el)) {\n      console.warn('<Pressable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n\n    if (\n      el.localName !== 'button' &&\n      el.localName !== 'input' &&\n      el.localName !== 'select' &&\n      el.localName !== 'textarea' &&\n      el.localName !== 'a' &&\n      el.localName !== 'area' &&\n      el.localName !== 'summary'\n    ) {\n      let role = el.getAttribute('role');\n      if (!role) {\n        console.warn('<Pressable> child must have an interactive ARIA role.');\n      } else if (\n        // https://w3c.github.io/aria/#widget_roles\n        role !== 'application' &&\n        role !== 'button' &&\n        role !== 'checkbox' &&\n        role !== 'combobox' &&\n        role !== 'gridcell' &&\n        role !== 'link' &&\n        role !== 'menuitem' &&\n        role !== 'menuitemcheckbox' &&\n        role !== 'menuitemradio' &&\n        role !== 'option' &&\n        role !== 'radio' &&\n        role !== 'searchbox' &&\n        role !== 'separator' &&\n        role !== 'slider' &&\n        role !== 'spinbutton' &&\n        role !== 'switch' &&\n        role !== 'tab' &&\n        role !== 'textbox' &&\n        role !== 'treeitem'\n      ) {\n        console.warn(`<Pressable> child must have an interactive ARIA role. Got \"${role}\".`);\n      }\n    }\n  }, [ref, props.isDisabled]);\n\n  // @ts-ignore\n  let childRef = parseInt(React.version, 10) < 19 ? child.ref : child.props.ref;\n\n  return React.cloneElement(\n    child,\n    {\n      ...mergeProps(pressProps, focusableProps, child.props),\n      // @ts-ignore\n      ref: mergeRefs(childRef, ref)\n    }\n  );\n});\n"], "mappings": ";;;;;AAAA;;;;;;;;;;;;AAsBO,MAAMA,yCAAA,gBAAY,IAAAC,YAAI,EAAEC,UAAU,CAAC,CAAC;EAAAC,QAAA,EAACA,QAAQ;EAAE,GAAGC;AAAA,CAAsB,EAAEC,GAAA;EAC/EA,GAAA,GAAM,IAAAC,mBAAW,EAAED,GAAA;EACnB,IAAI;IAAAE,UAAA,EAACA;EAAU,CAAC,GAAG,IAAAC,yCAAO,EAAE;IAAC,GAAGJ,KAAK;SAAEC;EAAG;EAC1C,IAAI;IAAAI,cAAA,EAACA;EAAc,CAAC,GAAG,IAAAC,yCAAW,EAAEN,KAAA,EAAOC,GAAA;EAC3C,IAAIM,KAAA,GAAQ,IAAAV,YAAI,EAAEW,QAAQ,CAACC,IAAI,CAACV,QAAA;EAEhC,IAAAW,gBAAQ,EAAE;IACR,IAAIC,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,cAC3B;IAGF,IAAIC,EAAA,GAAKb,GAAA,CAAIc,OAAO;IACpB,IAAI,CAACD,EAAA,IAAM,EAAEA,EAAA,YAAc,IAAAE,qBAAa,EAAEF,EAAA,EAAIG,OAAO,CAAD,EAAI;MACtDC,OAAA,CAAQC,KAAK,CAAC;MACd;IACF;IAEA,IAAI,CAACnB,KAAA,CAAMoB,UAAU,IAAI,CAAC,IAAAC,kBAAU,EAAEP,EAAA,GAAK;MACzCI,OAAA,CAAQI,IAAI,CAAC;MACb;IACF;IAEA,IACER,EAAA,CAAGS,SAAS,KAAK,YACjBT,EAAA,CAAGS,SAAS,KAAK,WACjBT,EAAA,CAAGS,SAAS,KAAK,YACjBT,EAAA,CAAGS,SAAS,KAAK,cACjBT,EAAA,CAAGS,SAAS,KAAK,OACjBT,EAAA,CAAGS,SAAS,KAAK,UACjBT,EAAA,CAAGS,SAAS,KAAK,WACjB;MACA,IAAIC,IAAA,GAAOV,EAAA,CAAGW,YAAY,CAAC;MAC3B,IAAI,CAACD,IAAA,EACHN,OAAA,CAAQI,IAAI,CAAC,8DACR;MACL;MACAE,IAAA,KAAS,iBACTA,IAAA,KAAS,YACTA,IAAA,KAAS,cACTA,IAAA,KAAS,cACTA,IAAA,KAAS,cACTA,IAAA,KAAS,UACTA,IAAA,KAAS,cACTA,IAAA,KAAS,sBACTA,IAAA,KAAS,mBACTA,IAAA,KAAS,YACTA,IAAA,KAAS,WACTA,IAAA,KAAS,eACTA,IAAA,KAAS,eACTA,IAAA,KAAS,YACTA,IAAA,KAAS,gBACTA,IAAA,KAAS,YACTA,IAAA,KAAS,SACTA,IAAA,KAAS,aACTA,IAAA,KAAS,YAETN,OAAA,CAAQI,IAAI,CAAC,8DAA8DE,IAAA,IAAQ;IAEvF;EACF,GAAG,CAACvB,GAAA,EAAKD,KAAA,CAAMoB,UAAU,CAAC;EAE1B;EACA,IAAIM,QAAA,GAAWC,QAAA,CAAS,IAAA9B,YAAI,EAAE+B,OAAO,EAAE,MAAM,KAAKrB,KAAA,CAAMN,GAAG,GAAGM,KAAA,CAAMP,KAAK,CAACC,GAAG;EAE7E,oBAAO,IAAAJ,YAAI,EAAEgC,YAAY,CACvBtB,KAAA,EACA;IACE,GAAG,IAAAuB,iBAAS,EAAE3B,UAAA,EAAYE,cAAA,EAAgBE,KAAA,CAAMP,KAAK,CAAC;IACtD;IACAC,GAAA,EAAK,IAAA8B,gBAAQ,EAAEL,QAAA,EAAUzB,GAAA;EAC3B;AAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}