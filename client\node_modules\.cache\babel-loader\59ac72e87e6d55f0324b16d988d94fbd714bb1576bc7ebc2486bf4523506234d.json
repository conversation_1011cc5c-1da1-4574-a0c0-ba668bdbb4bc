{"ast": null, "code": "import { useCallback as T, useRef as E } from \"react\";\nimport * as d from '../utils/dom.js';\nimport { FocusableMode as g, isFocusableElement as y } from '../utils/focus-management.js';\nimport { isMobile as p } from '../utils/platform.js';\nimport { useDocumentEvent as a } from './use-document-event.js';\nimport { useLatestValue as L } from './use-latest-value.js';\nimport { useWindowEvent as x } from './use-window-event.js';\nconst C = 30;\nfunction k(o, f, h) {\n  let m = L(h),\n    s = T(function (e, c) {\n      if (e.defaultPrevented) return;\n      let r = c(e);\n      if (r === null || !r.getRootNode().contains(r) || !r.isConnected) return;\n      let M = function u(n) {\n        return typeof n == \"function\" ? u(n()) : Array.isArray(n) || n instanceof Set ? n : [n];\n      }(f);\n      for (let u of M) if (u !== null && (u.contains(r) || e.composed && e.composedPath().includes(u))) return;\n      return !y(r, g.Loose) && r.tabIndex !== -1 && e.preventDefault(), m.current(e, r);\n    }, [m, f]),\n    i = E(null);\n  a(o, \"pointerdown\", t => {\n    var e, c;\n    p() || (i.current = ((c = (e = t.composedPath) == null ? void 0 : e.call(t)) == null ? void 0 : c[0]) || t.target);\n  }, !0), a(o, \"pointerup\", t => {\n    if (p() || !i.current) return;\n    let e = i.current;\n    return i.current = null, s(t, () => e);\n  }, !0);\n  let l = E({\n    x: 0,\n    y: 0\n  });\n  a(o, \"touchstart\", t => {\n    l.current.x = t.touches[0].clientX, l.current.y = t.touches[0].clientY;\n  }, !0), a(o, \"touchend\", t => {\n    let e = {\n      x: t.changedTouches[0].clientX,\n      y: t.changedTouches[0].clientY\n    };\n    if (!(Math.abs(e.x - l.current.x) >= C || Math.abs(e.y - l.current.y) >= C)) return s(t, () => d.isHTMLorSVGElement(t.target) ? t.target : null);\n  }, !0), x(o, \"blur\", t => s(t, () => d.isHTMLIframeElement(window.document.activeElement) ? window.document.activeElement : null), !0);\n}\nexport { k as useOutsideClick };", "map": {"version": 3, "names": ["useCallback", "T", "useRef", "E", "d", "FocusableMode", "g", "isFocusableElement", "y", "isMobile", "p", "useDocumentEvent", "a", "useLatestValue", "L", "useWindowEvent", "x", "C", "k", "o", "f", "h", "m", "s", "e", "c", "defaultPrevented", "r", "getRootNode", "contains", "isConnected", "M", "u", "n", "Array", "isArray", "Set", "composed", "<PERSON><PERSON><PERSON>", "includes", "Loose", "tabIndex", "preventDefault", "current", "i", "t", "call", "target", "l", "touches", "clientX", "clientY", "changedTouches", "Math", "abs", "isHTMLorSVGElement", "isHTMLIframeElement", "window", "document", "activeElement", "useOutsideClick"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/use-outside-click.js"], "sourcesContent": ["import{useCallback as T,useRef as E}from\"react\";import*as d from'../utils/dom.js';import{FocusableMode as g,isFocusableElement as y}from'../utils/focus-management.js';import{isMobile as p}from'../utils/platform.js';import{useDocumentEvent as a}from'./use-document-event.js';import{useLatestValue as L}from'./use-latest-value.js';import{useWindowEvent as x}from'./use-window-event.js';const C=30;function k(o,f,h){let m=L(h),s=T(function(e,c){if(e.defaultPrevented)return;let r=c(e);if(r===null||!r.getRootNode().contains(r)||!r.isConnected)return;let M=function u(n){return typeof n==\"function\"?u(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let u of M)if(u!==null&&(u.contains(r)||e.composed&&e.composedPath().includes(u)))return;return!y(r,g.Loose)&&r.tabIndex!==-1&&e.preventDefault(),m.current(e,r)},[m,f]),i=E(null);a(o,\"pointerdown\",t=>{var e,c;p()||(i.current=((c=(e=t.composedPath)==null?void 0:e.call(t))==null?void 0:c[0])||t.target)},!0),a(o,\"pointerup\",t=>{if(p()||!i.current)return;let e=i.current;return i.current=null,s(t,()=>e)},!0);let l=E({x:0,y:0});a(o,\"touchstart\",t=>{l.current.x=t.touches[0].clientX,l.current.y=t.touches[0].clientY},!0),a(o,\"touchend\",t=>{let e={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY};if(!(Math.abs(e.x-l.current.x)>=C||Math.abs(e.y-l.current.y)>=C))return s(t,()=>d.isHTMLorSVGElement(t.target)?t.target:null)},!0),x(o,\"blur\",t=>s(t,()=>d.isHTMLIframeElement(window.document.activeElement)?window.document.activeElement:null),!0)}export{k as useOutsideClick};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,OAAM,KAAIC,CAAC,MAAK,iBAAiB;AAAC,SAAOC,aAAa,IAAIC,CAAC,EAACC,kBAAkB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,MAAMC,CAAC,GAAC,EAAE;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACR,CAAC,CAACO,CAAC,CAAC;IAACE,CAAC,GAACtB,CAAC,CAAC,UAASuB,CAAC,EAACC,CAAC,EAAC;MAAC,IAAGD,CAAC,CAACE,gBAAgB,EAAC;MAAO,IAAIC,CAAC,GAACF,CAAC,CAACD,CAAC,CAAC;MAAC,IAAGG,CAAC,KAAG,IAAI,IAAE,CAACA,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,CAAC,CAAC,IAAE,CAACA,CAAC,CAACG,WAAW,EAAC;MAAO,IAAIC,CAAC,GAAC,SAASC,CAACA,CAACC,CAAC,EAAC;QAAC,OAAO,OAAOA,CAAC,IAAE,UAAU,GAACD,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,GAACC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAAEA,CAAC,YAAYG,GAAG,GAACH,CAAC,GAAC,CAACA,CAAC,CAAC;MAAA,CAAC,CAACb,CAAC,CAAC;MAAC,KAAI,IAAIY,CAAC,IAAID,CAAC,EAAC,IAAGC,CAAC,KAAG,IAAI,KAAGA,CAAC,CAACH,QAAQ,CAACF,CAAC,CAAC,IAAEH,CAAC,CAACa,QAAQ,IAAEb,CAAC,CAACc,YAAY,CAAC,CAAC,CAACC,QAAQ,CAACP,CAAC,CAAC,CAAC,EAAC;MAAO,OAAM,CAACxB,CAAC,CAACmB,CAAC,EAACrB,CAAC,CAACkC,KAAK,CAAC,IAAEb,CAAC,CAACc,QAAQ,KAAG,CAAC,CAAC,IAAEjB,CAAC,CAACkB,cAAc,CAAC,CAAC,EAACpB,CAAC,CAACqB,OAAO,CAACnB,CAAC,EAACG,CAAC,CAAC;IAAA,CAAC,EAAC,CAACL,CAAC,EAACF,CAAC,CAAC,CAAC;IAACwB,CAAC,GAACzC,CAAC,CAAC,IAAI,CAAC;EAACS,CAAC,CAACO,CAAC,EAAC,aAAa,EAAC0B,CAAC,IAAE;IAAC,IAAIrB,CAAC,EAACC,CAAC;IAACf,CAAC,CAAC,CAAC,KAAGkC,CAAC,CAACD,OAAO,GAAC,CAAC,CAAClB,CAAC,GAAC,CAACD,CAAC,GAACqB,CAAC,CAACP,YAAY,KAAG,IAAI,GAAC,KAAK,CAAC,GAACd,CAAC,CAACsB,IAAI,CAACD,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACpB,CAAC,CAAC,CAAC,CAAC,KAAGoB,CAAC,CAACE,MAAM,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAACnC,CAAC,CAACO,CAAC,EAAC,WAAW,EAAC0B,CAAC,IAAE;IAAC,IAAGnC,CAAC,CAAC,CAAC,IAAE,CAACkC,CAAC,CAACD,OAAO,EAAC;IAAO,IAAInB,CAAC,GAACoB,CAAC,CAACD,OAAO;IAAC,OAAOC,CAAC,CAACD,OAAO,GAAC,IAAI,EAACpB,CAAC,CAACsB,CAAC,EAAC,MAAIrB,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC;EAAC,IAAIwB,CAAC,GAAC7C,CAAC,CAAC;IAACa,CAAC,EAAC,CAAC;IAACR,CAAC,EAAC;EAAC,CAAC,CAAC;EAACI,CAAC,CAACO,CAAC,EAAC,YAAY,EAAC0B,CAAC,IAAE;IAACG,CAAC,CAACL,OAAO,CAAC3B,CAAC,GAAC6B,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,EAACF,CAAC,CAACL,OAAO,CAACnC,CAAC,GAACqC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAACvC,CAAC,CAACO,CAAC,EAAC,UAAU,EAAC0B,CAAC,IAAE;IAAC,IAAIrB,CAAC,GAAC;MAACR,CAAC,EAAC6B,CAAC,CAACO,cAAc,CAAC,CAAC,CAAC,CAACF,OAAO;MAAC1C,CAAC,EAACqC,CAAC,CAACO,cAAc,CAAC,CAAC,CAAC,CAACD;IAAO,CAAC;IAAC,IAAG,EAAEE,IAAI,CAACC,GAAG,CAAC9B,CAAC,CAACR,CAAC,GAACgC,CAAC,CAACL,OAAO,CAAC3B,CAAC,CAAC,IAAEC,CAAC,IAAEoC,IAAI,CAACC,GAAG,CAAC9B,CAAC,CAAChB,CAAC,GAACwC,CAAC,CAACL,OAAO,CAACnC,CAAC,CAAC,IAAES,CAAC,CAAC,EAAC,OAAOM,CAAC,CAACsB,CAAC,EAAC,MAAIzC,CAAC,CAACmD,kBAAkB,CAACV,CAAC,CAACE,MAAM,CAAC,GAACF,CAAC,CAACE,MAAM,GAAC,IAAI,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC/B,CAAC,CAACG,CAAC,EAAC,MAAM,EAAC0B,CAAC,IAAEtB,CAAC,CAACsB,CAAC,EAAC,MAAIzC,CAAC,CAACoD,mBAAmB,CAACC,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC,GAACF,MAAM,CAACC,QAAQ,CAACC,aAAa,GAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOzC,CAAC,IAAI0C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}