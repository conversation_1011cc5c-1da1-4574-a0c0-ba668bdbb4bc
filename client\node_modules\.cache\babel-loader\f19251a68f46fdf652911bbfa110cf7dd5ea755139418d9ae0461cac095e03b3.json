{"ast": null, "code": "function r() {\n  return {\n    before({\n      doc: e,\n      d: o\n    }) {\n      o.style(e.documentElement, \"overflow\", \"hidden\");\n    }\n  };\n}\nexport { r as preventScroll };", "map": {"version": 3, "names": ["r", "before", "doc", "e", "d", "o", "style", "documentElement", "preventScroll"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js"], "sourcesContent": ["function r(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{r as preventScroll};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAM;IAACC,MAAMA,CAAC;MAACC,GAAG,EAACC,CAAC;MAACC,CAAC,EAACC;IAAC,CAAC,EAAC;MAACA,CAAC,CAACC,KAAK,CAACH,CAAC,CAACI,eAAe,EAAC,UAAU,EAAC,QAAQ,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOP,CAAC,IAAIQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}