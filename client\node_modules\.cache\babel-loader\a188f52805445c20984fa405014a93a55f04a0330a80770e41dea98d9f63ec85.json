{"ast": null, "code": "import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\nfunction _class_private_field_init(obj, privateMap, value) {\n  _check_private_redeclaration(obj, privateMap);\n  privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };", "map": {"version": 3, "names": ["_", "_check_private_redeclaration", "_class_private_field_init", "obj", "privateMap", "value", "set"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@swc/helpers/esm/_class_private_field_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,4BAA4B,QAAQ,mCAAmC;AAErF,SAASC,yBAAyBA,CAACC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAE;EACvDJ,4BAA4B,CAACE,GAAG,EAAEC,UAAU,CAAC;EAC7CA,UAAU,CAACE,GAAG,CAACH,GAAG,EAAEE,KAAK,CAAC;AAC9B;AACA,SAASH,yBAAyB,IAAIF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}