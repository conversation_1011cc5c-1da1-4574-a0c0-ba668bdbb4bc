{"ast": null, "code": "import { useStore as s } from '../../hooks/use-store.js';\nimport { useIsoMorphicEffect as u } from '../use-iso-morphic-effect.js';\nimport { overflows as t } from './overflow-store.js';\nfunction a(r, e, n = () => ({\n  containers: []\n})) {\n  let f = s(t),\n    o = e ? f.get(e) : void 0,\n    i = o ? o.count > 0 : !1;\n  return u(() => {\n    if (!(!e || !r)) return t.dispatch(\"PUSH\", e, n), () => t.dispatch(\"POP\", e, n);\n  }, [r, e]), i;\n}\nexport { a as useDocumentOverflowLockedEffect };", "map": {"version": 3, "names": ["useStore", "s", "useIsoMorphicEffect", "u", "overflows", "t", "a", "r", "e", "n", "containers", "f", "o", "get", "i", "count", "dispatch", "useDocumentOverflowLockedEffect"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js"], "sourcesContent": ["import{useStore as s}from'../../hooks/use-store.js';import{useIsoMorphicEffect as u}from'../use-iso-morphic-effect.js';import{overflows as t}from'./overflow-store.js';function a(r,e,n=()=>({containers:[]})){let f=s(t),o=e?f.get(e):void 0,i=o?o.count>0:!1;return u(()=>{if(!(!e||!r))return t.dispatch(\"PUSH\",e,n),()=>t.dispatch(\"POP\",e,n)},[r,e]),i}export{a as useDocumentOverflowLockedEffect};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,GAACA,CAAA,MAAK;EAACC,UAAU,EAAC;AAAE,CAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACV,CAAC,CAACI,CAAC,CAAC;IAACO,CAAC,GAACJ,CAAC,GAACG,CAAC,CAACE,GAAG,CAACL,CAAC,CAAC,GAAC,KAAK,CAAC;IAACM,CAAC,GAACF,CAAC,GAACA,CAAC,CAACG,KAAK,GAAC,CAAC,GAAC,CAAC,CAAC;EAAC,OAAOZ,CAAC,CAAC,MAAI;IAAC,IAAG,EAAE,CAACK,CAAC,IAAE,CAACD,CAAC,CAAC,EAAC,OAAOF,CAAC,CAACW,QAAQ,CAAC,MAAM,EAACR,CAAC,EAACC,CAAC,CAAC,EAAC,MAAIJ,CAAC,CAACW,QAAQ,CAAC,KAAK,EAACR,CAAC,EAACC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACF,CAAC,EAACC,CAAC,CAAC,CAAC,EAACM,CAAC;AAAA;AAAC,SAAOR,CAAC,IAAIW,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}