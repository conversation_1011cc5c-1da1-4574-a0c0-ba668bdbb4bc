{"ast": null, "code": "import { Keys as u } from '../components/keyboard.js';\nimport { useEventListener as i } from './use-event-listener.js';\nimport { useIsTopLayer as f } from './use-is-top-layer.js';\nfunction a(o, r = typeof document != \"undefined\" ? document.defaultView : null, t) {\n  let n = f(o, \"escape\");\n  i(r, \"keydown\", e => {\n    n && (e.defaultPrevented || e.key === u.Escape && t(e));\n  });\n}\nexport { a as useEscape };", "map": {"version": 3, "names": ["Keys", "u", "useEventListener", "i", "useIsTopLayer", "f", "a", "o", "r", "document", "defaultView", "t", "n", "e", "defaultPrevented", "key", "Escape", "useEscape"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/use-escape.js"], "sourcesContent": ["import{Keys as u}from'../components/keyboard.js';import{useEventListener as i}from'./use-event-listener.js';import{useIsTopLayer as f}from'./use-is-top-layer.js';function a(o,r=typeof document!=\"undefined\"?document.defaultView:null,t){let n=f(o,\"escape\");i(r,\"keydown\",e=>{n&&(e.defaultPrevented||e.key===u.Escape&&t(e))})}export{a as useEscape};\n"], "mappings": "AAAA,SAAOA,IAAI,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,GAAC,OAAOC,QAAQ,IAAE,WAAW,GAACA,QAAQ,CAACC,WAAW,GAAC,IAAI,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACP,CAAC,CAACE,CAAC,EAAC,QAAQ,CAAC;EAACJ,CAAC,CAACK,CAAC,EAAC,SAAS,EAACK,CAAC,IAAE;IAACD,CAAC,KAAGC,CAAC,CAACC,gBAAgB,IAAED,CAAC,CAACE,GAAG,KAAGd,CAAC,CAACe,MAAM,IAAEL,CAAC,CAACE,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC;AAAA;AAAC,SAAOP,CAAC,IAAIW,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}