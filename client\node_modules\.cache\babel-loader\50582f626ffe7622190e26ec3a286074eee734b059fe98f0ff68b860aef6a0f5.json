{"ast": null, "code": "import { useGlobalListeners as $AWxnT$useGlobalListeners, getOwnerDocument as $AWxnT$getOwnerDocument, nodeContains as $AWxnT$nodeContains } from \"@react-aria/utils\";\nimport { useState as $AWxnT$useState, useRef as $AWxnT$useRef, useEffect as $AWxnT$useEffect, useMemo as $AWxnT$useMemo } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet $6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents = false;\nlet $6179b936705e76d3$var$hoverCount = 0;\nfunction $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents() {\n  $6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents = true;\n  // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n  // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n  // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n  // the distant future because a user previously touched the element.\n  setTimeout(() => {\n    $6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents = false;\n  }, 50);\n}\nfunction $6179b936705e76d3$var$handleGlobalPointerEvent(e) {\n  if (e.pointerType === 'touch') $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents();\n}\nfunction $6179b936705e76d3$var$setupGlobalTouchEvents() {\n  if (typeof document === 'undefined') return;\n  if (typeof PointerEvent !== 'undefined') document.addEventListener('pointerup', $6179b936705e76d3$var$handleGlobalPointerEvent);else if (process.env.NODE_ENV === 'test') document.addEventListener('touchend', $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents);\n  $6179b936705e76d3$var$hoverCount++;\n  return () => {\n    $6179b936705e76d3$var$hoverCount--;\n    if ($6179b936705e76d3$var$hoverCount > 0) return;\n    if (typeof PointerEvent !== 'undefined') document.removeEventListener('pointerup', $6179b936705e76d3$var$handleGlobalPointerEvent);else if (process.env.NODE_ENV === 'test') document.removeEventListener('touchend', $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents);\n  };\n}\nfunction $6179b936705e76d3$export$ae780daf29e6d456(props) {\n  let {\n    onHoverStart: onHoverStart,\n    onHoverChange: onHoverChange,\n    onHoverEnd: onHoverEnd,\n    isDisabled: isDisabled\n  } = props;\n  let [isHovered, setHovered] = (0, $AWxnT$useState)(false);\n  let state = (0, $AWxnT$useRef)({\n    isHovered: false,\n    ignoreEmulatedMouseEvents: false,\n    pointerType: '',\n    target: null\n  }).current;\n  (0, $AWxnT$useEffect)($6179b936705e76d3$var$setupGlobalTouchEvents, []);\n  let {\n    addGlobalListener: addGlobalListener,\n    removeAllGlobalListeners: removeAllGlobalListeners\n  } = (0, $AWxnT$useGlobalListeners)();\n  let {\n    hoverProps: hoverProps,\n    triggerHoverEnd: triggerHoverEnd\n  } = (0, $AWxnT$useMemo)(() => {\n    let triggerHoverStart = (event, pointerType) => {\n      state.pointerType = pointerType;\n      if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) return;\n      state.isHovered = true;\n      let target = event.currentTarget;\n      state.target = target;\n      // When an element that is hovered over is removed, no pointerleave event is fired by the browser,\n      // even though the originally hovered target may have shrunk in size so it is no longer hovered.\n      // However, a pointerover event will be fired on the new target the mouse is over.\n      // In Chrome this happens immediately. In Safari and Firefox, it happens upon moving the mouse one pixel.\n      addGlobalListener((0, $AWxnT$getOwnerDocument)(event.target), 'pointerover', e => {\n        if (state.isHovered && state.target && !(0, $AWxnT$nodeContains)(state.target, e.target)) triggerHoverEnd(e, e.pointerType);\n      }, {\n        capture: true\n      });\n      if (onHoverStart) onHoverStart({\n        type: 'hoverstart',\n        target: target,\n        pointerType: pointerType\n      });\n      if (onHoverChange) onHoverChange(true);\n      setHovered(true);\n    };\n    let triggerHoverEnd = (event, pointerType) => {\n      let target = state.target;\n      state.pointerType = '';\n      state.target = null;\n      if (pointerType === 'touch' || !state.isHovered || !target) return;\n      state.isHovered = false;\n      removeAllGlobalListeners();\n      if (onHoverEnd) onHoverEnd({\n        type: 'hoverend',\n        target: target,\n        pointerType: pointerType\n      });\n      if (onHoverChange) onHoverChange(false);\n      setHovered(false);\n    };\n    let hoverProps = {};\n    if (typeof PointerEvent !== 'undefined') {\n      hoverProps.onPointerEnter = e => {\n        if ($6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') return;\n        triggerHoverStart(e, e.pointerType);\n      };\n      hoverProps.onPointerLeave = e => {\n        if (!isDisabled && e.currentTarget.contains(e.target)) triggerHoverEnd(e, e.pointerType);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      hoverProps.onTouchStart = () => {\n        state.ignoreEmulatedMouseEvents = true;\n      };\n      hoverProps.onMouseEnter = e => {\n        if (!state.ignoreEmulatedMouseEvents && !$6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents) triggerHoverStart(e, 'mouse');\n        state.ignoreEmulatedMouseEvents = false;\n      };\n      hoverProps.onMouseLeave = e => {\n        if (!isDisabled && e.currentTarget.contains(e.target)) triggerHoverEnd(e, 'mouse');\n      };\n    }\n    return {\n      hoverProps: hoverProps,\n      triggerHoverEnd: triggerHoverEnd\n    };\n  }, [onHoverStart, onHoverChange, onHoverEnd, isDisabled, state, addGlobalListener, removeAllGlobalListeners]);\n  (0, $AWxnT$useEffect)(() => {\n    // Call the triggerHoverEnd as soon as isDisabled changes to true\n    // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n    if (isDisabled) triggerHoverEnd({\n      currentTarget: state.target\n    }, state.pointerType);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled]);\n  return {\n    hoverProps: hoverProps,\n    isHovered: isHovered\n  };\n}\nexport { $6179b936705e76d3$export$ae780daf29e6d456 as useHover };", "map": {"version": 3, "names": ["$6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents", "$6179b936705e76d3$var$hoverCount", "$6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents", "setTimeout", "$6179b936705e76d3$var$handleGlobalPointerEvent", "e", "pointerType", "$6179b936705e76d3$var$setupGlobalTouchEvents", "document", "PointerEvent", "addEventListener", "process", "env", "NODE_ENV", "removeEventListener", "$6179b936705e76d3$export$ae780daf29e6d456", "props", "onHoverStart", "onHoverChange", "onHoverEnd", "isDisabled", "isHovered", "setHovered", "$AWxnT$useState", "state", "$AWxnT$useRef", "ignoreEmulatedMouseEvents", "target", "current", "$AWxnT$useEffect", "addGlobalListener", "removeAllGlobalListeners", "$AWxnT$useGlobalListeners", "hoverProps", "triggerHoverEnd", "$AWxnT$useMemo", "triggerHoverStart", "event", "currentTarget", "contains", "$AWxnT$getOwnerDocument", "$AWxnT$nodeContains", "capture", "type", "onPointerEnter", "onPointerLeave", "onTouchStart", "onMouseEnter", "onMouseLeave"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useHover.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, HoverEvents} from '@react-types/shared';\nimport {getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface HoverProps extends HoverEvents {\n  /** Whether the hover events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface HoverResult {\n  /** Props to spread on the target element. */\n  hoverProps: DOMAttributes,\n  isHovered: boolean\n}\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet globalIgnoreEmulatedMouseEvents = false;\nlet hoverCount = 0;\n\nfunction setGlobalIgnoreEmulatedMouseEvents() {\n  globalIgnoreEmulatedMouseEvents = true;\n\n  // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n  // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n  // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n  // the distant future because a user previously touched the element.\n  setTimeout(() => {\n    globalIgnoreEmulatedMouseEvents = false;\n  }, 50);\n}\n\nfunction handleGlobalPointerEvent(e) {\n  if (e.pointerType === 'touch') {\n    setGlobalIgnoreEmulatedMouseEvents();\n  }\n}\n\nfunction setupGlobalTouchEvents() {\n  if (typeof document === 'undefined') {\n    return;\n  }\n\n  if (typeof PointerEvent !== 'undefined') {\n    document.addEventListener('pointerup', handleGlobalPointerEvent);\n  } else if (process.env.NODE_ENV === 'test') {\n    document.addEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n  }\n\n  hoverCount++;\n  return () => {\n    hoverCount--;\n    if (hoverCount > 0) {\n      return;\n    }\n\n    if (typeof PointerEvent !== 'undefined') {\n      document.removeEventListener('pointerup', handleGlobalPointerEvent);\n    } else if (process.env.NODE_ENV === 'test') {\n      document.removeEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n    }\n  };\n}\n\n/**\n * Handles pointer hover interactions for an element. Normalizes behavior\n * across browsers and platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useHover(props: HoverProps): HoverResult {\n  let {\n    onHoverStart,\n    onHoverChange,\n    onHoverEnd,\n    isDisabled\n  } = props;\n\n  let [isHovered, setHovered] = useState(false);\n  let state = useRef({\n    isHovered: false,\n    ignoreEmulatedMouseEvents: false,\n    pointerType: '',\n    target: null\n  }).current;\n\n  useEffect(setupGlobalTouchEvents, []);\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let {hoverProps, triggerHoverEnd} = useMemo(() => {\n    let triggerHoverStart = (event, pointerType) => {\n      state.pointerType = pointerType;\n      if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) {\n        return;\n      }\n\n      state.isHovered = true;\n      let target = event.currentTarget;\n      state.target = target;\n\n      // When an element that is hovered over is removed, no pointerleave event is fired by the browser,\n      // even though the originally hovered target may have shrunk in size so it is no longer hovered.\n      // However, a pointerover event will be fired on the new target the mouse is over.\n      // In Chrome this happens immediately. In Safari and Firefox, it happens upon moving the mouse one pixel.\n      addGlobalListener(getOwnerDocument(event.target), 'pointerover', e => {\n        if (state.isHovered && state.target && !nodeContains(state.target, e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      }, {capture: true});\n\n      if (onHoverStart) {\n        onHoverStart({\n          type: 'hoverstart',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(true);\n      }\n\n      setHovered(true);\n    };\n\n    let triggerHoverEnd = (event, pointerType) => {\n      let target = state.target;\n      state.pointerType = '';\n      state.target = null;\n\n      if (pointerType === 'touch' || !state.isHovered || !target) {\n        return;\n      }\n\n      state.isHovered = false;\n      removeAllGlobalListeners();\n\n      if (onHoverEnd) {\n        onHoverEnd({\n          type: 'hoverend',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(false);\n      }\n\n      setHovered(false);\n    };\n\n    let hoverProps: DOMAttributes = {};\n\n    if (typeof PointerEvent !== 'undefined') {\n      hoverProps.onPointerEnter = (e) => {\n        if (globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') {\n          return;\n        }\n\n        triggerHoverStart(e, e.pointerType);\n      };\n\n      hoverProps.onPointerLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      hoverProps.onTouchStart = () => {\n        state.ignoreEmulatedMouseEvents = true;\n      };\n\n      hoverProps.onMouseEnter = (e) => {\n        if (!state.ignoreEmulatedMouseEvents && !globalIgnoreEmulatedMouseEvents) {\n          triggerHoverStart(e, 'mouse');\n        }\n\n        state.ignoreEmulatedMouseEvents = false;\n      };\n\n      hoverProps.onMouseLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, 'mouse');\n        }\n      };\n    }\n    return {hoverProps, triggerHoverEnd};\n  }, [onHoverStart, onHoverChange, onHoverEnd, isDisabled, state, addGlobalListener, removeAllGlobalListeners]);\n\n  useEffect(() => {\n    // Call the triggerHoverEnd as soon as isDisabled changes to true\n    // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n    if (isDisabled) {\n      triggerHoverEnd({currentTarget: state.target}, state.pointerType);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled]);\n\n  return {\n    hoverProps,\n    isHovered\n  };\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;GAAA,CAYA;AACA;AACA;AACA;;AAiBA;AACA;AACA;AACA,IAAIA,qDAAA,GAAkC;AACtC,IAAIC,gCAAA,GAAa;AAEjB,SAASC,yDAAA;EACPF,qDAAA,GAAkC;EAElC;EACA;EACA;EACA;EACAG,UAAA,CAAW;IACTH,qDAAA,GAAkC;EACpC,GAAG;AACL;AAEA,SAASI,+CAAyBC,CAAC;EACjC,IAAIA,CAAA,CAAEC,WAAW,KAAK,SACpBJ,wDAAA;AAEJ;AAEA,SAASK,6CAAA;EACP,IAAI,OAAOC,QAAA,KAAa,aACtB;EAGF,IAAI,OAAOC,YAAA,KAAiB,aAC1BD,QAAA,CAASE,gBAAgB,CAAC,aAAaN,8CAAA,OAClC,IAAIO,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAClCL,QAAA,CAASE,gBAAgB,CAAC,YAAYR,wDAAA;EAGxCD,gCAAA;EACA,OAAO;IACLA,gCAAA;IACA,IAAIA,gCAAA,GAAa,GACf;IAGF,IAAI,OAAOQ,YAAA,KAAiB,aAC1BD,QAAA,CAASM,mBAAmB,CAAC,aAAaV,8CAAA,OACrC,IAAIO,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAClCL,QAAA,CAASM,mBAAmB,CAAC,YAAYZ,wDAAA;EAE7C;AACF;AAMO,SAASa,0CAASC,KAAiB;EACxC,IAAI;IAAAC,YAAA,EACFA,YAAY;IAAAC,aAAA,EACZA,aAAa;IAAAC,UAAA,EACbA,UAAU;IAAAC,UAAA,EACVA;EAAU,CACX,GAAGJ,KAAA;EAEJ,IAAI,CAACK,SAAA,EAAWC,UAAA,CAAW,GAAG,IAAAC,eAAO,EAAE;EACvC,IAAIC,KAAA,GAAQ,IAAAC,aAAK,EAAE;IACjBJ,SAAA,EAAW;IACXK,yBAAA,EAA2B;IAC3BpB,WAAA,EAAa;IACbqB,MAAA,EAAQ;EACV,GAAGC,OAAO;EAEV,IAAAC,gBAAQ,EAAEtB,4CAAA,EAAwB,EAAE;EACpC,IAAI;IAAAuB,iBAAA,EAACA,iBAAiB;IAAAC,wBAAA,EAAEA;EAAwB,CAAC,GAAG,IAAAC,yBAAiB;EAErE,IAAI;IAAAC,UAAA,EAACA,UAAU;IAAAC,eAAA,EAAEA;EAAe,CAAC,GAAG,IAAAC,cAAM,EAAE;IAC1C,IAAIC,iBAAA,GAAoBA,CAACC,KAAA,EAAO/B,WAAA;MAC9BkB,KAAA,CAAMlB,WAAW,GAAGA,WAAA;MACpB,IAAIc,UAAA,IAAcd,WAAA,KAAgB,WAAWkB,KAAA,CAAMH,SAAS,IAAI,CAACgB,KAAA,CAAMC,aAAa,CAACC,QAAQ,CAACF,KAAA,CAAMV,MAAM,GACxG;MAGFH,KAAA,CAAMH,SAAS,GAAG;MAClB,IAAIM,MAAA,GAASU,KAAA,CAAMC,aAAa;MAChCd,KAAA,CAAMG,MAAM,GAAGA,MAAA;MAEf;MACA;MACA;MACA;MACAG,iBAAA,CAAkB,IAAAU,uBAAe,EAAEH,KAAA,CAAMV,MAAM,GAAG,eAAetB,CAAA;QAC/D,IAAImB,KAAA,CAAMH,SAAS,IAAIG,KAAA,CAAMG,MAAM,IAAI,CAAC,IAAAc,mBAAW,EAAEjB,KAAA,CAAMG,MAAM,EAAEtB,CAAA,CAAEsB,MAAM,GACzEO,eAAA,CAAgB7B,CAAA,EAAGA,CAAA,CAAEC,WAAW;MAEpC,GAAG;QAACoC,OAAA,EAAS;MAAI;MAEjB,IAAIzB,YAAA,EACFA,YAAA,CAAa;QACX0B,IAAA,EAAM;gBACNhB,MAAA;qBACArB;MACF;MAGF,IAAIY,aAAA,EACFA,aAAA,CAAc;MAGhBI,UAAA,CAAW;IACb;IAEA,IAAIY,eAAA,GAAkBA,CAACG,KAAA,EAAO/B,WAAA;MAC5B,IAAIqB,MAAA,GAASH,KAAA,CAAMG,MAAM;MACzBH,KAAA,CAAMlB,WAAW,GAAG;MACpBkB,KAAA,CAAMG,MAAM,GAAG;MAEf,IAAIrB,WAAA,KAAgB,WAAW,CAACkB,KAAA,CAAMH,SAAS,IAAI,CAACM,MAAA,EAClD;MAGFH,KAAA,CAAMH,SAAS,GAAG;MAClBU,wBAAA;MAEA,IAAIZ,UAAA,EACFA,UAAA,CAAW;QACTwB,IAAA,EAAM;gBACNhB,MAAA;qBACArB;MACF;MAGF,IAAIY,aAAA,EACFA,aAAA,CAAc;MAGhBI,UAAA,CAAW;IACb;IAEA,IAAIW,UAAA,GAA4B,CAAC;IAEjC,IAAI,OAAOxB,YAAA,KAAiB,aAAa;MACvCwB,UAAA,CAAWW,cAAc,GAAIvC,CAAA;QAC3B,IAAIL,qDAAA,IAAmCK,CAAA,CAAEC,WAAW,KAAK,SACvD;QAGF8B,iBAAA,CAAkB/B,CAAA,EAAGA,CAAA,CAAEC,WAAW;MACpC;MAEA2B,UAAA,CAAWY,cAAc,GAAIxC,CAAA;QAC3B,IAAI,CAACe,UAAA,IAAcf,CAAA,CAAEiC,aAAa,CAACC,QAAQ,CAAClC,CAAA,CAAEsB,MAAM,GAClDO,eAAA,CAAgB7B,CAAA,EAAGA,CAAA,CAAEC,WAAW;MAEpC;IACF,OAAO,IAAIK,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAAQ;MAC1CoB,UAAA,CAAWa,YAAY,GAAG;QACxBtB,KAAA,CAAME,yBAAyB,GAAG;MACpC;MAEAO,UAAA,CAAWc,YAAY,GAAI1C,CAAA;QACzB,IAAI,CAACmB,KAAA,CAAME,yBAAyB,IAAI,CAAC1B,qDAAA,EACvCoC,iBAAA,CAAkB/B,CAAA,EAAG;QAGvBmB,KAAA,CAAME,yBAAyB,GAAG;MACpC;MAEAO,UAAA,CAAWe,YAAY,GAAI3C,CAAA;QACzB,IAAI,CAACe,UAAA,IAAcf,CAAA,CAAEiC,aAAa,CAACC,QAAQ,CAAClC,CAAA,CAAEsB,MAAM,GAClDO,eAAA,CAAgB7B,CAAA,EAAG;MAEvB;IACF;IACA,OAAO;kBAAC4B,UAAA;uBAAYC;IAAe;EACrC,GAAG,CAACjB,YAAA,EAAcC,aAAA,EAAeC,UAAA,EAAYC,UAAA,EAAYI,KAAA,EAAOM,iBAAA,EAAmBC,wBAAA,CAAyB;EAE5G,IAAAF,gBAAQ,EAAE;IACR;IACA;IACA,IAAIT,UAAA,EACFc,eAAA,CAAgB;MAACI,aAAA,EAAed,KAAA,CAAMG;IAAM,GAAGH,KAAA,CAAMlB,WAAW;IAEpE;EACA,GAAG,CAACc,UAAA,CAAW;EAEf,OAAO;gBACLa,UAAA;eACAZ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}