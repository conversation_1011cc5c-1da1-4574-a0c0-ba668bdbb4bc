{"ast": null, "code": "import { usePress as $f6c31cce2adf654f$export$45712eceda6fad21 } from \"./usePress.mjs\";\nimport { useGlobalListeners as $4k2kv$useGlobalListeners, getOwnerDocument as $4k2kv$getOwnerDocument, focusWithoutScrolling as $4k2kv$focusWithoutScrolling, useDescription as $4k2kv$useDescription, mergeProps as $4k2kv$mergeProps } from \"@react-aria/utils\";\nimport { useRef as $4k2kv$useRef } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nconst $8a26561d2877236e$var$DEFAULT_THRESHOLD = 500;\nfunction $8a26561d2877236e$export$c24ed0104d07eab9(props) {\n  let {\n    isDisabled: isDisabled,\n    onLongPressStart: onLongPressStart,\n    onLongPressEnd: onLongPressEnd,\n    onLongPress: onLongPress,\n    threshold = $8a26561d2877236e$var$DEFAULT_THRESHOLD,\n    accessibilityDescription: accessibilityDescription\n  } = props;\n  const timeRef = (0, $4k2kv$useRef)(undefined);\n  let {\n    addGlobalListener: addGlobalListener,\n    removeGlobalListener: removeGlobalListener\n  } = (0, $4k2kv$useGlobalListeners)();\n  let {\n    pressProps: pressProps\n  } = (0, $f6c31cce2adf654f$export$45712eceda6fad21)({\n    isDisabled: isDisabled,\n    onPressStart(e) {\n      e.continuePropagation();\n      if (e.pointerType === 'mouse' || e.pointerType === 'touch') {\n        if (onLongPressStart) onLongPressStart({\n          ...e,\n          type: 'longpressstart'\n        });\n        timeRef.current = setTimeout(() => {\n          // Prevent other usePress handlers from also handling this event.\n          e.target.dispatchEvent(new PointerEvent('pointercancel', {\n            bubbles: true\n          }));\n          // Ensure target is focused. On touch devices, browsers typically focus on pointer up.\n          if ((0, $4k2kv$getOwnerDocument)(e.target).activeElement !== e.target) (0, $4k2kv$focusWithoutScrolling)(e.target);\n          if (onLongPress) onLongPress({\n            ...e,\n            type: 'longpress'\n          });\n          timeRef.current = undefined;\n        }, threshold);\n        // Prevent context menu, which may be opened on long press on touch devices\n        if (e.pointerType === 'touch') {\n          let onContextMenu = e => {\n            e.preventDefault();\n          };\n          addGlobalListener(e.target, 'contextmenu', onContextMenu, {\n            once: true\n          });\n          addGlobalListener(window, 'pointerup', () => {\n            // If no contextmenu event is fired quickly after pointerup, remove the handler\n            // so future context menu events outside a long press are not prevented.\n            setTimeout(() => {\n              removeGlobalListener(e.target, 'contextmenu', onContextMenu);\n            }, 30);\n          }, {\n            once: true\n          });\n        }\n      }\n    },\n    onPressEnd(e) {\n      if (timeRef.current) clearTimeout(timeRef.current);\n      if (onLongPressEnd && (e.pointerType === 'mouse' || e.pointerType === 'touch')) onLongPressEnd({\n        ...e,\n        type: 'longpressend'\n      });\n    }\n  });\n  let descriptionProps = (0, $4k2kv$useDescription)(onLongPress && !isDisabled ? accessibilityDescription : undefined);\n  return {\n    longPressProps: (0, $4k2kv$mergeProps)(pressProps, descriptionProps)\n  };\n}\nexport { $8a26561d2877236e$export$c24ed0104d07eab9 as useLongPress };", "map": {"version": 3, "names": ["$8a26561d2877236e$var$DEFAULT_THRESHOLD", "$8a26561d2877236e$export$c24ed0104d07eab9", "props", "isDisabled", "onLongPressStart", "onLongPressEnd", "onLongPress", "threshold", "accessibilityDescription", "timeRef", "$4k2kv$useRef", "undefined", "addGlobalListener", "removeGlobalListener", "$4k2kv$useGlobalListeners", "pressProps", "$f6c31cce2adf654f$export$45712eceda6fad21", "onPressStart", "e", "continuePropagation", "pointerType", "type", "current", "setTimeout", "target", "dispatchEvent", "PointerEvent", "bubbles", "$4k2kv$getOwnerDocument", "activeElement", "$4k2kv$focusWithoutScrolling", "onContextMenu", "preventDefault", "once", "window", "onPressEnd", "clearTimeout", "descriptionProps", "$4k2kv$useDescription", "longPressProps", "$4k2kv$mergeProps"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useLongPress.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, LongPressEvent} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerDocument, mergeProps, useDescription, useGlobalListeners} from '@react-aria/utils';\nimport {usePress} from './usePress';\nimport {useRef} from 'react';\n\nexport interface LongPressProps {\n  /** Whether long press events should be disabled. */\n  isDisabled?: boolean,\n  /** Handler that is called when a long press interaction starts. */\n  onLongPressStart?: (e: LongPressEvent) => void,\n  /**\n   * Handler that is called when a long press interaction ends, either\n   * over the target or when the pointer leaves the target.\n   */\n  onLongPressEnd?: (e: LongPressEvent) => void,\n  /**\n   * Handler that is called when the threshold time is met while\n   * the press is over the target.\n   */\n  onLongPress?: (e: LongPressEvent) => void,\n  /**\n   * The amount of time in milliseconds to wait before triggering a long press.\n   * @default 500ms\n   */\n  threshold?: number,\n  /**\n   * A description for assistive techology users indicating that a long press\n   * action is available, e.g. \"Long press to open menu\".\n   */\n  accessibilityDescription?: string\n}\n\nexport interface LongPressResult {\n  /** Props to spread on the target element. */\n  longPressProps: DOMAttributes\n}\n\nconst DEFAULT_THRESHOLD = 500;\n\n/**\n * Handles long press interactions across mouse and touch devices. Supports a customizable time threshold,\n * accessibility description, and normalizes behavior across browsers and devices.\n */\nexport function useLongPress(props: LongPressProps): LongPressResult {\n  let {\n    isDisabled,\n    onLongPressStart,\n    onLongPressEnd,\n    onLongPress,\n    threshold = DEFAULT_THRESHOLD,\n    accessibilityDescription\n  } = props;\n\n  const timeRef = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);\n  let {addGlobalListener, removeGlobalListener} = useGlobalListeners();\n\n  let {pressProps} = usePress({\n    isDisabled,\n    onPressStart(e) {\n      e.continuePropagation();\n      if (e.pointerType === 'mouse' || e.pointerType === 'touch') {\n        if (onLongPressStart) {\n          onLongPressStart({\n            ...e,\n            type: 'longpressstart'\n          });\n        }\n\n        timeRef.current = setTimeout(() => {\n          // Prevent other usePress handlers from also handling this event.\n          e.target.dispatchEvent(new PointerEvent('pointercancel', {bubbles: true}));\n\n          // Ensure target is focused. On touch devices, browsers typically focus on pointer up.\n          if (getOwnerDocument(e.target).activeElement !== e.target) {\n            focusWithoutScrolling(e.target as FocusableElement);\n          }\n\n          if (onLongPress) {\n            onLongPress({\n              ...e,\n              type: 'longpress'\n            });\n          }\n          timeRef.current = undefined;\n        }, threshold);\n\n        // Prevent context menu, which may be opened on long press on touch devices\n        if (e.pointerType === 'touch') {\n          let onContextMenu = e => {\n            e.preventDefault();\n          };\n\n          addGlobalListener(e.target, 'contextmenu', onContextMenu, {once: true});\n          addGlobalListener(window, 'pointerup', () => {\n            // If no contextmenu event is fired quickly after pointerup, remove the handler\n            // so future context menu events outside a long press are not prevented.\n            setTimeout(() => {\n              removeGlobalListener(e.target, 'contextmenu', onContextMenu);\n            }, 30);\n          }, {once: true});\n        }\n      }\n    },\n    onPressEnd(e) {\n      if (timeRef.current) {\n        clearTimeout(timeRef.current);\n      }\n\n      if (onLongPressEnd && (e.pointerType === 'mouse' || e.pointerType === 'touch')) {\n        onLongPressEnd({\n          ...e,\n          type: 'longpressend'\n        });\n      }\n    }\n  });\n\n  let descriptionProps = useDescription(onLongPress && !isDisabled ? accessibilityDescription : undefined);\n\n  return {\n    longPressProps: mergeProps(pressProps, descriptionProps)\n  };\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;AAiDA,MAAMA,uCAAA,GAAoB;AAMnB,SAASC,0CAAaC,KAAqB;EAChD,IAAI;IAAAC,UAAA,EACFA,UAAU;IAAAC,gBAAA,EACVA,gBAAgB;IAAAC,cAAA,EAChBA,cAAc;IAAAC,WAAA,EACdA,WAAW;IACXC,SAAA,GAAYP,uCAAA;IAAAQ,wBAAA,EACZA;EAAwB,CACzB,GAAGN,KAAA;EAEJ,MAAMO,OAAA,GAAU,IAAAC,aAAK,EAA6CC,SAAA;EAClE,IAAI;IAAAC,iBAAA,EAACA,iBAAiB;IAAAC,oBAAA,EAAEA;EAAoB,CAAC,GAAG,IAAAC,yBAAiB;EAEjE,IAAI;IAAAC,UAAA,EAACA;EAAU,CAAC,GAAG,IAAAC,yCAAO,EAAE;gBAC1Bb,UAAA;IACAc,aAAaC,CAAC;MACZA,CAAA,CAAEC,mBAAmB;MACrB,IAAID,CAAA,CAAEE,WAAW,KAAK,WAAWF,CAAA,CAAEE,WAAW,KAAK,SAAS;QAC1D,IAAIhB,gBAAA,EACFA,gBAAA,CAAiB;UACf,GAAGc,CAAC;UACJG,IAAA,EAAM;QACR;QAGFZ,OAAA,CAAQa,OAAO,GAAGC,UAAA,CAAW;UAC3B;UACAL,CAAA,CAAEM,MAAM,CAACC,aAAa,CAAC,IAAIC,YAAA,CAAa,iBAAiB;YAACC,OAAA,EAAS;UAAI;UAEvE;UACA,IAAI,IAAAC,uBAAe,EAAEV,CAAA,CAAEM,MAAM,EAAEK,aAAa,KAAKX,CAAA,CAAEM,MAAM,EACvD,IAAAM,4BAAoB,EAAEZ,CAAA,CAAEM,MAAM;UAGhC,IAAIlB,WAAA,EACFA,WAAA,CAAY;YACV,GAAGY,CAAC;YACJG,IAAA,EAAM;UACR;UAEFZ,OAAA,CAAQa,OAAO,GAAGX,SAAA;QACpB,GAAGJ,SAAA;QAEH;QACA,IAAIW,CAAA,CAAEE,WAAW,KAAK,SAAS;UAC7B,IAAIW,aAAA,GAAgBb,CAAA;YAClBA,CAAA,CAAEc,cAAc;UAClB;UAEApB,iBAAA,CAAkBM,CAAA,CAAEM,MAAM,EAAE,eAAeO,aAAA,EAAe;YAACE,IAAA,EAAM;UAAI;UACrErB,iBAAA,CAAkBsB,MAAA,EAAQ,aAAa;YACrC;YACA;YACAX,UAAA,CAAW;cACTV,oBAAA,CAAqBK,CAAA,CAAEM,MAAM,EAAE,eAAeO,aAAA;YAChD,GAAG;UACL,GAAG;YAACE,IAAA,EAAM;UAAI;QAChB;MACF;IACF;IACAE,WAAWjB,CAAC;MACV,IAAIT,OAAA,CAAQa,OAAO,EACjBc,YAAA,CAAa3B,OAAA,CAAQa,OAAO;MAG9B,IAAIjB,cAAA,KAAmBa,CAAA,CAAEE,WAAW,KAAK,WAAWF,CAAA,CAAEE,WAAW,KAAK,OAAM,GAC1Ef,cAAA,CAAe;QACb,GAAGa,CAAC;QACJG,IAAA,EAAM;MACR;IAEJ;EACF;EAEA,IAAIgB,gBAAA,GAAmB,IAAAC,qBAAa,EAAEhC,WAAA,IAAe,CAACH,UAAA,GAAaK,wBAAA,GAA2BG,SAAA;EAE9F,OAAO;IACL4B,cAAA,EAAgB,IAAAC,iBAAS,EAAEzB,UAAA,EAAYsB,gBAAA;EACzC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}