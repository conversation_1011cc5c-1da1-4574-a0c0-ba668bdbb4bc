const mysql = require('mysql2/promise');
const config = require('../config/db.config');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const db = require('../config/db');

// Ensure upload directory exists
const uploadDir = path.join(__dirname, '..', 'uploads', 'profile-pictures');
fs.mkdir(uploadDir, { recursive: true }).catch(console.error);

// Ensure commitment documents directory exists
const commitmentDocsDir = path.join(__dirname, '..', 'uploads', 'commitment-documents');
fs.mkdir(commitmentDocsDir, { recursive: true }).catch(console.error);

// Configure multer storage
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      // Determinar el directorio de destino según el tipo de archivo
      let destDir = uploadDir; // Por defecto, directorio de imágenes de perfil

      if (file.fieldname === 'commitmentDocument') {
        destDir = commitmentDocsDir;
        await fs.mkdir(destDir, { recursive: true });
      } else {
        await fs.mkdir(uploadDir, { recursive: true });
      }

      cb(null, destDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);

    if (file.fieldname === 'commitmentDocument') {
      cb(null, `commitment-${req.user.id}-${uniqueSuffix}${ext}`);
    } else {
      cb(null, `profilePicture-${uniqueSuffix}${ext}`);
    }
  }
});

// Configure multer upload
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Verificar el tipo de archivo según el campo
    if (file.fieldname === 'commitmentDocument') {
      // Para documentos de compromiso, solo permitir PDF
      if (file.mimetype !== 'application/pdf') {
        return cb(new Error('Only PDF files are allowed for commitment documents'), false);
      }
    } else {
      // Para imágenes de perfil, permitir JPG, JPEG y PNG
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.mimetype)) {
        return cb(new Error('Only JPG, JPEG, and PNG files are allowed for profile pictures'), false);
      }
    }
    cb(null, true);
  }
});

const getStatusMessage = (status) => {
  return `teacher.application.statusMessage.${status}`;
};

const submitApplication = async (req, res) => {
  console.log('Auth user:', req.user);
  console.log('Request body:', req.body);
  console.log('Request file:', req.file);
  console.log('Update application flag:', req.body.updateApplication);

  if (!req.user || !req.user.id) {
    console.error('No authenticated user found');
    return res.status(401).json({ message: 'Not authenticated' });
  }

  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Extract fields with default values
    const {
      country = '',
      residence = '',
      nativeLanguage = '',
      teachingLanguages = '[]',
      courseTypes = '[]',
      qualifications = '',
      teachingExperience = 0,
      introVideoUrl = '',
      cv = '',
      phone = '',
      availableHours = '[]',
      pricePerLesson = 0,
      trialLessonPrice = 0,
      timezone = '',
      paymentMethod = '',
      commitmentAccepted = false
    } = req.body;

    // Log extracted data
    console.log('Extracted data:', {
      country,
      residence,
      nativeLanguage,
      teachingLanguages,
      courseTypes,
      qualifications,
      teachingExperience,
      introVideoUrl,
      cv,
      phone,
      availableHours,
      pricePerLesson,
      trialLessonPrice,
      timezone,
      paymentMethod,
      commitmentAccepted
    });

    // Get profile picture path if uploaded
    let profilePictureUrl = null;

    if (req.file) {
      // User uploaded a profile picture
      profilePictureUrl = `/uploads/profile-pictures/${req.file.filename}`;
    }

    // Parse JSON strings if needed
    const parsedTeachingLanguages = typeof teachingLanguages === 'string'
      ? teachingLanguages
      : JSON.stringify(teachingLanguages);

    const parsedCourseTypes = typeof courseTypes === 'string'
      ? courseTypes
      : JSON.stringify(courseTypes);

    const parsedAvailableHours = typeof availableHours === 'string'
      ? availableHours
      : JSON.stringify(availableHours);

    // Start transaction
    await connection.beginTransaction();

    try {
      // Check if application already exists
      const [existingApplications] = await connection.execute(
        'SELECT id FROM teacher_profiles WHERE user_id = ?',
        [req.user.id]
      );

      // Update users table with profile picture URL if provided
      if (profilePictureUrl) {
        await connection.execute(
          'UPDATE users SET profile_picture_url = ? WHERE id = ?',
          [profilePictureUrl, req.user.id]
        );
      }

      let result;
      // Check if this is an application update from an approved teacher
      const isApplicationUpdate = req.body.updateApplication === 'true';
      console.log('Is application update:', isApplicationUpdate);
      console.log('Update application flag in request body:', req.body.updateApplication);

      // Check if the teacher was previously approved
      let wasApproved = false;
      if (existingApplications.length > 0) {
        const [statusResult] = await connection.execute(
          'SELECT status FROM teacher_profiles WHERE id = ?',
          [existingApplications[0].id]
        );
        wasApproved = statusResult.length > 0 && statusResult[0].status === 'approved';
        console.log('Was previously approved:', wasApproved);
      }

      if (existingApplications.length > 0) {
        // Update existing application
        [result] = await connection.execute(
          `UPDATE teacher_profiles SET
            country = ?,
            residence = ?,
            native_language = ?,
            teaching_languages = ?,
            course_types = ?,
            qualifications = ?,
            teaching_experience = ?,
            intro_video_url = ?,
            cv = ?,
            phone = ?,
            profile_picture_url = COALESCE(?, profile_picture_url),
            commitment_accepted = ?,
            available_hours = ?,
            price_per_lesson = ?,
            trial_lesson_price = ?,
            timezone = ?,
            payment_method = ?,
            status = 'pending'
          WHERE user_id = ?`,
          [
            country,
            residence,
            nativeLanguage,
            parsedTeachingLanguages,
            parsedCourseTypes,
            qualifications,
            teachingExperience,
            introVideoUrl,
            cv,
            phone,
            profilePictureUrl,
            commitmentAccepted === 'true' || commitmentAccepted === true ? 1 : 0,
            parsedAvailableHours,
            pricePerLesson,
            trialLessonPrice,
            timezone,
            paymentMethod,
            req.user.id
          ]
        );

        // Delete existing teacher categories
        await connection.execute(
          'DELETE FROM teacher_categories WHERE teacher_profile_id = ?',
          [existingApplications[0].id]
        );
      } else {
        // Insert new application
        [result] = await connection.execute(
          `INSERT INTO teacher_profiles (
            user_id, country, residence, native_language, teaching_languages,
            course_types, qualifications, teaching_experience, intro_video_url, cv,
            phone, profile_picture_url, commitment_accepted, available_hours, price_per_lesson, trial_lesson_price, timezone,
            payment_method, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')`,
          [
            req.user.id,
            country,
            residence,
            nativeLanguage,
            parsedTeachingLanguages,
            parsedCourseTypes,
            qualifications,
            teachingExperience,
            introVideoUrl,
            cv,
            phone,
            profilePictureUrl,
            commitmentAccepted === 'true' || commitmentAccepted === true ? 1 : 0,
            parsedAvailableHours,
            pricePerLesson,
            trialLessonPrice,
            timezone,
            paymentMethod
          ]
        );
      }

      // Get teacher profile ID
      const teacherProfileId = existingApplications.length > 0
        ? existingApplications[0].id
        : result.insertId;

      // Parse course types and insert into teacher_categories
      const selectedCategories = JSON.parse(parsedCourseTypes);
      console.log('Selected categories:', selectedCategories);

      for (const categoryId of selectedCategories) {
        await connection.execute(
          'INSERT INTO teacher_categories (teacher_profile_id, category_id) VALUES (?, ?)',
          [teacherProfileId, categoryId]
        );
      }

      // Parse teaching languages and insert into teacher_languages
      const selectedLanguages = JSON.parse(parsedTeachingLanguages);
      console.log('Selected languages:', selectedLanguages);

      // Delete existing teacher languages
      await connection.execute(
        'DELETE FROM teacher_languages WHERE teacher_profile_id = ?',
        [teacherProfileId]
      );

      // Insert new teaching languages
      for (const languageId of selectedLanguages) {
        await connection.execute(
          'INSERT INTO teacher_languages (teacher_profile_id, language_id) VALUES (?, ?)',
          [teacherProfileId, languageId]
        );
      }

      // If this is an application update from an approved teacher, update user role to teacher
      if (isApplicationUpdate && wasApproved) {
        await connection.execute(
          'UPDATE users SET role = "teacher" WHERE id = ?',
          [req.user.id]
        );
        console.log('Updated user role to teacher for application update');
      }

      // Commit transaction
      await connection.commit();

      // Get the updated application
      const [updatedApp] = await connection.execute(
        'SELECT * FROM teacher_profiles WHERE user_id = ?',
        [req.user.id]
      );

      // Emit status update through socket
      const statusUpdate = {
        status: 'pending',
        message: getStatusMessage('pending')
      };

      // Get io instance and emit status update
      const io = req.app.get('io');
      if (io) {
        io.to(`teacher_${req.user.id}`).emit('application_status', statusUpdate);
        console.log(`Emitting status update to teacher_${req.user.id}:`, statusUpdate);
      }

      // Actualizar el rol del usuario según el caso
      if (req.user.role === 'new_teacher') {
        // Si es un profesor nuevo, mantener el rol como "new_teacher" hasta que sea aprobado
        await connection.execute(
          'UPDATE users SET role = "new_teacher" WHERE id = ?',
          [req.user.id]
        );
        console.log('Maintained user role as new_teacher');
      } else if (!req.user.role || req.user.role === '') {
        // Si el rol está vacío, establecer como "new_teacher"
        await connection.execute(
          'UPDATE users SET role = "new_teacher" WHERE id = ?',
          [req.user.id]
        );
        console.log('Set empty role to new_teacher');
      } else if (isApplicationUpdate && req.user.role === 'platform_teacher') {
        // Si es una actualización de aplicación y el usuario es un profesor de plataforma, cambiar a "teacher" (pending approval)
        await connection.execute(
          'UPDATE users SET role = "teacher" WHERE id = ?',
          [req.user.id]
        );
        console.log('Changed platform_teacher role to teacher for application update');
      } else {
        // Para cualquier otro caso, mantener el rol actual
        console.log('Keeping current user role:', req.user.role);
      }

      // Customize message based on whether this is an update or new application
      const message = isApplicationUpdate && wasApproved
        ? 'Application updated successfully and pending approval'
        : 'Application submitted successfully';

      res.json({
        success: true,
        message: message,
        application: updatedApp[0],
        ...statusUpdate
      });
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error submitting application:', error);
    res.status(500).json({
      success: false,
      message: 'Error submitting application'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

const getApplicationStatus = async (req, res) => {
  try {
    console.log('Checking application status for user:', req.user.id, 'with role:', req.user.role);

    const [rows] = await db.pool.execute(
      'SELECT * FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );

    // Si no hay aplicación y el usuario es un profesor nuevo, devolver 'new' con status 200
    if (rows.length === 0) {
      console.log('No application found for user:', req.user.id);
      return res.status(200).json({
        status: 'new',
        message: getStatusMessage('new')
      });
    }

    const application = rows[0];
    console.log('Found application with status:', application.status);

    // Get io instance
    const io = req.app.get('io');

    // Emit to specific teacher's room
    if (io) {
      console.log(`Emitting status update to teacher_${req.user.id}:`, {
        status: application.status,
        message: getStatusMessage(application.status)
      });

      io.to(`teacher_${req.user.id}`).emit('application_status', {
        status: application.status,
        message: getStatusMessage(application.status)
      });
    }

    // Devolver el estado actual de la aplicación
    res.json({
      status: application.status,
      message: getStatusMessage(application.status)
    });

  } catch (err) {
    console.error('Error checking application status:', err);
    res.status(500).json({ message: 'Error checking application status' });
  }
};

const getPendingApplications = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);
    const [rows] = await connection.execute(
      `SELECT tp.*, u.full_name, u.email
       FROM teacher_profiles tp
       JOIN users u ON tp.user_id = u.id
       WHERE tp.status = 'pending'`
    );

    res.json(rows);
  } catch (error) {
    console.error('Error getting pending applications:', error);
    res.status(500).json({ message: 'Error getting pending applications' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

const updateApplicationStatus = async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  if (!['approved', 'rejected'].includes(status)) {
    return res.status(400).json({ message: 'Invalid status' });
  }

  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Update application status
    await connection.execute(
      'UPDATE teacher_profiles SET status = ? WHERE id = ?',
      [status, id]
    );

    // If approved, update user role to platform_teacher
    if (status === 'approved') {
      const [application] = await connection.execute(
        'SELECT user_id FROM teacher_profiles WHERE id = ?',
        [id]
      );

      if (application.length > 0) {
        await connection.execute(
          'UPDATE users SET role = "platform_teacher" WHERE id = ?',
          [application[0].user_id]
        );
      }
    }

    // Get the updated application
    const [updatedApp] = await connection.execute(
      'SELECT * FROM teacher_profiles WHERE id = ?',
      [id]
    );

    if (!updatedApp[0]) {
      return res.status(404).json({ message: 'Application not found' });
    }

    // Get io instance
    const io = req.app.get('io');

    // Emit to specific teacher's room
    if (io) {
      console.log(`Emitting status update to teacher_${updatedApp[0].user_id}:`, {
        status: status,
        message: getStatusMessage(status)
      });

      io.to(`teacher_${updatedApp[0].user_id}`).emit('application_status', {
        status: status,
        message: getStatusMessage(status)
      });

      // Also emit to admin room to update their view
      io.to('admin_room').emit('application_status_changed', {
        applicationId: id,
        status: status,
        message: getStatusMessage(status)
      });
    }

    res.json({
      status: status,
      message: getStatusMessage(status)
    });
  } catch (error) {
    console.error('Error updating application status:', error);
    res.status(500).json({ message: 'Error updating application status' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

const getProfile = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Get user and profile data in a single query
    const [profiles] = await connection.execute(
      `SELECT
        u.full_name, u.email, u.role, u.gender,
        tp.phone, tp.country, tp.residence, tp.native_language,
        tp.teaching_languages,
        GROUP_CONCAT(DISTINCT c.name) as course_types,
        tp.qualifications, tp.teaching_experience,
        tp.available_hours, tp.price_per_lesson, tp.trial_lesson_price,
        tp.timezone, tp.payment_method, tp.status,
        tp.intro_video_url, tp.cv, tp.profile_picture_url,
        tp.commitment_accepted
      FROM users u
      LEFT JOIN teacher_profiles tp ON u.id = tp.user_id
      LEFT JOIN teacher_categories tc ON tp.id = tc.teacher_profile_id
      LEFT JOIN categories c ON tc.category_id = c.id
      WHERE u.id = ?
      GROUP BY u.id`,
      [req.user.id]
    );

    if (profiles.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    const profile = profiles[0];

    // Parse JSON fields
    const formattedProfile = {
      phone: profile.phone || '',
      country: profile.country || '',
      residence: profile.residence || '',
      native_language: profile.native_language || '',
      teaching_languages: profile.teaching_languages ? JSON.parse(profile.teaching_languages) : [],
      course_types: profile.course_types ? profile.course_types.split(',') : [],
      qualifications: profile.qualifications || '',
      teaching_experience: parseInt(profile.teaching_experience) || 0,
      available_hours: profile.available_hours ? JSON.parse(profile.available_hours) : {},
      price_per_lesson: parseFloat(profile.price_per_lesson) || 0.00,
      trial_lesson_price: parseFloat(profile.trial_lesson_price) || 0.00,
      timezone: profile.timezone || '',
      payment_method: profile.payment_method || '',
      status: profile.status || 'pending',
      commitment_accepted: profile.commitment_accepted === 1 || profile.commitment_accepted === true,
      intro_video_url: profile.intro_video_url || '',
      cv: profile.cv || '',
      profile_picture_url: profile.profile_picture_url || ''
    };

    return res.json({
      success: true,
      user: {
        full_name: profile.full_name,
        email: profile.email,
        role: profile.role,
        gender: profile.gender
      },
      profile: formattedProfile
    });

  } catch (error) {
    console.error('Get teacher profile error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting teacher profile'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

const updateProfile = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    const {
      phone,
      country,
      residence,
      native_language,
      teaching_languages,
      course_types,
      qualifications,
      teaching_experience,
      available_hours,
      price_per_lesson,
      trial_lesson_price,
      timezone,
      payment_method,
      intro_video_url,
      cv,
      updateApplication
    } = req.body;

    // Check if profile exists
    const [existingProfiles] = await connection.execute(
      'SELECT id, status FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );

    // Convert undefined values to empty strings or default values based on schema
    const profileData = {
      phone: phone || '',
      country: country || '',
      residence: residence || '',
      native_language: native_language || '',
      teaching_languages: JSON.stringify(teaching_languages || []),
      course_types: JSON.stringify(course_types || []),
      qualifications: qualifications || '',
      teaching_experience: parseInt(teaching_experience) || 0,
      available_hours: JSON.stringify(available_hours || {}),
      price_per_lesson: parseFloat(price_per_lesson) || 0.00,
      trial_lesson_price: parseFloat(trial_lesson_price) || 0.00,
      timezone: timezone || '',
      payment_method: payment_method || '',
      intro_video_url: intro_video_url || '',
      cv: cv || ''
    };

    console.log('Profile data to be saved:', profileData);

    // Check if this is an application update (which requires admin approval)
    const isApplicationUpdate = updateApplication === true;

    // If this is an application update, set status to pending and update user role
    if (isApplicationUpdate && existingProfiles.length > 0 && existingProfiles[0].status === 'approved') {
      // Begin transaction
      await connection.beginTransaction();

      try {
        // Update profile with pending status
        await connection.execute(
          `UPDATE teacher_profiles SET
            phone = ?,
            country = ?,
            residence = ?,
            native_language = ?,
            teaching_languages = ?,
            course_types = ?,
            qualifications = ?,
            teaching_experience = ?,
            available_hours = ?,
            price_per_lesson = ?,
            trial_lesson_price = ?,
            timezone = ?,
            payment_method = ?,
            intro_video_url = ?,
            cv = ?,
            status = 'pending'
          WHERE user_id = ?`,
          [
            profileData.phone,
            profileData.country,
            profileData.residence,
            profileData.native_language,
            profileData.teaching_languages,
            profileData.course_types,
            profileData.qualifications,
            profileData.teaching_experience,
            profileData.available_hours,
            profileData.price_per_lesson,
            profileData.trial_lesson_price,
            profileData.timezone,
            profileData.payment_method,
            profileData.intro_video_url,
            profileData.cv,
            req.user.id
          ]
        );

        // Update user role to teacher (from platform_teacher)
        await connection.execute(
          'UPDATE users SET role = "teacher" WHERE id = ?',
          [req.user.id]
        );

        // Delete existing teacher categories
        await connection.execute(
          'DELETE FROM teacher_categories WHERE teacher_profile_id = ?',
          [existingProfiles[0].id]
        );

        // Parse course types and insert into teacher_categories
        const selectedCategories = JSON.parse(profileData.course_types);
        console.log('Selected categories:', selectedCategories);

        for (const categoryId of selectedCategories) {
          await connection.execute(
            'INSERT INTO teacher_categories (teacher_profile_id, category_id) VALUES (?, ?)',
            [existingProfiles[0].id, categoryId]
          );
        }

        // Delete existing teacher languages
        await connection.execute(
          'DELETE FROM teacher_languages WHERE teacher_profile_id = ?',
          [existingProfiles[0].id]
        );

        // Parse teaching languages and insert into teacher_languages
        const selectedLanguages = JSON.parse(profileData.teaching_languages);
        console.log('Selected languages:', selectedLanguages);

        for (const languageId of selectedLanguages) {
          await connection.execute(
            'INSERT INTO teacher_languages (teacher_profile_id, language_id) VALUES (?, ?)',
            [existingProfiles[0].id, languageId]
          );
        }

        // Commit transaction
        await connection.commit();

        // Get io instance and emit status update
        const io = req.app.get('io');
        if (io) {
          const statusUpdate = {
            status: 'pending',
            message: getStatusMessage('pending')
          };

          io.to(`teacher_${req.user.id}`).emit('application_status', statusUpdate);
          console.log(`Emitting status update to teacher_${req.user.id}:`, statusUpdate);
        }

        return res.json({
          success: true,
          message: 'Teacher application updated successfully and pending approval',
          status: 'pending'
        });
      } catch (error) {
        // Rollback transaction on error
        await connection.rollback();
        throw error;
      }
    } else if (existingProfiles.length === 0) {
      // Create new profile
      await connection.execute(
        `INSERT INTO teacher_profiles (
          user_id, phone, country, residence, native_language,
          teaching_languages, course_types, qualifications,
          teaching_experience, available_hours, price_per_lesson,
          timezone, payment_method, intro_video_url, cv
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          req.user.id,
          profileData.phone,
          profileData.country,
          profileData.residence,
          profileData.native_language,
          profileData.teaching_languages,
          profileData.course_types,
          profileData.qualifications,
          profileData.teaching_experience,
          profileData.available_hours,
          profileData.price_per_lesson,
          profileData.timezone,
          profileData.payment_method,
          profileData.intro_video_url,
          profileData.cv
        ]
      );

      return res.json({
        success: true,
        message: 'Teacher profile created successfully'
      });
    } else {
      // Update existing profile without changing status
      await connection.execute(
        `UPDATE teacher_profiles SET
          phone = ?,
          country = ?,
          residence = ?,
          native_language = ?,
          teaching_languages = ?,
          course_types = ?,
          qualifications = ?,
          teaching_experience = ?,
          available_hours = ?,
          price_per_lesson = ?,
          timezone = ?,
          payment_method = ?,
          intro_video_url = ?,
          cv = ?
        WHERE user_id = ?`,
        [
          profileData.phone,
          profileData.country,
          profileData.residence,
          profileData.native_language,
          profileData.teaching_languages,
          profileData.course_types,
          profileData.qualifications,
          profileData.teaching_experience,
          profileData.available_hours,
          profileData.price_per_lesson,
          profileData.timezone,
          profileData.payment_method,
          profileData.intro_video_url,
          profileData.cv,
          req.user.id
        ]
      );

      return res.json({
        success: true,
        message: 'Teacher profile updated successfully'
      });
    }
  } catch (error) {
    console.error('Update teacher profile error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error updating teacher profile'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

const updateProfilePicture = async (req, res) => {
  console.log('Updating profile picture...');
  console.log('Request headers:', req.headers);
  console.log('Request user:', req.user);

  if (!req.user || !req.user.id) {
    console.error('No authenticated user found');
    return res.status(401).json({
      success: false,
      message: 'Not authenticated'
    });
  }

  const uploadSingle = upload.single('profilePicture');

  uploadSingle(req, res, async (err) => {
    console.log('Upload middleware executed');
    console.log('Request file:', req.file);
    console.log('Upload error:', err);

    if (err instanceof multer.MulterError) {
      console.error('Multer error:', err);
      return res.status(400).json({
        success: false,
        message: err.code === 'LIMIT_FILE_SIZE'
          ? 'File size cannot exceed 5MB'
          : 'File upload error: ' + err.message
      });
    } else if (err) {
      console.error('Upload error:', err);
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }

    if (!req.file) {
      console.error('No file uploaded');
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    try {
      const userId = req.user.id;
      console.log('User ID:', userId);
      const relativePath = `/uploads/profile-pictures/${req.file.filename}`;
      console.log('Relative path:', relativePath);

      // Get current profile picture
      const [currentProfile] = await db.pool.execute(
        'SELECT profile_picture_url FROM teacher_profiles WHERE user_id = ?',
        [userId]
      );
      console.log('Current profile:', currentProfile);

      // Delete old profile picture if it exists
      if (currentProfile[0]?.profile_picture_url) {
        // Remove leading slash and convert to absolute path
        const oldPath = currentProfile[0].profile_picture_url.replace(/^\//, '');
        const oldAbsolutePath = path.join(__dirname, '..', oldPath);
        console.log('Attempting to delete old profile picture at:', oldAbsolutePath);

        try {
          if (await fs.access(oldAbsolutePath).then(() => true).catch(() => false)) {
            await fs.unlink(oldAbsolutePath);
            console.log('Old profile picture deleted:', oldAbsolutePath);
          } else {
            console.log('Old profile picture not found:', oldAbsolutePath);
          }
        } catch (error) {
          console.error('Error deleting old profile picture:', error);
          // Continue even if old file deletion fails
        }
      }

      // Check if teacher profile exists
      const [existingProfile] = await db.pool.execute(
        'SELECT id FROM teacher_profiles WHERE user_id = ?',
        [userId]
      );
      console.log('Existing profile:', existingProfile);

      if (existingProfile.length > 0) {
        // Update existing profile
        await db.pool.execute(
          'UPDATE teacher_profiles SET profile_picture_url = ? WHERE user_id = ?',
          [relativePath, userId]
        );
        console.log('Profile picture updated for existing profile');
      } else {
        // Create new profile
        await db.pool.execute(
          'INSERT INTO teacher_profiles (user_id, profile_picture_url) VALUES (?, ?)',
          [userId, relativePath]
        );
        console.log('New profile created with profile picture');
      }

      res.json({
        success: true,
        message: 'Profile picture updated successfully',
        profilePictureUrl: relativePath
      });
    } catch (error) {
      console.error('Database error:', error);
      // Delete uploaded file if database operation fails
      if (req.file) {
        try {
          await fs.unlink(req.file.path);
          console.log('Cleaned up uploaded file after error:', req.file.path);
        } catch (unlinkError) {
          console.error('Error deleting uploaded file after failed update:', unlinkError);
        }
      }
      res.status(500).json({
        success: false,
        message: 'Error updating profile picture'
      });
    }
  });
};

const getCategories = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);
    const [categories] = await connection.execute(
      'SELECT id, name, description FROM categories'
    );
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ message: 'Error fetching categories' });
  } finally {
    if (connection) connection.end();
  }
};

const getLanguages = async (req, res) => {
  try {
    const [languages] = await db.pool.execute(
      'SELECT id, name FROM languages ORDER BY name'
    );
    res.json(languages);
  } catch (error) {
    console.error('Error fetching languages:', error);
    res.status(500).json({ message: 'Error fetching languages' });
  }
};

const uploadVideo = async (req, res) => {
  console.log('Uploading intro video...');
  console.log('Request user:', req.user);
  console.log('Request file:', req.file);

  if (!req.user || !req.user.id) {
    console.error('No authenticated user found');
    return res.status(401).json({
      success: false,
      message: 'Not authenticated'
    });
  }

  if (!req.file) {
    console.error('No video file uploaded');
    return res.status(400).json({
      success: false,
      message: 'No video file uploaded. Please make sure your video is between 1MB and 100MB in size and in MP4, WebM, or OGG format.'
    });
  }

  try {
    // Make sure the uploads directory exists
    const uploadsDir = path.join(__dirname, '..', 'uploads', 'videos');
    try {
      await fs.mkdir(uploadsDir, { recursive: true });
      console.log('Uploads directory created or already exists');
    } catch (mkdirError) {
      console.error('Error creating uploads directory:', mkdirError);
    }

    const userId = req.user.id;
    console.log('User ID:', userId);
    const relativePath = `/uploads/videos/${req.file.filename}`;
    console.log('Relative path:', relativePath);

    // Get current video URL from the database to check if we need to delete an old file
    const [currentProfile] = await db.pool.execute(
      'SELECT intro_video_url FROM teacher_profiles WHERE user_id = ?',
      [userId]
    );
    console.log('Current profile:', currentProfile);

    // If there's an existing video in the database, delete the old file
    if (currentProfile[0]?.intro_video_url && currentProfile[0].intro_video_url.startsWith('/uploads/')) {
      // Remove leading slash and convert to absolute path
      const oldPath = currentProfile[0].intro_video_url.replace(/^\//, '');
      const oldAbsolutePath = path.join(__dirname, '..', oldPath);
      console.log('Attempting to delete old video at:', oldAbsolutePath);

      try {
        try {
          await fs.access(oldAbsolutePath);
          // If we get here, the file exists
          await fs.unlink(oldAbsolutePath);
          console.log('Old video deleted:', oldAbsolutePath);
        } catch (accessError) {
          console.log('Old video not found:', oldAbsolutePath);
        }
      } catch (error) {
        console.error('Error deleting old video:', error);
        // Continue even if old file deletion fails
      }
    }

    // Important: We're NOT updating the database here anymore
    // The video URL will be saved with the rest of the form data when the form is submitted

    res.json({
      success: true,
      message: 'Video uploaded successfully',
      videoUrl: relativePath
    });
  } catch (error) {
    console.error('Error handling video upload:', error);
    // Delete uploaded file if operation fails
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
        console.log('Cleaned up uploaded file after error:', req.file.path);
      } catch (unlinkError) {
        console.error('Error deleting uploaded file after failed update:', unlinkError);
      }
    }
    res.status(500).json({
      success: false,
      message: 'Error uploading video: ' + error.message
    });
  }
};

const deleteVideo = async (req, res) => {
  console.log('Deleting intro video...');
  console.log('Request user:', req.user);

  if (!req.user || !req.user.id) {
    console.error('No authenticated user found');
    return res.status(401).json({
      success: false,
      message: 'Not authenticated'
    });
  }

  try {
    const userId = req.user.id;
    console.log('User ID:', userId);

    // Get video URL from request query or check localStorage on client side
    const videoUrl = req.query.videoUrl;

    if (videoUrl) {
      // If a specific video URL is provided, delete that file
      if (videoUrl.startsWith('/uploads/')) {
        // Remove leading slash and convert to absolute path
        const oldPath = videoUrl.replace(/^\//, '');
        const oldAbsolutePath = path.join(__dirname, '..', oldPath);
        console.log('Attempting to delete video at:', oldAbsolutePath);

        try {
          await fs.access(oldAbsolutePath);
          // If we get here, the file exists
          await fs.unlink(oldAbsolutePath);
          console.log('Video deleted:', oldAbsolutePath);
        } catch (accessError) {
          console.log('Video not found on disk:', oldAbsolutePath);
          // Continue even if file doesn't exist
        }
      }
    } else {
      // If no specific URL is provided, check if there's a temporary video in the database
      // Get current video URL from database
      const [currentProfile] = await db.pool.execute(
        'SELECT intro_video_url FROM teacher_profiles WHERE user_id = ?',
        [userId]
      );
      console.log('Current profile:', currentProfile);

      if (currentProfile && currentProfile.length > 0 && currentProfile[0].intro_video_url) {
        // Delete video file if it exists and is a local file (not a YouTube URL)
        if (currentProfile[0].intro_video_url.startsWith('/uploads/')) {
          // Remove leading slash and convert to absolute path
          const oldPath = currentProfile[0].intro_video_url.replace(/^\//, '');
          const oldAbsolutePath = path.join(__dirname, '..', oldPath);
          console.log('Attempting to delete video at:', oldAbsolutePath);

          try {
            await fs.access(oldAbsolutePath);
            // If we get here, the file exists
            await fs.unlink(oldAbsolutePath);
            console.log('Video deleted:', oldAbsolutePath);
          } catch (accessError) {
            console.log('Video not found on disk:', oldAbsolutePath);
            // Continue even if file doesn't exist
          }
        }
      }
    }

    // Note: We're NOT updating the database here anymore
    // The video URL will be removed when the form is submitted

    res.json({
      success: true,
      message: 'Video deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting video:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting video: ' + error.message
    });
  }
};

const updateAvailableHours = async (req, res) => {
  console.log('Updating available hours...');
  console.log('Request user:', req.user);
  console.log('Request body:', req.body);

  if (!req.user || !req.user.id) {
    console.error('No authenticated user found');
    return res.status(401).json({
      success: false,
      message: 'Not authenticated'
    });
  }

  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Get available hours from request body
    const { available_hours } = req.body;

    if (!available_hours) {
      return res.status(400).json({
        success: false,
        message: 'Available hours data is required'
      });
    }

    // Check if teacher profile exists
    const [existingProfile] = await connection.execute(
      'SELECT id FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );

    // Convert available hours to JSON string
    const availableHoursJson = JSON.stringify(available_hours);

    if (existingProfile.length > 0) {
      // Update existing profile
      await connection.execute(
        'UPDATE teacher_profiles SET available_hours = ? WHERE user_id = ?',
        [availableHoursJson, req.user.id]
      );
      console.log('Available hours updated for existing profile');
    } else {
      // Create new profile with available hours
      await connection.execute(
        'INSERT INTO teacher_profiles (user_id, available_hours) VALUES (?, ?)',
        [req.user.id, availableHoursJson]
      );
      console.log('New profile created with available hours');
    }

    res.json({
      success: true,
      message: 'Available hours updated successfully'
    });
  } catch (error) {
    console.error('Error updating available hours:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating available hours: ' + error.message
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

// Take break for specific time slot
const takeBreak = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Create table if it doesn't exist (new simplified structure)
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS teacher_weekly_breaks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_profile_id INT NOT NULL,
        datetime DATETIME NOT NULL COMMENT 'Break date and time in UTC',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_profile_id) REFERENCES teacher_profiles(id) ON DELETE CASCADE,
        INDEX idx_teacher_profile_datetime (teacher_profile_id, datetime),
        INDEX idx_datetime (datetime),
        UNIQUE KEY unique_teacher_break (teacher_profile_id, datetime)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    const { date, hour, minute } = req.body;
    const teacherId = req.user.id;

    // Validate input
    if (!date || hour === undefined || minute === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: date, hour, minute'
      });
    }

    // Get teacher profile to get timezone
    const [teacherProfile] = await connection.execute(
      'SELECT id, timezone FROM teacher_profiles WHERE user_id = ?',
      [teacherId]
    );

    if (teacherProfile.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher profile not found'
      });
    }

    const teacherProfileId = teacherProfile[0].id;
    const teacherTimezone = teacherProfile[0].timezone || 'UTC';

    // Convert teacher's local time to UTC
    // The date and time received are in teacher's timezone, convert to UTC

    // Get timezone offset in minutes (e.g., UTC+05:00 = +300 minutes from UTC)
    const timezoneMatch = teacherTimezone.match(/UTC([+-])(\d{2}):(\d{2})/);
    let offsetMinutes = 0;
    if (timezoneMatch) {
      const sign = timezoneMatch[1] === '+' ? 1 : -1;
      const hours = parseInt(timezoneMatch[2]);
      const minutes = parseInt(timezoneMatch[3]);
      offsetMinutes = sign * (hours * 60 + minutes);
    }

    // Convert time from teacher's timezone to UTC, but keep the original date
    // Calculate UTC time
    const teacherHour = hour;
    const teacherMinute = minute;

    // Convert to UTC time (subtract offset)
    let utcHour = teacherHour - Math.floor(offsetMinutes / 60);
    let utcMinute = teacherMinute - (offsetMinutes % 60);

    // Handle minute overflow/underflow
    if (utcMinute < 0) {
      utcMinute += 60;
      utcHour -= 1;
    } else if (utcMinute >= 60) {
      utcMinute -= 60;
      utcHour += 1;
    }

    // Create UTC datetime properly handling timezone conversion
    let utcDateTime = new Date(`${date}T00:00:00Z`);

    // Add the converted time
    let finalHour = utcHour;
    let dayOffset = 0;

    // Handle hour overflow/underflow
    if (finalHour < 0) {
      dayOffset = -1;
      finalHour = finalHour + 24;
    } else if (finalHour >= 24) {
      dayOffset = 1;
      finalHour = finalHour - 24;
    }

    // Set the time and adjust date if needed
    utcDateTime.setUTCHours(finalHour, utcMinute, 0, 0);
    if (dayOffset !== 0) {
      utcDateTime.setUTCDate(utcDateTime.getUTCDate() + dayOffset);
    }

    console.log('\n🔍 TAKE_BREAK DEBUG:');
    console.log('📊 Original input:', { date, hour, minute });
    console.log('🌍 Teacher timezone:', teacherTimezone);
    console.log('⏰ Offset minutes:', offsetMinutes);
    console.log('🏠 Teacher time:', `${date} ${hour}:${minute}`);
    console.log('🌍 Final UTC DateTime:', utcDateTime.toISOString());
    console.log('💾 Saving to DB:', utcDateTime.toISOString().slice(0, 19).replace('T', ' '));

    // Check if break already exists (using datetime only)
    const [existingBreak] = await connection.execute(
      'SELECT id FROM teacher_weekly_breaks WHERE teacher_profile_id = ? AND datetime = ?',
      [teacherProfileId, utcDateTime.toISOString().slice(0, 19).replace('T', ' ')]
    );

    if (existingBreak.length > 0) {
      console.log('❌ Break already exists!');
      return res.status(400).json({
        success: false,
        message: 'Break already exists for this time slot'
      });
    }

    // Insert new break (using datetime only)
    const [insertResult] = await connection.execute(
      'INSERT INTO teacher_weekly_breaks (teacher_profile_id, datetime) VALUES (?, ?)',
      [teacherProfileId, utcDateTime.toISOString().slice(0, 19).replace('T', ' ')]
    );

    console.log('✅ Break saved successfully with ID:', insertResult.insertId);

    res.json({
      success: true,
      message: 'Break taken successfully'
    });

  } catch (error) {
    console.error('Error taking break:', error);
    res.status(500).json({
      success: false,
      message: 'Error taking break'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Get weekly breaks for teacher
const getWeeklyBreaks = async (req, res) => {
  let connection;
  try {
    console.log('\n🚀 GET_WEEKLY_BREAKS CALLED');

    connection = await mysql.createConnection(config);

    const { start_date, end_date } = req.query;
    const teacherId = req.user.id;

    // Get teacher profile ID and timezone
    const [teacherProfile] = await connection.execute(
      'SELECT id, timezone FROM teacher_profiles WHERE user_id = ?',
      [teacherId]
    );

    if (teacherProfile.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher profile not found'
      });
    }

    const teacherProfileId = teacherProfile[0].id;
    const teacherTimezone = teacherProfile[0].timezone || 'UTC';

    // Calculate date range for query
    let weekStartDate, weekEndDate;

    if (start_date && end_date) {
      // Use provided date range
      weekStartDate = `${start_date} 00:00:00`;
      weekEndDate = `${end_date} 23:59:59`;
    } else {
      // Default to current week (Monday to Sunday)
      const today = new Date();
      const currentWeekStart = new Date(today);
      currentWeekStart.setDate(today.getDate() - today.getDay() + 1); // Monday
      currentWeekStart.setHours(0, 0, 0, 0);

      const currentWeekEnd = new Date(currentWeekStart);
      currentWeekEnd.setDate(currentWeekStart.getDate() + 6); // Sunday
      currentWeekEnd.setHours(23, 59, 59, 999);

      weekStartDate = currentWeekStart.toISOString().slice(0, 19).replace('T', ' ');
      weekEndDate = currentWeekEnd.toISOString().slice(0, 19).replace('T', ' ');
    }

    console.log('\n🔍 DATE RANGE:');
    console.log('📅 Start date:', weekStartDate);
    console.log('📅 End date:', weekEndDate);
    console.log('🌍 Teacher timezone:', teacherTimezone);

    // Get weekly breaks for the current week using datetime range
    const [breaks] = await connection.execute(
      'SELECT datetime FROM teacher_weekly_breaks WHERE teacher_profile_id = ? AND datetime >= ? AND datetime <= ?',
      [teacherProfileId, weekStartDate, weekEndDate]
    );


    // Send breaks as UTC datetime objects for frontend to handle timezone conversion
    console.log('\n🔍 GET_WEEKLY_BREAKS DEBUG:');
    console.log('📊 Raw breaks from DB:', breaks);
    console.log('🌍 Teacher timezone:', teacherTimezone);

    // Send breaks as UTC datetime objects for frontend to handle timezone conversion
    const breakData = breaks.map((breakSlot, index) => {
      try {
        console.log(`\n📅 PROCESSING BREAK ${index + 1}:`);
        console.log('  📊 Raw from DB:', breakSlot.datetime);

        return {
          datetime: breakSlot.datetime // Send UTC datetime to frontend
        };
      } catch (error) {
        console.error('❌ Error processing break slot:', breakSlot, error);
        return null;
      }
    }).filter(item => item !== null);

    console.log('\n🎯 FINAL BREAK DATA:', breakData);
    console.log('📤 SENDING TO CLIENT:', { success: true, data: breakData });

    res.json({
      success: true,
      data: breakData
    });

  } catch (error) {
    console.error('Error fetching weekly breaks:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching weekly breaks'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Función para cambiar el rol del usuario de "platform_teacher" a "new_teacher"
const changeRoleToNew = async (req, res) => {
  console.log('Changing user role to new_teacher...');
  console.log('Request user:', req.user);

  if (!req.user || !req.user.id) {
    console.error('No authenticated user found');
    return res.status(401).json({
      success: false,
      message: 'Not authenticated'
    });
  }

  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Iniciar transacción
    await connection.beginTransaction();

    try {
      // Verificar que el usuario sea un profesor de plataforma
      const [users] = await connection.execute(
        'SELECT role FROM users WHERE id = ?',
        [req.user.id]
      );

      if (users.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const user = users[0];
      console.log('Current user role:', user.role);

      // Obtener el ID del perfil del profesor
      const [profiles] = await connection.execute(
        'SELECT id FROM teacher_profiles WHERE user_id = ?',
        [req.user.id]
      );

      if (profiles.length > 0) {
        const profileId = profiles[0].id;

        // Eliminar registros relacionados en teacher_categories
        await connection.execute(
          'DELETE FROM teacher_categories WHERE teacher_profile_id = ?',
          [profileId]
        );
        console.log('Deleted teacher categories');

        // Eliminar registros relacionados en teacher_languages
        await connection.execute(
          'DELETE FROM teacher_languages WHERE teacher_profile_id = ?',
          [profileId]
        );
        console.log('Deleted teacher languages');

        // Eliminar el perfil del profesor
        await connection.execute(
          'DELETE FROM teacher_profiles WHERE id = ?',
          [profileId]
        );
        console.log('Deleted teacher profile');
      }

      // Cambiar el rol del usuario a "new_teacher"
      await connection.execute(
        'UPDATE users SET role = "new_teacher" WHERE id = ?',
        [req.user.id]
      );
      console.log('User role changed to new_teacher');

      // Confirmar transacción
      await connection.commit();

      return res.json({
        success: true,
        message: 'User role changed to new_teacher and profile data deleted'
      });
    } catch (error) {
      // Revertir transacción en caso de error
      await connection.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error changing user role:', error);
    return res.status(500).json({
      success: false,
      message: 'Error changing user role: ' + error.message
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

// Controlador para editar la aplicación de un profesor
const editApplication = async (req, res) => {
  console.log('Editing teacher application...');

  // Configurar el middleware de carga de archivos para manejar múltiples campos
  const uploadFields = upload.fields([
    { name: 'profilePicture', maxCount: 1 },
    { name: 'commitmentDocument', maxCount: 1 }
  ]);

  uploadFields(req, res, async (err) => {
    if (err) {
      console.error('Error uploading files:', err);
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }

    console.log('Auth user:', req.user);
    console.log('Request body:', req.body);
    console.log('Request files:', req.files);

    let connection;
    try {
      connection = await mysql.createConnection(config);
      await connection.beginTransaction();

    // Verificar si el profesor ya tiene un perfil
    const [existingProfiles] = await connection.execute(
      'SELECT * FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );

    if (existingProfiles.length === 0) {
      await connection.rollback();
      return res.status(404).json({
        success: false,
        message: 'No se encontró un perfil de profesor para editar'
      });
    }

    // Extraer datos del formulario
    const {
      country,
      residence,
      nativeLanguage,
      teachingLanguages,
      courseTypes,
      qualifications,
      teachingExperience,
      cv,
      phone,
      pricePerLesson,
      timezone,
      paymentMethod,
      availableHours,
      useDefaultFemalePicture
    } = req.body;

    // Procesar datos JSON
    let parsedTeachingLanguages = [];
    let parsedCourseTypes = [];
    let parsedAvailableHours = {};

    try {
      // Procesar idiomas de enseñanza
      if (teachingLanguages) {
        if (typeof teachingLanguages === 'string') {
          parsedTeachingLanguages = JSON.parse(teachingLanguages);
        } else if (Array.isArray(teachingLanguages)) {
          parsedTeachingLanguages = teachingLanguages;
        }
      }
      console.log('Parsed teaching languages:', parsedTeachingLanguages);
    } catch (e) {
      console.error('Error parsing teaching languages:', e);
    }

    try {
      // Procesar tipos de cursos
      if (courseTypes) {
        if (typeof courseTypes === 'string') {
          parsedCourseTypes = JSON.parse(courseTypes);
        } else if (Array.isArray(courseTypes)) {
          parsedCourseTypes = courseTypes;
        }
      }
      console.log('Parsed course types:', parsedCourseTypes);
    } catch (e) {
      console.error('Error parsing course types:', e);
    }

    try {
      // Procesar horas disponibles
      if (availableHours) {
        if (typeof availableHours === 'string') {
          parsedAvailableHours = JSON.parse(availableHours);
        } else if (typeof availableHours === 'object') {
          parsedAvailableHours = availableHours;
        }
      }
      console.log('Parsed available hours:', parsedAvailableHours);
    } catch (e) {
      console.error('Error parsing available hours:', e);
    }

    // Manejar la URL de la imagen de perfil
    let profilePictureUrl = existingProfiles[0].profile_picture_url;
    let commitmentAccepted = req.body.commitmentAccepted === 'true' || req.body.commitmentAccepted === true;

    // Procesar los archivos cargados
    if (req.files) {
      // Procesar imagen de perfil
      if (req.files.profilePicture && req.files.profilePicture.length > 0) {
        profilePictureUrl = `/uploads/profile-pictures/${req.files.profilePicture[0].filename}`;
      }
    } else if (req.file) {
      // Para compatibilidad con versiones anteriores
      profilePictureUrl = `/uploads/profile-pictures/${req.file.filename}`;
    }



    console.log('Commitment accepted:', commitmentAccepted);

    // Manejar la URL del video de introducción
    let introVideoUrl = req.body.introVideoUrl || existingProfiles[0].intro_video_url;

    // Actualizar el perfil del profesor
    await connection.execute(
      `UPDATE teacher_profiles SET
        country = ?,
        residence = ?,
        native_language = ?,
        teaching_languages = ?,
        course_types = ?,
        qualifications = ?,
        teaching_experience = ?,
        intro_video_url = ?,
        cv = ?,
        phone = ?,
        profile_picture_url = ?,
        commitment_accepted = ?,
        available_hours = ?,
        price_per_lesson = ?,
        timezone = ?,
        payment_method = ?,
        status = 'pending'
      WHERE user_id = ?`,
      [
        country,
        residence,
        nativeLanguage,
        JSON.stringify(parsedTeachingLanguages),
        JSON.stringify(parsedCourseTypes),
        qualifications,
        teachingExperience,
        introVideoUrl,
        cv,
        phone,
        profilePictureUrl,
        commitmentAccepted,
        JSON.stringify(parsedAvailableHours),
        pricePerLesson,
        timezone,
        paymentMethod,
        req.user.id
      ]
    );

    // Actualizar el rol del usuario según el caso
    if (!req.user.role || req.user.role === '') {
      // Si el rol está vacío, establecer el rol a "new_teacher"
      await connection.execute(
        'UPDATE users SET role = "new_teacher" WHERE id = ?',
        [req.user.id]
      );
      console.log('Set empty role to new_teacher');
    } else if (req.user.role === 'platform_teacher') {
      // Si es un profesor de plataforma, mantener el rol
      await connection.execute(
        'UPDATE users SET role = "platform_teacher" WHERE id = ?',
        [req.user.id]
      );
      console.log('Maintained platform_teacher role');
    } else {
      // Para cualquier otro caso, establecer el rol a "new_teacher"
      await connection.execute(
        'UPDATE users SET role = "new_teacher" WHERE id = ?',
        [req.user.id]
      );
      console.log('Updated role to new_teacher');
    }

    await connection.commit();

    // Obtener el perfil actualizado
    const [updatedProfile] = await connection.execute(
      'SELECT * FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );

    // Emitir actualización de estado a través de socket
    const statusUpdate = {
      status: 'pending',
      message: getStatusMessage('pending')
    };

    const io = req.app.get('io');
    if (io) {
      io.to(`teacher_${req.user.id}`).emit('application_status', statusUpdate);
    }

    res.json({
      success: true,
      message: 'Aplicación actualizada correctamente y pendiente de aprobación',
      profile: updatedProfile[0],
      ...statusUpdate
    });
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      console.error('Error editing teacher application:', error);
      res.status(500).json({
        success: false,
        message: 'Error al editar la aplicación del profesor'
      });
    } finally {
      if (connection) {
        connection.end();
      }
    }
  });
};

const updateTeachingInfo = async (req, res) => {
  console.log('Updating teaching info...');
  console.log('Request user:', req.user);
  console.log('Request body:', req.body);

  if (!req.user || !req.user.id) {
    console.error('No authenticated user found');
    return res.status(401).json({
      success: false,
      message: 'Not authenticated'
    });
  }

  let connection;
  try {
    connection = await mysql.createConnection(config);

    const {
      phone,
      native_language,
      teaching_languages,
      course_types,
      price_per_lesson,
      trial_lesson_price,
      timezone
    } = req.body;

    // Convert arrays to JSON strings if needed
    const parsedTeachingLanguages = Array.isArray(teaching_languages)
      ? JSON.stringify(teaching_languages)
      : teaching_languages;

    const parsedCourseTypes = Array.isArray(course_types)
      ? JSON.stringify(course_types)
      : course_types;

    // Get teacher profile ID
    const [profileResult] = await connection.execute(
      'SELECT id FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );

    if (profileResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher profile not found'
      });
    }

    const profileId = profileResult[0].id;

    // Begin transaction
    await connection.beginTransaction();

    try {
      // Update teacher profile
      await connection.execute(
        `UPDATE teacher_profiles SET
          phone = ?,
          native_language = ?,
          teaching_languages = ?,
          course_types = ?,
          price_per_lesson = ?,
          trial_lesson_price = ?,
          timezone = ?
        WHERE user_id = ?`,
        [
          phone,
          native_language,
          parsedTeachingLanguages,
          parsedCourseTypes,
          price_per_lesson,
          trial_lesson_price,
          timezone,
          req.user.id
        ]
      );

      // Update teacher_categories table
      // Delete existing categories
      await connection.execute(
        'DELETE FROM teacher_categories WHERE teacher_profile_id = ?',
        [profileId]
      );

      // Insert new categories
      if (Array.isArray(course_types) && course_types.length > 0) {
        for (const categoryId of course_types) {
          await connection.execute(
            'INSERT INTO teacher_categories (teacher_profile_id, category_id) VALUES (?, ?)',
            [profileId, categoryId]
          );
        }
      }

      // Update teacher_languages table
      // Delete existing languages
      await connection.execute(
        'DELETE FROM teacher_languages WHERE teacher_profile_id = ?',
        [profileId]
      );

      // Insert new languages
      if (Array.isArray(teaching_languages) && teaching_languages.length > 0) {
        for (const languageId of teaching_languages) {
          await connection.execute(
            'INSERT INTO teacher_languages (teacher_profile_id, language_id) VALUES (?, ?)',
            [profileId, languageId]
          );
        }
      }

      // Commit transaction
      await connection.commit();
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      throw error;
    }

    console.log('Successfully updated teaching info for user:', req.user.id);

    res.json({
      success: true,
      message: 'Teaching information updated successfully'
    });

  } catch (error) {
    console.error('Error updating teaching info:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating teaching information'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

module.exports = {
  submitApplication,
  editApplication,
  getApplicationStatus,
  getPendingApplications,
  updateApplicationStatus,
  getProfile,
  updateProfile,
  updateProfilePicture,
  uploadVideo,
  deleteVideo,
  getCategories,
  getLanguages,
  updateAvailableHours,
  updateTeachingInfo,
  changeRoleToNew,
  takeBreak,
  getWeeklyBreaks
};
