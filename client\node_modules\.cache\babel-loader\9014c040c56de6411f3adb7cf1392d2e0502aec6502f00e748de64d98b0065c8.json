{"ast": null, "code": "import { useCallback as n, useId as u } from \"react\";\nimport { stackMachines as p } from '../machines/stack-machine.js';\nimport { useSlice as f } from '../react-glue.js';\nimport { useIsoMorphicEffect as a } from './use-iso-morphic-effect.js';\nfunction I(o, s) {\n  let t = u(),\n    r = p.get(s),\n    [i, c] = f(r, n(e => [r.selectors.isTop(e, t), r.selectors.inStack(e, t)], [r, t]));\n  return a(() => {\n    if (o) return r.actions.push(t), () => r.actions.pop(t);\n  }, [r, o, t]), o ? c ? i : !0 : !1;\n}\nexport { I as useIsTopLayer };", "map": {"version": 3, "names": ["useCallback", "n", "useId", "u", "stackMachines", "p", "useSlice", "f", "useIsoMorphicEffect", "a", "I", "o", "s", "t", "r", "get", "i", "c", "e", "selectors", "isTop", "inStack", "actions", "push", "pop", "useIsTopLayer"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/use-is-top-layer.js"], "sourcesContent": ["import{useCallback as n,useId as u}from\"react\";import{stackMachines as p}from'../machines/stack-machine.js';import{useSlice as f}from'../react-glue.js';import{useIsoMorphicEffect as a}from'./use-iso-morphic-effect.js';function I(o,s){let t=u(),r=p.get(s),[i,c]=f(r,n(e=>[r.selectors.isTop(e,t),r.selectors.inStack(e,t)],[r,t]));return a(()=>{if(o)return r.actions.push(t),()=>r.actions.pop(t)},[r,o,t]),o?c?i:!0:!1}export{I as useIsTopLayer};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,EAACC,KAAK,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACV,CAAC,CAAC,CAAC;IAACW,CAAC,GAACT,CAAC,CAACU,GAAG,CAACH,CAAC,CAAC;IAAC,CAACI,CAAC,EAACC,CAAC,CAAC,GAACV,CAAC,CAACO,CAAC,EAACb,CAAC,CAACiB,CAAC,IAAE,CAACJ,CAAC,CAACK,SAAS,CAACC,KAAK,CAACF,CAAC,EAACL,CAAC,CAAC,EAACC,CAAC,CAACK,SAAS,CAACE,OAAO,CAACH,CAAC,EAACL,CAAC,CAAC,CAAC,EAAC,CAACC,CAAC,EAACD,CAAC,CAAC,CAAC,CAAC;EAAC,OAAOJ,CAAC,CAAC,MAAI;IAAC,IAAGE,CAAC,EAAC,OAAOG,CAAC,CAACQ,OAAO,CAACC,IAAI,CAACV,CAAC,CAAC,EAAC,MAAIC,CAAC,CAACQ,OAAO,CAACE,GAAG,CAACX,CAAC,CAAC;EAAA,CAAC,EAAC,CAACC,CAAC,EAACH,CAAC,EAACE,CAAC,CAAC,CAAC,EAACF,CAAC,GAACM,CAAC,GAACD,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC;AAAA;AAAC,SAAON,CAAC,IAAIe,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}