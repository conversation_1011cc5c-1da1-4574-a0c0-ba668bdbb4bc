{"ast": null, "code": "import { useRef as $jtQ6z$useRef } from \"react\";\n\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ /* eslint-disable rulesdir/pure-render */\nfunction $5a387cc49350e6db$export$722debc0e56fea39(value, isEqual) {\n  // Using a ref during render is ok here because it's only an optimization – both values are equivalent.\n  // If a render is thrown away, it'll still work the same no matter if the next render is the same or not.\n  let lastValue = (0, $jtQ6z$useRef)(null);\n  if (value && lastValue.current && isEqual(value, lastValue.current)) value = lastValue.current;\n  lastValue.current = value;\n  return value;\n}\nexport { $5a387cc49350e6db$export$722debc0e56fea39 as useDeepMemo };", "map": {"version": 3, "names": ["$5a387cc49350e6db$export$722debc0e56fea39", "value", "isEqual", "lastValue", "$jtQ6z$useRef", "current"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useDeepMemo.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/* eslint-disable rulesdir/pure-render */\n\nimport {useRef} from 'react';\n\nexport function useDeepMemo<T>(value: T, isEqual: (a: T, b: T) => boolean): T {\n  // Using a ref during render is ok here because it's only an optimization – both values are equivalent.\n  // If a render is thrown away, it'll still work the same no matter if the next render is the same or not.\n  let lastValue = useRef<T | null>(null);\n  if (value && lastValue.current && isEqual(value, lastValue.current)) {\n    value = lastValue.current;\n  }\n\n  lastValue.current = value;\n  return value;\n}\n"], "mappings": ";;AAAA;;;;;;;;;;GAAA,CAYA;AAIO,SAASA,0CAAeC,KAAQ,EAAEC,OAAgC;EACvE;EACA;EACA,IAAIC,SAAA,GAAY,IAAAC,aAAK,EAAY;EACjC,IAAIH,KAAA,IAASE,SAAA,CAAUE,OAAO,IAAIH,OAAA,CAAQD,KAAA,EAAOE,SAAA,CAAUE,OAAO,GAChEJ,KAAA,GAAQE,SAAA,CAAUE,OAAO;EAG3BF,SAAA,CAAUE,OAAO,GAAGJ,KAAA;EACpB,OAAOA,KAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}