{"ast": null, "code": "/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n'use strict';\n\nexports.match = matchQuery;\nexports.parse = parseQuery;\n\n// -----------------------------------------------------------------------------\n\nvar RE_MEDIA_QUERY = /(?:(only|not)?\\s*([^\\s\\(\\)]+)(?:\\s*and)?\\s*)?(.+)?/i,\n  RE_MQ_EXPRESSION = /\\(\\s*([^\\s\\:\\)]+)\\s*(?:\\:\\s*([^\\s\\)]+))?\\s*\\)/,\n  RE_MQ_FEATURE = /^(?:(min|max)-)?(.+)/,\n  RE_LENGTH_UNIT = /(em|rem|px|cm|mm|in|pt|pc)?$/,\n  RE_RESOLUTION_UNIT = /(dpi|dpcm|dppx)?$/;\nfunction matchQuery(mediaQuery, values) {\n  return parseQuery(mediaQuery).some(function (query) {\n    var inverse = query.inverse;\n\n    // Either the parsed or specified `type` is \"all\", or the types must be\n    // equal for a match.\n    var typeMatch = query.type === 'all' || values.type === query.type;\n\n    // Quit early when `type` doesn't match, but take \"not\" into account.\n    if (typeMatch && inverse || !(typeMatch || inverse)) {\n      return false;\n    }\n    var expressionsMatch = query.expressions.every(function (expression) {\n      var feature = expression.feature,\n        modifier = expression.modifier,\n        expValue = expression.value,\n        value = values[feature];\n\n      // Missing or falsy values don't match.\n      if (!value) {\n        return false;\n      }\n      switch (feature) {\n        case 'orientation':\n        case 'scan':\n          return value.toLowerCase() === expValue.toLowerCase();\n        case 'width':\n        case 'height':\n        case 'device-width':\n        case 'device-height':\n          expValue = toPx(expValue);\n          value = toPx(value);\n          break;\n        case 'resolution':\n          expValue = toDpi(expValue);\n          value = toDpi(value);\n          break;\n        case 'aspect-ratio':\n        case 'device-aspect-ratio':\n        case /* Deprecated */'device-pixel-ratio':\n          expValue = toDecimal(expValue);\n          value = toDecimal(value);\n          break;\n        case 'grid':\n        case 'color':\n        case 'color-index':\n        case 'monochrome':\n          expValue = parseInt(expValue, 10) || 1;\n          value = parseInt(value, 10) || 0;\n          break;\n      }\n      switch (modifier) {\n        case 'min':\n          return value >= expValue;\n        case 'max':\n          return value <= expValue;\n        default:\n          return value === expValue;\n      }\n    });\n    return expressionsMatch && !inverse || !expressionsMatch && inverse;\n  });\n}\nfunction parseQuery(mediaQuery) {\n  return mediaQuery.split(',').map(function (query) {\n    query = query.trim();\n    var captures = query.match(RE_MEDIA_QUERY),\n      modifier = captures[1],\n      type = captures[2],\n      expressions = captures[3] || '',\n      parsed = {};\n    parsed.inverse = !!modifier && modifier.toLowerCase() === 'not';\n    parsed.type = type ? type.toLowerCase() : 'all';\n\n    // Split expressions into a list.\n    expressions = expressions.match(/\\([^\\)]+\\)/g) || [];\n    parsed.expressions = expressions.map(function (expression) {\n      var captures = expression.match(RE_MQ_EXPRESSION),\n        feature = captures[1].toLowerCase().match(RE_MQ_FEATURE);\n      return {\n        modifier: feature[1],\n        feature: feature[2],\n        value: captures[2]\n      };\n    });\n    return parsed;\n  });\n}\n\n// -- Utilities ----------------------------------------------------------------\n\nfunction toDecimal(ratio) {\n  var decimal = Number(ratio),\n    numbers;\n  if (!decimal) {\n    numbers = ratio.match(/^(\\d+)\\s*\\/\\s*(\\d+)$/);\n    decimal = numbers[1] / numbers[2];\n  }\n  return decimal;\n}\nfunction toDpi(resolution) {\n  var value = parseFloat(resolution),\n    units = String(resolution).match(RE_RESOLUTION_UNIT)[1];\n  switch (units) {\n    case 'dpcm':\n      return value / 2.54;\n    case 'dppx':\n      return value * 96;\n    default:\n      return value;\n  }\n}\nfunction toPx(length) {\n  var value = parseFloat(length),\n    units = String(length).match(RE_LENGTH_UNIT)[1];\n  switch (units) {\n    case 'em':\n      return value * 16;\n    case 'rem':\n      return value * 16;\n    case 'cm':\n      return value * 96 / 2.54;\n    case 'mm':\n      return value * 96 / 2.54 / 10;\n    case 'in':\n      return value * 96;\n    case 'pt':\n      return value * 72;\n    case 'pc':\n      return value * 72 / 12;\n    default:\n      return value;\n  }\n}", "map": {"version": 3, "names": ["exports", "match", "matchQuery", "parse", "parse<PERSON><PERSON>y", "RE_MEDIA_QUERY", "RE_MQ_EXPRESSION", "RE_MQ_FEATURE", "RE_LENGTH_UNIT", "RE_RESOLUTION_UNIT", "mediaQuery", "values", "some", "query", "inverse", "typeMatch", "type", "expressionsMatch", "expressions", "every", "expression", "feature", "modifier", "expValue", "value", "toLowerCase", "toPx", "toDpi", "toDecimal", "parseInt", "split", "map", "trim", "captures", "parsed", "ratio", "decimal", "Number", "numbers", "resolution", "parseFloat", "units", "String", "length"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/css-mediaquery/index.js"], "sourcesContent": ["/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n'use strict';\n\nexports.match = matchQuery;\nexports.parse = parseQuery;\n\n// -----------------------------------------------------------------------------\n\nvar RE_MEDIA_QUERY     = /(?:(only|not)?\\s*([^\\s\\(\\)]+)(?:\\s*and)?\\s*)?(.+)?/i,\n    RE_MQ_EXPRESSION   = /\\(\\s*([^\\s\\:\\)]+)\\s*(?:\\:\\s*([^\\s\\)]+))?\\s*\\)/,\n    RE_MQ_FEATURE      = /^(?:(min|max)-)?(.+)/,\n    RE_LENGTH_UNIT     = /(em|rem|px|cm|mm|in|pt|pc)?$/,\n    RE_RESOLUTION_UNIT = /(dpi|dpcm|dppx)?$/;\n\nfunction matchQuery(mediaQuery, values) {\n    return parseQuery(mediaQuery).some(function (query) {\n        var inverse = query.inverse;\n\n        // Either the parsed or specified `type` is \"all\", or the types must be\n        // equal for a match.\n        var typeMatch = query.type === 'all' || values.type === query.type;\n\n        // Quit early when `type` doesn't match, but take \"not\" into account.\n        if ((typeMatch && inverse) || !(typeMatch || inverse)) {\n            return false;\n        }\n\n        var expressionsMatch = query.expressions.every(function (expression) {\n            var feature  = expression.feature,\n                modifier = expression.modifier,\n                expValue = expression.value,\n                value    = values[feature];\n\n            // Missing or falsy values don't match.\n            if (!value) { return false; }\n\n            switch (feature) {\n                case 'orientation':\n                case 'scan':\n                    return value.toLowerCase() === expValue.toLowerCase();\n\n                case 'width':\n                case 'height':\n                case 'device-width':\n                case 'device-height':\n                    expValue = toPx(expValue);\n                    value    = toPx(value);\n                    break;\n\n                case 'resolution':\n                    expValue = toDpi(expValue);\n                    value    = toDpi(value);\n                    break;\n\n                case 'aspect-ratio':\n                case 'device-aspect-ratio':\n                case /* Deprecated */ 'device-pixel-ratio':\n                    expValue = toDecimal(expValue);\n                    value    = toDecimal(value);\n                    break;\n\n                case 'grid':\n                case 'color':\n                case 'color-index':\n                case 'monochrome':\n                    expValue = parseInt(expValue, 10) || 1;\n                    value    = parseInt(value, 10) || 0;\n                    break;\n            }\n\n            switch (modifier) {\n                case 'min': return value >= expValue;\n                case 'max': return value <= expValue;\n                default   : return value === expValue;\n            }\n        });\n\n        return (expressionsMatch && !inverse) || (!expressionsMatch && inverse);\n    });\n}\n\nfunction parseQuery(mediaQuery) {\n    return mediaQuery.split(',').map(function (query) {\n        query = query.trim();\n\n        var captures    = query.match(RE_MEDIA_QUERY),\n            modifier    = captures[1],\n            type        = captures[2],\n            expressions = captures[3] || '',\n            parsed      = {};\n\n        parsed.inverse = !!modifier && modifier.toLowerCase() === 'not';\n        parsed.type    = type ? type.toLowerCase() : 'all';\n\n        // Split expressions into a list.\n        expressions = expressions.match(/\\([^\\)]+\\)/g) || [];\n\n        parsed.expressions = expressions.map(function (expression) {\n            var captures = expression.match(RE_MQ_EXPRESSION),\n                feature  = captures[1].toLowerCase().match(RE_MQ_FEATURE);\n\n            return {\n                modifier: feature[1],\n                feature : feature[2],\n                value   : captures[2]\n            };\n        });\n\n        return parsed;\n    });\n}\n\n// -- Utilities ----------------------------------------------------------------\n\nfunction toDecimal(ratio) {\n    var decimal = Number(ratio),\n        numbers;\n\n    if (!decimal) {\n        numbers = ratio.match(/^(\\d+)\\s*\\/\\s*(\\d+)$/);\n        decimal = numbers[1] / numbers[2];\n    }\n\n    return decimal;\n}\n\nfunction toDpi(resolution) {\n    var value = parseFloat(resolution),\n        units = String(resolution).match(RE_RESOLUTION_UNIT)[1];\n\n    switch (units) {\n        case 'dpcm': return value / 2.54;\n        case 'dppx': return value * 96;\n        default    : return value;\n    }\n}\n\nfunction toPx(length) {\n    var value = parseFloat(length),\n        units = String(length).match(RE_LENGTH_UNIT)[1];\n\n    switch (units) {\n        case 'em' : return value * 16;\n        case 'rem': return value * 16;\n        case 'cm' : return value * 96 / 2.54;\n        case 'mm' : return value * 96 / 2.54 / 10;\n        case 'in' : return value * 96;\n        case 'pt' : return value * 72;\n        case 'pc' : return value * 72 / 12;\n        default   : return value;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZA,OAAO,CAACC,KAAK,GAAGC,UAAU;AAC1BF,OAAO,CAACG,KAAK,GAAGC,UAAU;;AAE1B;;AAEA,IAAIC,cAAc,GAAO,qDAAqD;EAC1EC,gBAAgB,GAAK,+CAA+C;EACpEC,aAAa,GAAQ,sBAAsB;EAC3CC,cAAc,GAAO,8BAA8B;EACnDC,kBAAkB,GAAG,mBAAmB;AAE5C,SAASP,UAAUA,CAACQ,UAAU,EAAEC,MAAM,EAAE;EACpC,OAAOP,UAAU,CAACM,UAAU,CAAC,CAACE,IAAI,CAAC,UAAUC,KAAK,EAAE;IAChD,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;;IAE3B;IACA;IACA,IAAIC,SAAS,GAAGF,KAAK,CAACG,IAAI,KAAK,KAAK,IAAIL,MAAM,CAACK,IAAI,KAAKH,KAAK,CAACG,IAAI;;IAElE;IACA,IAAKD,SAAS,IAAID,OAAO,IAAK,EAAEC,SAAS,IAAID,OAAO,CAAC,EAAE;MACnD,OAAO,KAAK;IAChB;IAEA,IAAIG,gBAAgB,GAAGJ,KAAK,CAACK,WAAW,CAACC,KAAK,CAAC,UAAUC,UAAU,EAAE;MACjE,IAAIC,OAAO,GAAID,UAAU,CAACC,OAAO;QAC7BC,QAAQ,GAAGF,UAAU,CAACE,QAAQ;QAC9BC,QAAQ,GAAGH,UAAU,CAACI,KAAK;QAC3BA,KAAK,GAAMb,MAAM,CAACU,OAAO,CAAC;;MAE9B;MACA,IAAI,CAACG,KAAK,EAAE;QAAE,OAAO,KAAK;MAAE;MAE5B,QAAQH,OAAO;QACX,KAAK,aAAa;QAClB,KAAK,MAAM;UACP,OAAOG,KAAK,CAACC,WAAW,CAAC,CAAC,KAAKF,QAAQ,CAACE,WAAW,CAAC,CAAC;QAEzD,KAAK,OAAO;QACZ,KAAK,QAAQ;QACb,KAAK,cAAc;QACnB,KAAK,eAAe;UAChBF,QAAQ,GAAGG,IAAI,CAACH,QAAQ,CAAC;UACzBC,KAAK,GAAME,IAAI,CAACF,KAAK,CAAC;UACtB;QAEJ,KAAK,YAAY;UACbD,QAAQ,GAAGI,KAAK,CAACJ,QAAQ,CAAC;UAC1BC,KAAK,GAAMG,KAAK,CAACH,KAAK,CAAC;UACvB;QAEJ,KAAK,cAAc;QACnB,KAAK,qBAAqB;QAC1B,KAAK,gBAAiB,oBAAoB;UACtCD,QAAQ,GAAGK,SAAS,CAACL,QAAQ,CAAC;UAC9BC,KAAK,GAAMI,SAAS,CAACJ,KAAK,CAAC;UAC3B;QAEJ,KAAK,MAAM;QACX,KAAK,OAAO;QACZ,KAAK,aAAa;QAClB,KAAK,YAAY;UACbD,QAAQ,GAAGM,QAAQ,CAACN,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC;UACtCC,KAAK,GAAMK,QAAQ,CAACL,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC;UACnC;MACR;MAEA,QAAQF,QAAQ;QACZ,KAAK,KAAK;UAAE,OAAOE,KAAK,IAAID,QAAQ;QACpC,KAAK,KAAK;UAAE,OAAOC,KAAK,IAAID,QAAQ;QACpC;UAAY,OAAOC,KAAK,KAAKD,QAAQ;MACzC;IACJ,CAAC,CAAC;IAEF,OAAQN,gBAAgB,IAAI,CAACH,OAAO,IAAM,CAACG,gBAAgB,IAAIH,OAAQ;EAC3E,CAAC,CAAC;AACN;AAEA,SAASV,UAAUA,CAACM,UAAU,EAAE;EAC5B,OAAOA,UAAU,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAUlB,KAAK,EAAE;IAC9CA,KAAK,GAAGA,KAAK,CAACmB,IAAI,CAAC,CAAC;IAEpB,IAAIC,QAAQ,GAAMpB,KAAK,CAACZ,KAAK,CAACI,cAAc,CAAC;MACzCiB,QAAQ,GAAMW,QAAQ,CAAC,CAAC,CAAC;MACzBjB,IAAI,GAAUiB,QAAQ,CAAC,CAAC,CAAC;MACzBf,WAAW,GAAGe,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;MAC/BC,MAAM,GAAQ,CAAC,CAAC;IAEpBA,MAAM,CAACpB,OAAO,GAAG,CAAC,CAACQ,QAAQ,IAAIA,QAAQ,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK;IAC/DS,MAAM,CAAClB,IAAI,GAAMA,IAAI,GAAGA,IAAI,CAACS,WAAW,CAAC,CAAC,GAAG,KAAK;;IAElD;IACAP,WAAW,GAAGA,WAAW,CAACjB,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE;IAEpDiC,MAAM,CAAChB,WAAW,GAAGA,WAAW,CAACa,GAAG,CAAC,UAAUX,UAAU,EAAE;MACvD,IAAIa,QAAQ,GAAGb,UAAU,CAACnB,KAAK,CAACK,gBAAgB,CAAC;QAC7Ce,OAAO,GAAIY,QAAQ,CAAC,CAAC,CAAC,CAACR,WAAW,CAAC,CAAC,CAACxB,KAAK,CAACM,aAAa,CAAC;MAE7D,OAAO;QACHe,QAAQ,EAAED,OAAO,CAAC,CAAC,CAAC;QACpBA,OAAO,EAAGA,OAAO,CAAC,CAAC,CAAC;QACpBG,KAAK,EAAKS,QAAQ,CAAC,CAAC;MACxB,CAAC;IACL,CAAC,CAAC;IAEF,OAAOC,MAAM;EACjB,CAAC,CAAC;AACN;;AAEA;;AAEA,SAASN,SAASA,CAACO,KAAK,EAAE;EACtB,IAAIC,OAAO,GAAGC,MAAM,CAACF,KAAK,CAAC;IACvBG,OAAO;EAEX,IAAI,CAACF,OAAO,EAAE;IACVE,OAAO,GAAGH,KAAK,CAAClC,KAAK,CAAC,sBAAsB,CAAC;IAC7CmC,OAAO,GAAGE,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EACrC;EAEA,OAAOF,OAAO;AAClB;AAEA,SAAST,KAAKA,CAACY,UAAU,EAAE;EACvB,IAAIf,KAAK,GAAGgB,UAAU,CAACD,UAAU,CAAC;IAC9BE,KAAK,GAAGC,MAAM,CAACH,UAAU,CAAC,CAACtC,KAAK,CAACQ,kBAAkB,CAAC,CAAC,CAAC,CAAC;EAE3D,QAAQgC,KAAK;IACT,KAAK,MAAM;MAAE,OAAOjB,KAAK,GAAG,IAAI;IAChC,KAAK,MAAM;MAAE,OAAOA,KAAK,GAAG,EAAE;IAC9B;MAAa,OAAOA,KAAK;EAC7B;AACJ;AAEA,SAASE,IAAIA,CAACiB,MAAM,EAAE;EAClB,IAAInB,KAAK,GAAGgB,UAAU,CAACG,MAAM,CAAC;IAC1BF,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC1C,KAAK,CAACO,cAAc,CAAC,CAAC,CAAC,CAAC;EAEnD,QAAQiC,KAAK;IACT,KAAK,IAAI;MAAG,OAAOjB,KAAK,GAAG,EAAE;IAC7B,KAAK,KAAK;MAAE,OAAOA,KAAK,GAAG,EAAE;IAC7B,KAAK,IAAI;MAAG,OAAOA,KAAK,GAAG,EAAE,GAAG,IAAI;IACpC,KAAK,IAAI;MAAG,OAAOA,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE;IACzC,KAAK,IAAI;MAAG,OAAOA,KAAK,GAAG,EAAE;IAC7B,KAAK,IAAI;MAAG,OAAOA,KAAK,GAAG,EAAE;IAC7B,KAAK,IAAI;MAAG,OAAOA,KAAK,GAAG,EAAE,GAAG,EAAE;IAClC;MAAY,OAAOA,KAAK;EAC5B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}