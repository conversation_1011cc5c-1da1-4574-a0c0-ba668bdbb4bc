{"ast": null, "code": "const $431fbd86ca7dc216$export$b204af158042fbac = el => {\n  var _el_ownerDocument;\n  return (_el_ownerDocument = el === null || el === void 0 ? void 0 : el.ownerDocument) !== null && _el_ownerDocument !== void 0 ? _el_ownerDocument : document;\n};\nconst $431fbd86ca7dc216$export$f21a1ffae260145a = el => {\n  if (el && 'window' in el && el.window === el) return el;\n  const doc = $431fbd86ca7dc216$export$b204af158042fbac(el);\n  return doc.defaultView || window;\n};\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction $431fbd86ca7dc216$var$isNode(value) {\n  return value !== null && typeof value === 'object' && 'nodeType' in value && typeof value.nodeType === 'number';\n}\nfunction $431fbd86ca7dc216$export$af51f0f06c0f328a(node) {\n  return $431fbd86ca7dc216$var$isNode(node) && node.nodeType === Node.DOCUMENT_FRAGMENT_NODE && 'host' in node;\n}\nexport { $431fbd86ca7dc216$export$b204af158042fbac as getOwnerDocument, $431fbd86ca7dc216$export$f21a1ffae260145a as getOwnerWindow, $431fbd86ca7dc216$export$af51f0f06c0f328a as isShadowRoot };", "map": {"version": 3, "names": ["$431fbd86ca7dc216$export$b204af158042fbac", "el", "_el_ownerDocument", "ownerDocument", "document", "$431fbd86ca7dc216$export$f21a1ffae260145a", "window", "doc", "defaultView", "$431fbd86ca7dc216$var$isNode", "value", "nodeType", "$431fbd86ca7dc216$export$af51f0f06c0f328a", "node", "Node", "DOCUMENT_FRAGMENT_NODE"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\domHelpers.ts"], "sourcesContent": ["export const getOwnerDocument = (el: Element | null | undefined): Document => {\n  return el?.ownerDocument ?? document;\n};\n\nexport const getOwnerWindow = (\n  el: (Window & typeof global) | Element | null | undefined\n): Window & typeof global => {\n  if (el && 'window' in el && el.window === el) {\n    return el;\n  }\n\n  const doc = getOwnerDocument(el as Element | null | undefined);\n  return doc.defaultView || window;\n};\n\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction isNode(value: unknown): value is Node {\n  return value !== null &&\n    typeof value === 'object' &&\n    'nodeType' in value &&\n    typeof (value as Node).nodeType === 'number';\n}\n/**\n * Type guard that checks if a node is a ShadowRoot. Uses nodeType and host property checks to\n * distinguish ShadowRoot from other DocumentFragments.\n */\nexport function isShadowRoot(node: Node | null): node is ShadowRoot {\n  return isNode(node) &&\n    node.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n    'host' in node;\n}\n"], "mappings": "AAAO,MAAMA,yCAAA,GAAoBC,EAAA;MACxBC,iBAAA;EAAP,OAAO,CAAAA,iBAAA,GAAAD,EAAA,aAAAA,EAAA,uBAAAA,EAAA,CAAIE,aAAa,cAAjBD,iBAAA,cAAAA,iBAAA,GAAqBE,QAAA;AAC9B;AAEO,MAAMC,yCAAA,GACXJ,EAAA;EAEA,IAAIA,EAAA,IAAM,YAAYA,EAAA,IAAMA,EAAA,CAAGK,MAAM,KAAKL,EAAA,EACxC,OAAOA,EAAA;EAGT,MAAMM,GAAA,GAAMP,yCAAA,CAAiBC,EAAA;EAC7B,OAAOM,GAAA,CAAIC,WAAW,IAAIF,MAAA;AAC5B;AAEA;;;AAGA,SAASG,6BAAOC,KAAc;EAC5B,OAAOA,KAAA,KAAU,QACf,OAAOA,KAAA,KAAU,YACjB,cAAcA,KAAA,IACd,OAAOA,KAAC,CAAeC,QAAQ,KAAK;AACxC;AAKO,SAASC,0CAAaC,IAAiB;EAC5C,OAAOJ,4BAAA,CAAOI,IAAA,KACZA,IAAA,CAAKF,QAAQ,KAAKG,IAAA,CAAKC,sBAAsB,IAC7C,UAAUF,IAAA;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}