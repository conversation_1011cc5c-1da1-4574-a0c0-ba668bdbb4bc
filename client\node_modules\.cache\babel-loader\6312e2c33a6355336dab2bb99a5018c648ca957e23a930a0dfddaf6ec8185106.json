{"ast": null, "code": "import { useEffectEvent as $8ae05eaa5c114e9c$export$7f54fc3180508a52 } from \"./useEffectEvent.mjs\";\nimport { useRef as $8rM3G$useRef, useEffect as $8rM3G$useEffect } from \"react\";\n\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $99facab73266f662$export$5add1d006293d136(ref, initialValue, onReset) {\n  let resetValue = (0, $8rM3G$useRef)(initialValue);\n  let handleReset = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)(() => {\n    if (onReset) onReset(resetValue.current);\n  });\n  (0, $8rM3G$useEffect)(() => {\n    var _ref_current;\n    let form = ref === null || ref === void 0 ? void 0 : (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.form;\n    form === null || form === void 0 ? void 0 : form.addEventListener('reset', handleReset);\n    return () => {\n      form === null || form === void 0 ? void 0 : form.removeEventListener('reset', handleReset);\n    };\n  }, [ref, handleReset]);\n}\nexport { $99facab73266f662$export$5add1d006293d136 as useFormReset };", "map": {"version": 3, "names": ["$99facab73266f662$export$5add1d006293d136", "ref", "initialValue", "onReset", "resetValue", "$8rM3G$useRef", "handleReset", "$8ae05eaa5c114e9c$export$7f54fc3180508a52", "current", "$8rM3G$useEffect", "_ref_current", "form", "addEventListener", "removeEventListener"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useFormReset.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect, useRef} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useFormReset<T>(\n  ref: RefObject<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null> | undefined,\n  initialValue: T,\n  onReset: (value: T) => void\n): void {\n  let resetValue = useRef(initialValue);\n  let handleReset = useEffectEvent(() => {\n    if (onReset) {\n      onReset(resetValue.current);\n    }\n  });\n\n  useEffect(() => {\n    let form = ref?.current?.form;\n    form?.addEventListener('reset', handleReset);\n    return () => {\n      form?.removeEventListener('reset', handleReset);\n    };\n  }, [ref, handleReset]);\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAgBO,SAASA,0CACdC,GAA6F,EAC7FC,YAAe,EACfC,OAA2B;EAE3B,IAAIC,UAAA,GAAa,IAAAC,aAAK,EAAEH,YAAA;EACxB,IAAII,WAAA,GAAc,IAAAC,yCAAa,EAAE;IAC/B,IAAIJ,OAAA,EACFA,OAAA,CAAQC,UAAA,CAAWI,OAAO;EAE9B;EAEA,IAAAC,gBAAQ,EAAE;QACGC,YAAA;IAAX,IAAIC,IAAA,GAAOV,GAAA,aAAAA,GAAA,wBAAAS,YAAA,GAAAT,GAAA,CAAKO,OAAO,cAAZE,YAAA,uBAAAA,YAAA,CAAcC,IAAI;IAC7BA,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAMC,gBAAgB,CAAC,SAASN,WAAA;IAChC,OAAO;MACLK,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAME,mBAAmB,CAAC,SAASP,WAAA;IACrC;EACF,GAAG,CAACL,GAAA,EAAKK,WAAA,CAAY;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}