{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{Box,Typography,Paper,Button,Chip,useTheme,alpha,CircularProgress,Tooltip,Dialog,DialogTitle,DialogContent,DialogActions,IconButton}from'@mui/material';import{Visibility as ViewIcon,Cancel as CancelIcon,AccessTime as AccessTimeIcon,RadioButtonUnchecked as RadioButtonUncheckedIcon,CheckCircle as CheckCircleIcon,SelfImprovement as RestIcon}from'@mui/icons-material';import{format,addDays}from'date-fns';import{ar,enUS}from'date-fns/locale';import moment from'moment-timezone';import{formatDateInStudentTimezone}from'../utils/timezone';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const WeeklyBookingsTable=_ref=>{let{bookings,loading=false,currentWeekStart,daysOfWeek,onViewDetails,onCancelBooking,studentProfile,formatBookingTime,getStatusColor,isTeacherView=false,availableHours=null,onTakeBreak=null,weeklyBreaks=[]}=_ref;const{t,i18n}=useTranslation();const theme=useTheme();const isRtl=i18n.language==='ar';// State for break dialog\nconst[breakDialogOpen,setBreakDialogOpen]=React.useState(false);const[selectedBreakSlot,setSelectedBreakSlot]=React.useState(null);// Handle take break\nconst handleTakeBreak=(day,timeSlot,isFirstHalf)=>{const dayIndex=daysOfWeekData.findIndex(d=>d.key===day);const date=addDays(currentWeekStart,dayIndex);const slotMinute=isFirstHalf?0:30;setSelectedBreakSlot({day,date,hour:timeSlot.hour,minute:slotMinute,isFirstHalf,timeSlot});setBreakDialogOpen(true);};const confirmTakeBreak=()=>{if(selectedBreakSlot&&onTakeBreak){onTakeBreak(selectedBreakSlot);}setBreakDialogOpen(false);setSelectedBreakSlot(null);};// Check if a half-hour slot is in the past (considering teacher's timezone)\nconst isHalfHourSlotInPast=(day,timeSlot,isFirstHalf)=>{try{const dayIndex=daysOfWeekData.findIndex(d=>d.key===day);const date=addDays(currentWeekStart,dayIndex);const slotMinute=isFirstHalf?0:30;// Create the slot start datetime\nconst slotStartTime=new Date(date);slotStartTime.setHours(timeSlot.hour,slotMinute,0,0);// Get current time in teacher's timezone using the same method as the table\nlet currentTime;if(studentProfile&&studentProfile.timezone){// Use teacher's timezone\nconst currentTimeStr=formatDateInStudentTimezone(new Date().toISOString(),studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');currentTime=new Date(currentTimeStr);}else{// Fallback to browser local time\ncurrentTime=new Date();}// A slot is considered \"past\" if its start time has already passed\n// This means the current half-hour slot is also considered past\nreturn slotStartTime<=currentTime;}catch(error){console.error('Error checking if slot is in past:',error);return false;}};// Check if a specific half-hour slot is available\nconst isHalfHourSlotAvailable=(day,timeSlot,isFirstHalf)=>{if(!availableHours||!isTeacherView)return false;const dayKey=day.toLowerCase();const daySlots=availableHours[dayKey];if(!daySlots||!Array.isArray(daySlots))return false;// Check if this slot is taken as a break\nconst dayIndex=daysOfWeekData.findIndex(d=>d.key===day);const date=addDays(currentWeekStart,dayIndex);const slotMinute=isFirstHalf?0:30;// Check if this slot matches any break from the backend\n// Backend now sends UTC datetimes, so we need to convert them to teacher's timezone\n// and check if they match this slot\nconsole.log('🔍 FRONTEND CHECK: Checking if slot is break');console.log('  📅 Day:',day,'DayIndex:',dayIndex);console.log('  📅 CurrentWeekStart:',currentWeekStart===null||currentWeekStart===void 0?void 0:currentWeekStart.toISOString());console.log('  📅 Calculated Date:',date.toISOString());console.log('  📅 Slot Date String:',date.getFullYear()+'-'+String(date.getMonth()+1).padStart(2,'0')+'-'+String(date.getDate()).padStart(2,'0'));console.log('  ⏰ Slot Hour:',timeSlot.hour,'Minute:',slotMinute);console.log('  🔍 Available breaks:',weeklyBreaks.length,weeklyBreaks);// Check if any break matches this slot\nconst isBreakSlot=weeklyBreaks.some(breakData=>{if(typeof breakData==='string'){// Old format - ignore for now\nreturn false;}if(breakData&&breakData.datetime){// New format - UTC datetime from backend\nconst breakUtcDateTime=new Date(breakData.datetime);// Convert break UTC time to teacher's timezone\nif(studentProfile&&studentProfile.timezone){const teacherTimezone=studentProfile.timezone;const breakInTeacherTz=formatDateInStudentTimezone(breakData.datetime,teacherTimezone,'YYYY-MM-DD HH:mm:ss');const[breakDate,breakTime]=breakInTeacherTz.split(' ');const[breakHour,breakMinute]=breakTime.split(':').map(Number);// Get the current slot date in the same format (YYYY-MM-DD)\nconst slotDateStr=date.getFullYear()+'-'+String(date.getMonth()+1).padStart(2,'0')+'-'+String(date.getDate()).padStart(2,'0');const isDateMatch=breakDate===slotDateStr;const isTimeMatch=breakHour===timeSlot.hour&&breakMinute===slotMinute;console.log('    🔍 Checking break:',breakData.datetime);console.log('      🌍 UTC:',breakUtcDateTime.toISOString());console.log('      🏠 Teacher TZ:',breakInTeacherTz);console.log('      🔍 Break Date:',breakDate,'Break Time:',`${breakHour}:${breakMinute}`);console.log('      🔍 Slot Date:',slotDateStr,'Slot Time:',`${timeSlot.hour}:${slotMinute}`);console.log('      📅 Date match:',isDateMatch,`(${breakDate} vs ${slotDateStr})`);console.log('      ⏰ Time match:',isTimeMatch,`(${breakHour}:${breakMinute} vs ${timeSlot.hour}:${slotMinute})`);return isDateMatch&&isTimeMatch;}}return false;});console.log('  ❓ Is break slot?',isBreakSlot);if(isBreakSlot){console.log('  🚫 SLOT IS BREAK - hiding slot');return false;// Slot is taken as break\n}// Calculate the exact 30-minute slot we're checking (using existing slotMinute)\nconst slotStartMinutes=timeSlot.hour*60+slotMinute;const slotEndMinutes=slotStartMinutes+30;// Handle edge case for last half hour of the day (23:30-24:00)\nif(timeSlot.hour===23&&!isFirstHalf){// For 23:30-24:00, check if any available slot covers 23:30\nreturn daySlots.some(slot=>{const[startTime,endTime]=slot.split('-');const[startHour,startMinute]=startTime.split(':').map(Number);let[endHour,endMinute]=endTime.split(':').map(Number);// Handle 24:00 or 00:00 as end time\nif(endHour===0||endHour===24){endHour=24;endMinute=0;}const availableStartMinutes=startHour*60+startMinute;const availableEndMinutes=endHour*60+endMinute;// Check if 23:30 is covered\nreturn slotStartMinutes>=availableStartMinutes&&slotStartMinutes<availableEndMinutes;});}// Check if this specific 30-minute slot is in the available hours\nreturn daySlots.some(slot=>{const[startTime,endTime]=slot.split('-');const[startHour,startMinute]=startTime.split(':').map(Number);let[endHour,endMinute]=endTime.split(':').map(Number);// Handle 24:00 or 00:00 as end time (next day)\nif(endHour===0){endHour=24;endMinute=0;}const availableStartMinutes=startHour*60+startMinute;const availableEndMinutes=endHour*60+endMinute;// Check if the 30-minute slot fits within the available time range\nreturn slotStartMinutes>=availableStartMinutes&&slotEndMinutes<=availableEndMinutes;});};// Define time slots - full hours from 00:00 to 23:00\nconst timeSlots=[];for(let hour=0;hour<24;hour++){const startTime=`${hour.toString().padStart(2,'0')}:00`;const midTime=`${hour.toString().padStart(2,'0')}:30`;const endTime=hour<23?`${(hour+1).toString().padStart(2,'0')}:00`:'00:00';timeSlots.push({key:`${startTime}-${endTime}`,label:startTime,midLabel:midTime,hour,minute:0,// Include both half-hour slots for this hour\nfirstHalf:`${startTime}-${midTime}`,secondHalf:hour<23?`${midTime}-${endTime}`:'23:30-00:00'});}// Define days of the week\nconst defaultDaysOfWeek=['monday','tuesday','wednesday','thursday','friday','saturday','sunday'];const daysOfWeekData=daysOfWeek?daysOfWeek.map(day=>({key:day,label:t(`days.${day}`)})):defaultDaysOfWeek.map(day=>({key:day,label:t(`days.${day}`)}));// Get abbreviated day names for mobile\nconst getAbbreviatedDayName=dayKey=>{const abbreviations={sunday:t('days.sundayShort')||'Sun',monday:t('days.mondayShort')||'Mon',tuesday:t('days.tuesdayShort')||'Tue',wednesday:t('days.wednesdayShort')||'Wed',thursday:t('days.thursdayShort')||'Thu',friday:t('days.fridayShort')||'Fri',saturday:t('days.saturdayShort')||'Sat'};return abbreviations[dayKey]||dayKey.substring(0,3);};// Get meeting status based on current time\nconst getMeetingStatus=booking=>{const meetingStartTime=new Date(booking.datetime);const meetingEndTime=new Date(booking.datetime);meetingEndTime.setMinutes(meetingEndTime.getMinutes()+parseInt(booking.duration));const now=new Date();if(booking.status==='cancelled'){return'cancelled';}if(now>=meetingStartTime&&now<meetingEndTime){return'ongoing';}if(now>=meetingEndTime){return'completed';}return'scheduled';};// Get background color based on meeting status\nconst getBookingBackgroundColor=booking=>{const status=getMeetingStatus(booking);switch(status){case'ongoing':return{background:`linear-gradient(135deg, ${alpha(theme.palette.success.main,0.85)} 0%, ${alpha(theme.palette.success.dark,0.95)} 100%)`,border:`2px solid ${alpha(theme.palette.success.main,0.3)}`,hoverBackground:`linear-gradient(135deg, ${alpha(theme.palette.success.main,0.95)} 0%, ${alpha(theme.palette.success.dark,1)} 100%)`};case'completed':return{background:`linear-gradient(135deg, ${alpha(theme.palette.grey[500],0.85)} 0%, ${alpha(theme.palette.grey[700],0.95)} 100%)`,border:`2px solid ${alpha(theme.palette.grey[500],0.3)}`,hoverBackground:`linear-gradient(135deg, ${alpha(theme.palette.grey[500],0.95)} 0%, ${alpha(theme.palette.grey[700],1)} 100%)`};case'cancelled':return{background:`linear-gradient(135deg, ${alpha(theme.palette.error.main,0.85)} 0%, ${alpha(theme.palette.error.dark,0.95)} 100%)`,border:`2px solid ${alpha(theme.palette.error.main,0.3)}`,hoverBackground:`linear-gradient(135deg, ${alpha(theme.palette.error.main,0.95)} 0%, ${alpha(theme.palette.error.dark,1)} 100%)`};default:// scheduled\nreturn{background:`linear-gradient(135deg, ${alpha(theme.palette.info.main,0.85)} 0%, ${alpha(theme.palette.info.dark,0.95)} 100%)`,border:`2px solid ${alpha(theme.palette.info.main,0.3)}`,hoverBackground:`linear-gradient(135deg, ${alpha(theme.palette.info.main,0.95)} 0%, ${alpha(theme.palette.info.dark,1)} 100%)`};}};// Find all bookings for specific day and time slot\nconst findBookingsForSlot=(day,timeSlot)=>{if(!bookings||!currentWeekStart)return[];const dayIndex=daysOfWeekData.findIndex(d=>d.key===day);const date=addDays(currentWeekStart,dayIndex);const dateStr=format(date,'yyyy-MM-dd');return bookings.filter(booking=>{// Get booking date and time in student timezone\nlet bookingDate,bookingHour,bookingMinute;if(studentProfile&&studentProfile.timezone){// Use student timezone for both date and time\nconst formattedDateTime=formatDateInStudentTimezone(booking.datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');const[datePart,timePart]=formattedDateTime.split(' ');bookingDate=datePart;const[hourStr,minuteStr]=timePart.split(':');bookingHour=parseInt(hourStr);bookingMinute=parseInt(minuteStr);}else{// Fallback to browser local time\nconst bookingDateTime=new Date(booking.datetime);bookingDate=format(bookingDateTime,'yyyy-MM-dd');bookingHour=bookingDateTime.getHours();bookingMinute=bookingDateTime.getMinutes();}// Check if booking matches this date\nconst isDateMatch=bookingDate===dateStr;if(!isDateMatch)return false;// Calculate booking start and end times in minutes from midnight\nconst duration=parseInt(booking.duration)||25;const bookingStartMinutes=bookingHour*60+bookingMinute;const bookingEndMinutes=bookingStartMinutes+duration;// Calculate slot start and end times in minutes from midnight\nconst slotStartMinutes=timeSlot.hour*60;const slotEndMinutes=slotStartMinutes+60;// For 50-minute lessons, show in both starting hour and next hour if it spans\nif(duration===50){// Check if booking starts in this slot\nconst startsInThisSlot=bookingStartMinutes>=slotStartMinutes&&bookingStartMinutes<slotEndMinutes;// Check if booking extends into this slot from previous hour\nconst extendsIntoThisSlot=bookingStartMinutes<slotStartMinutes&&bookingEndMinutes>slotStartMinutes;return startsInThisSlot||extendsIntoThisSlot;}else{// For 25-minute lessons, show in the exact 30-minute slot\nconst slotMiddle=slotStartMinutes+30;// Check if booking is in first half (00-30) or second half (30-60)\nif(bookingStartMinutes>=slotStartMinutes&&bookingStartMinutes<slotMiddle){// First half booking\nreturn bookingStartMinutes>=slotStartMinutes&&bookingStartMinutes<slotMiddle;}else if(bookingStartMinutes>=slotMiddle&&bookingStartMinutes<slotEndMinutes){// Second half booking\nreturn bookingStartMinutes>=slotMiddle&&bookingStartMinutes<slotEndMinutes;}}return false;});};if(loading){return/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',my:4},children:/*#__PURE__*/_jsx(CircularProgress,{})});}if(!bookings||bookings.length===0){return/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:3,mb:4,borderRadius:2,textAlign:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:t('bookings.noBookings')})});}return/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{mb:4,borderRadius:2},children:[/*#__PURE__*/_jsx(Box,{sx:{background:`linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,p:3,color:'white',display:'flex',justifyContent:'space-between',alignItems:'center',flexWrap:'wrap',gap:2},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:'bold',mb:1},children:[\"\\uD83D\\uDCC5 \",t('bookings.weeklyTitle')]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{opacity:0.9},children:t('bookings.weeklyDescription')})]})}),/*#__PURE__*/_jsxs(Box,{sx:{overflow:'auto'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'60px repeat(7, minmax(80px, 1fr))',sm:'80px repeat(7, minmax(100px, 1fr))',md:'120px repeat(7, minmax(120px, 1fr))'},bgcolor:alpha(theme.palette.primary.main,0.05),borderBottom:`2px solid ${alpha(theme.palette.primary.main,0.1)}`,position:'sticky',top:0,zIndex:10},children:[/*#__PURE__*/_jsx(Box,{sx:{p:{xs:1.5,sm:2,md:2.5},minHeight:{xs:'60px',sm:'75px',md:'90px'},display:'flex',alignItems:'center',justifyContent:'center',borderRight:`1px solid ${alpha(theme.palette.primary.main,0.1)}`},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{fontWeight:'bold',color:theme.palette.text.secondary,fontSize:{xs:'0.8rem',sm:'0.9rem',md:'1rem'}},children:[\"\\u23F0 \",t('teacher.time')]})}),daysOfWeekData.map((day,index)=>{// Calculate the date for this day\nconst dayDate=currentWeekStart?addDays(currentWeekStart,index):new Date();return/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:1.5,sm:2,md:2.5},minHeight:{xs:'60px',sm:'75px',md:'90px'},textAlign:'center',borderRight:`1px solid ${alpha(theme.palette.primary.main,0.1)}`,'&:last-child':{borderRight:'none'},display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:'bold',color:theme.palette.primary.main,fontSize:{xs:'0.8rem',sm:'1rem',md:'1.2rem'},lineHeight:1.2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{display:{xs:'none',md:'block'}},children:day.label}),/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{display:{xs:'none',sm:'block',md:'none'}},children:day.label.length>6?day.label.substring(0,6):day.label}),/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{display:{xs:'block',sm:'none'}},children:getAbbreviatedDayName(day.key)})]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:'block',color:theme.palette.text.secondary,fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'},mt:0.5},children:format(dayDate,'MMM d',{locale:isRtl?ar:enUS})})]},day.key);})]}),/*#__PURE__*/_jsx(Box,{children:timeSlots.map((timeSlot,index)=>{const isHourStart=timeSlot.minute===0;return/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'60px repeat(7, minmax(80px, 1fr))',sm:'80px repeat(7, minmax(100px, 1fr))',md:'120px repeat(7, minmax(120px, 1fr))'},borderBottom:`1px solid ${alpha(theme.palette.divider,isHourStart?0.3:0.1)}`,bgcolor:index%4<2?alpha(theme.palette.primary.main,0.02):'transparent','&:hover':{bgcolor:alpha(theme.palette.primary.main,0.05)}},children:[/*#__PURE__*/_jsx(Box,{sx:{p:{xs:0.5,sm:1,md:1.5},display:'grid',placeItems:'center',borderRight:`1px solid ${alpha(theme.palette.primary.main,0.1)}`,bgcolor:isHourStart?alpha(theme.palette.primary.main,0.05):'transparent',position:'relative'},children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',gap:0.5,position:'absolute',left:'50%',transform:'translateX(-50%)',top:timeSlot.minute===0?'-8px':'calc(50% + 20px)'},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontWeight:'bold',color:theme.palette.primary.main,fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'}},children:timeSlot.label})})}),daysOfWeekData.map(day=>{const allBookingsInSlot=findBookingsForSlot(day.key,timeSlot);// Filter out extended bookings from previous hour\n// Filter out extended bookings from previous hour\nconst bookingsInSlot=allBookingsInSlot.filter(booking=>{// Get booking start time to determine if it's extended from previous hour\nlet bookingHour=0;let bookingMinute=0;if(studentProfile&&studentProfile.timezone){const formattedDateTime=formatDateInStudentTimezone(booking.datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');const[,timePart]=formattedDateTime.split(' ');const[hourStr,minuteStr]=timePart.split(':');bookingHour=parseInt(hourStr);bookingMinute=parseInt(minuteStr);}else{const bookingDate=new Date(booking.datetime);bookingHour=bookingDate.getHours();bookingMinute=bookingDate.getMinutes();}const duration=parseInt(booking.duration)||25;const bookingStartMinutes=bookingHour*60+bookingMinute;const currentSlotStartMinutes=timeSlot.hour*60;// Only show bookings that start in this slot or earlier in the same hour\n// Don't show bookings that extend from previous hour\nif(duration===50){const extendsIntoThisSlot=bookingStartMinutes<currentSlotStartMinutes;return!extendsIntoThisSlot;// Filter out extended bookings\n}return true;// Show all 25-minute bookings\n});// Deduplicate overlapping bookings that share the exact same start time (datetime)\n// If a non-cancelled booking and a cancelled booking overlap, keep the non-cancelled one\n// so the student sees the active reservation instead of the cancelled one.\nconst visibleBookingsMap=bookingsInSlot.reduce((acc,curr)=>{const key=curr.datetime;// ISO string coming from backend\nconst existing=acc[key];if(!existing){acc[key]=curr;// first occurrence\n}else{// If the existing booking is cancelled and the new one is not, replace it\nif(existing.status==='cancelled'&&curr.status!=='cancelled'){acc[key]=curr;}// If both have same non-cancelled status keep the earlier inserted one\n// otherwise, leave as is.\n}return acc;},{});const visibleBookings=Object.values(visibleBookingsMap);return/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:0.2,sm:0.4,md:0.6},minHeight:{xs:'80px',sm:'100px',md:'120px'},// Increased height for better content fit\ndisplay:'flex',alignItems:'center',justifyContent:'center',borderRight:`1px solid ${alpha(theme.palette.primary.main,0.1)}`,'&:last-child':{borderRight:'none'},position:'relative'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,right:0,height:'4px',bgcolor:theme.palette.primary.main,zIndex:0,borderRadius:'2px',boxShadow:'0 2px 4px rgba(0,0,0,0.1)',pointerEvents:'none'}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:'50%',left:0,right:0,height:'2px',bgcolor:alpha(theme.palette.divider,0.3),// More transparent\nzIndex:0,// Lower z-index to stay behind bookings\nborderRadius:'1px'}}),visibleBookings.length>0?/*#__PURE__*/_jsx(_Fragment,{children:visibleBookings.map((booking,bookingIndex)=>{const bookingColors=getBookingBackgroundColor(booking);const isFullLesson=parseInt(booking.duration)===50;// Get booking start time to determine position\nlet bookingMinute=0;let bookingHour=0;if(studentProfile&&studentProfile.timezone){const formattedDateTime=formatDateInStudentTimezone(booking.datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');const[,timePart]=formattedDateTime.split(' ');const[hourStr,minuteStr]=timePart.split(':');bookingHour=parseInt(hourStr);bookingMinute=parseInt(minuteStr);}else{const bookingDate=new Date(booking.datetime);bookingHour=bookingDate.getHours();bookingMinute=bookingDate.getMinutes();}// For full lessons, determine which half of this slot the booking occupies\n// For half lessons, show in correct half based on start time\nlet isFirstHalf=bookingMinute===0;// For full lessons, determine the visual representation\nif(isFullLesson){// Calculate if this booking spans into the next hour\nconst bookingStartMinutes=bookingHour*60+bookingMinute;const bookingEndMinutes=bookingStartMinutes+50;// 50-minute lesson\nconst currentSlotStartMinutes=timeSlot.hour*60;const currentSlotEndMinutes=currentSlotStartMinutes+60;// Check if booking starts in this slot\nconst startsInThisSlot=bookingStartMinutes>=currentSlotStartMinutes&&bookingStartMinutes<currentSlotEndMinutes;if(startsInThisSlot){// Booking starts in this slot\nconst extendsToNextHour=bookingEndMinutes>currentSlotEndMinutes;if(extendsToNextHour){// This is a cross-hour booking, show it spanning from current position to next hour\nisFirstHalf='spanning';// Special case for spanning bookings\n}else{// Regular full lesson within one hour - don't override first half bookings\nisFirstHalf=bookingMinute===0?null:'secondHalfFull';}}}return/*#__PURE__*/_jsxs(Box,{sx:{width:'90%',height:(()=>{if(isFirstHalf==='spanning'){// For cross-hour bookings, extend from second half to next hour\nreturn'100%';// Extend to cover both halves visually\n}if(isFirstHalf==='secondHalfFull'){// Full lesson starting from second half - only show in second half\nreturn'40%';}return isFullLesson?'95%':'40%';})(),position:'absolute',top:(()=>{if(isFirstHalf==='spanning'){// Start from the second half of current slot\nreturn'50%';}if(isFirstHalf==='secondHalfFull'){// Position in second half only\nreturn'55%';}return isFullLesson?'2.5%':isFirstHalf?'5%':'55%';})(),left:'5%',zIndex:(()=>{if(isFirstHalf==='spanning'){return 100;// Very high z-index for spanning bookings\n}return 10+bookingIndex;// Stacked z-index for multiple bookings\n})(),borderRadius:1,background:bookingColors.background,display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',gap:{xs:0.2,sm:0.3,md:0.4},p:{xs:0.2,sm:0.3,md:0.4},cursor:'pointer',border:bookingColors.border,boxShadow:'0 2px 8px rgba(0,0,0,0.1)','&:hover':{background:bookingColors.hoverBackground,transform:'translateY(-1px)',boxShadow:'0 4px 12px rgba(0,0,0,0.15)'},transition:'all 0.2s ease-in-out',overflow:'hidden'},onClick:()=>onViewDetails(booking),children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'white',fontWeight:'bold',fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.75rem'},textAlign:'center',lineHeight:1.1,overflow:'hidden',textOverflow:'ellipsis',display:'-webkit-box',WebkitLineClamp:1,WebkitBoxOrient:'vertical',maxWidth:'100%',textShadow:'0 1px 2px rgba(0,0,0,0.3)',mb:{xs:0.1,sm:0.2,md:0.2}},children:isTeacherView?booking.student_name:booking.teacher_name}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:{xs:0.2,sm:0.3,md:0.3}},children:[/*#__PURE__*/_jsx(Button,{size:\"small\",variant:\"contained\",onClick:e=>{e.stopPropagation();onViewDetails(booking);},sx:{minWidth:'auto',width:{xs:18,sm:22,md:24},height:{xs:16,sm:20,md:22},p:0,bgcolor:'rgba(255, 255, 255, 0.2)',color:'white',borderRadius:0.5,'&:hover':{bgcolor:'rgba(255, 255, 255, 0.3)'}},children:/*#__PURE__*/_jsx(ViewIcon,{sx:{fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'}}})}),booking.status==='scheduled'&&onCancelBooking&&!isTeacherView&&/*#__PURE__*/_jsx(Button,{size:\"small\",variant:\"contained\",onClick:e=>{e.stopPropagation();onCancelBooking(booking);},sx:{minWidth:'auto',width:{xs:18,sm:22,md:24},height:{xs:16,sm:20,md:22},p:0,bgcolor:'rgba(244, 67, 54, 0.8)',color:'white',borderRadius:0.5,'&:hover':{bgcolor:'rgba(244, 67, 54, 1)'}},children:/*#__PURE__*/_jsx(CancelIcon,{sx:{fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'}}})})]})]},`${booking.id}-${bookingIndex}`);})}):/*#__PURE__*/// Empty slot - show two halves for 30-minute slots\n_jsxs(Box,{sx:{width:'90%',height:{xs:'65px',sm:'85px',md:'105px'},borderRadius:1.5,border:`1px solid ${alpha(theme.palette.grey[300],0.5)}`,display:'flex',flexDirection:'column',overflow:'hidden',zIndex:1},children:[(()=>{const firstHalfAvailable=isHalfHourSlotAvailable(day.key,timeSlot,true);const firstHalfInPast=isHalfHourSlotInPast(day.key,timeSlot,true);return/*#__PURE__*/_jsx(Box,{sx:{flex:1,display:'flex',alignItems:'center',justifyContent:'center',borderBottom:`1px solid ${alpha(theme.palette.grey[300],0.3)}`,bgcolor:firstHalfInPast?alpha(theme.palette.grey[600],0.15):firstHalfAvailable?alpha(theme.palette.success.main,0.08):alpha(theme.palette.grey[100],0.3),transition:'all 0.2s ease'},children:firstHalfInPast?/*#__PURE__*/_jsx(Tooltip,{title:`${timeSlot.hour.toString().padStart(2,'0')}:00 - ${t('bookings.pastSlot','Past Time')}`,arrow:true,children:/*#__PURE__*/_jsx(RadioButtonUncheckedIcon,{sx:{color:theme.palette.grey[600],fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'}}})}):firstHalfAvailable?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(Tooltip,{title:`${timeSlot.hour.toString().padStart(2,'0')}:00 - ${t('bookings.availableSlot','Available')}`,arrow:true,children:/*#__PURE__*/_jsx(CheckCircleIcon,{sx:{color:theme.palette.success.main,fontSize:{xs:'0.9rem',sm:'1rem',md:'1.1rem'},cursor:'pointer'}})}),onTakeBreak&&/*#__PURE__*/_jsx(Tooltip,{title:t('bookings.takeBreak','Take Break'),arrow:true,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleTakeBreak(day.key,timeSlot,true),sx:{color:theme.palette.warning.main,fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'},padding:'2px','&:hover':{bgcolor:alpha(theme.palette.warning.main,0.1)}},children:/*#__PURE__*/_jsx(RestIcon,{sx:{fontSize:'inherit'}})})})]}):/*#__PURE__*/_jsx(RadioButtonUncheckedIcon,{sx:{color:alpha(theme.palette.grey[400],0.5),fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'}}})});})(),(()=>{const secondHalfAvailable=isHalfHourSlotAvailable(day.key,timeSlot,false);const secondHalfInPast=isHalfHourSlotInPast(day.key,timeSlot,false);return/*#__PURE__*/_jsx(Box,{sx:{flex:1,display:'flex',alignItems:'center',justifyContent:'center',bgcolor:secondHalfInPast?alpha(theme.palette.grey[600],0.15):secondHalfAvailable?alpha(theme.palette.success.main,0.08):alpha(theme.palette.grey[100],0.3),transition:'all 0.2s ease'},children:secondHalfInPast?/*#__PURE__*/_jsx(Tooltip,{title:`${timeSlot.hour.toString().padStart(2,'0')}:30${timeSlot.hour===23?'-24:00':`-${(timeSlot.hour+1).toString().padStart(2,'0')}:00`} - ${t('bookings.pastSlot','Past Time')}`,arrow:true,children:/*#__PURE__*/_jsx(RadioButtonUncheckedIcon,{sx:{color:theme.palette.grey[600],fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'}}})}):secondHalfAvailable?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(Tooltip,{title:`${timeSlot.hour.toString().padStart(2,'0')}:30${timeSlot.hour===23?'-24:00':`-${(timeSlot.hour+1).toString().padStart(2,'0')}:00`} - ${t('bookings.availableSlot','Available')}`,arrow:true,children:/*#__PURE__*/_jsx(CheckCircleIcon,{sx:{color:theme.palette.success.main,fontSize:{xs:'0.9rem',sm:'1rem',md:'1.1rem'},cursor:'pointer'}})}),onTakeBreak&&/*#__PURE__*/_jsx(Tooltip,{title:t('bookings.takeBreak','Take Break'),arrow:true,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleTakeBreak(day.key,timeSlot,false),sx:{color:theme.palette.warning.main,fontSize:{xs:'0.6rem',sm:'0.7rem',md:'0.8rem'},padding:'2px','&:hover':{bgcolor:alpha(theme.palette.warning.main,0.1)}},children:/*#__PURE__*/_jsx(RestIcon,{sx:{fontSize:'inherit'}})})})]}):/*#__PURE__*/_jsx(RadioButtonUncheckedIcon,{sx:{color:alpha(theme.palette.grey[400],0.5),fontSize:{xs:'0.7rem',sm:'0.8rem',md:'0.9rem'}}})});})()]})]},`${day.key}-${timeSlot.key}`);})]},timeSlot.key);})})]}),/*#__PURE__*/_jsxs(Dialog,{open:breakDialogOpen,onClose:()=>setBreakDialogOpen(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsxs(DialogTitle,{sx:{textAlign:'center',pb:1},children:[/*#__PURE__*/_jsx(RestIcon,{sx:{fontSize:'2rem',color:'warning.main',mb:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",component:\"div\",children:t('bookings.takeBreakTitle','أخذ هذا الوقت راحة')})]}),/*#__PURE__*/_jsx(DialogContent,{sx:{textAlign:'center',py:3},children:selectedBreakSlot&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2},children:t('bookings.takeBreakMessage','هل تريد أخذ هذا الوقت راحة؟')}),/*#__PURE__*/_jsxs(Box,{sx:{bgcolor:alpha(theme.palette.warning.main,0.1),p:2,borderRadius:2,border:`1px solid ${alpha(theme.palette.warning.main,0.3)}`},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{color:'warning.main',mb:1},children:[\"\\uD83D\\uDCC5 \",format(selectedBreakSlot.date,'EEEE, MMM d, yyyy',{locale:isRtl?ar:enUS})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",sx:{fontWeight:'bold'},children:[\"\\uD83D\\uDD50 \",(()=>{const startHour=selectedBreakSlot.hour;const startMinute=selectedBreakSlot.minute;const endMinute=startMinute+30;const endHour=endMinute>=60?startHour+1:startHour;const finalEndMinute=endMinute>=60?endMinute-60:endMinute;return`${startHour.toString().padStart(2,'0')}:${startMinute.toString().padStart(2,'0')} - ${endHour.toString().padStart(2,'0')}:${finalEndMinute.toString().padStart(2,'0')}`;})()]})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:2,color:'text.secondary'},children:t('bookings.takeBreakNote','سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط')})]})}),/*#__PURE__*/_jsxs(DialogActions,{sx:{justifyContent:'center',pb:3},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setBreakDialogOpen(false),variant:\"outlined\",sx:{minWidth:100},children:t('common.cancel','إلغاء')}),/*#__PURE__*/_jsx(Button,{onClick:confirmTakeBreak,variant:\"contained\",color:\"warning\",sx:{minWidth:100,ml:2},children:t('common.confirm','موافق')})]})]})]});};export default WeeklyBookingsTable;", "map": {"version": 3, "names": ["React", "useTranslation", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Chip", "useTheme", "alpha", "CircularProgress", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "Visibility", "ViewIcon", "Cancel", "CancelIcon", "AccessTime", "AccessTimeIcon", "RadioButton<PERSON><PERSON><PERSON>ed", "RadioButtonUncheckedIcon", "CheckCircle", "CheckCircleIcon", "SelfImprovement", "RestIcon", "format", "addDays", "ar", "enUS", "moment", "formatDateInStudentTimezone", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "WeeklyBookingsTable", "_ref", "bookings", "loading", "currentWeekStart", "daysOfWeek", "onViewDetails", "onCancelBooking", "studentProfile", "formatBookingTime", "getStatusColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "availableHours", "onTakeBreak", "weeklyBreaks", "t", "i18n", "theme", "isRtl", "language", "breakDialogOpen", "setBreakDialogOpen", "useState", "selectedBreakSlot", "setSelectedBreakSlot", "handleTakeBreak", "day", "timeSlot", "isFirstHalf", "dayIndex", "daysOfWeekData", "findIndex", "d", "key", "date", "slotMinute", "hour", "minute", "confirmTakeBreak", "isHalfHourSlotInPast", "slotStartTime", "Date", "setHours", "currentTime", "timezone", "currentTimeStr", "toISOString", "error", "console", "isHalfHourSlotAvailable", "<PERSON><PERSON><PERSON>", "toLowerCase", "daySlots", "Array", "isArray", "log", "getFullYear", "String", "getMonth", "padStart", "getDate", "length", "isBreakSlot", "some", "breakData", "datetime", "breakUtcDateTime", "teacherTimezone", "breakInTeacherTz", "breakDate", "breakTime", "split", "breakHour", "breakMinute", "map", "Number", "slotDateStr", "isDateMatch", "isTimeMatch", "slotStartMinutes", "slotEndMinutes", "slot", "startTime", "endTime", "startHour", "startMinute", "endHour", "endMinute", "availableStartMinutes", "availableEndMinutes", "timeSlots", "toString", "midTime", "push", "label", "midLabel", "firstHalf", "secondHalf", "defaultDaysOfWeek", "getAbbreviatedDayName", "abbreviations", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "substring", "getMeetingStatus", "booking", "meetingStartTime", "meetingEndTime", "setMinutes", "getMinutes", "parseInt", "duration", "now", "status", "getBookingBackgroundColor", "background", "palette", "success", "main", "dark", "border", "hoverBackground", "grey", "info", "findBookingsForSlot", "dateStr", "filter", "bookingDate", "bookingHour", "bookingMinute", "formattedDateTime", "datePart", "timePart", "hourStr", "minuteStr", "bookingDateTime", "getHours", "bookingStartMinutes", "bookingEndMinutes", "startsInThisSlot", "extendsIntoThisSlot", "slotMiddle", "sx", "display", "justifyContent", "my", "children", "elevation", "p", "mb", "borderRadius", "textAlign", "variant", "color", "primary", "alignItems", "flexWrap", "gap", "fontWeight", "opacity", "overflow", "gridTemplateColumns", "xs", "sm", "md", "bgcolor", "borderBottom", "position", "top", "zIndex", "minHeight", "borderRight", "text", "secondary", "fontSize", "index", "dayDate", "flexDirection", "lineHeight", "component", "mt", "locale", "isHourStart", "divider", "placeItems", "left", "transform", "allBookingsInSlot", "bookingsInSlot", "currentSlotStartMinutes", "visibleBookingsMap", "reduce", "acc", "curr", "existing", "visibleBookings", "Object", "values", "right", "height", "boxShadow", "pointerEvents", "bookingIndex", "bookingColors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentSlotEndMinutes", "extendsToNextHour", "width", "cursor", "transition", "onClick", "textOverflow", "WebkitLineClamp", "WebkitBoxOrient", "max<PERSON><PERSON><PERSON>", "textShadow", "student_name", "teacher_name", "size", "e", "stopPropagation", "min<PERSON><PERSON><PERSON>", "id", "firstHalfAvailable", "firstHalfInPast", "flex", "title", "arrow", "warning", "padding", "secondHalfAvailable", "secondHalfInPast", "open", "onClose", "fullWidth", "pb", "py", "finalEndMinute", "ml"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/WeeklyBookingsTable.js"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Chip,\n  useTheme,\n  alpha,\n  CircularProgress,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  IconButton\n} from '@mui/material';\nimport {\n  Visibility as ViewIcon,\n  Cancel as CancelIcon,\n  AccessTime as AccessTimeIcon,\n  RadioButtonUnchecked as RadioButtonUncheckedIcon,\n  CheckCircle as CheckCircleIcon,\n  SelfImprovement as RestIcon\n} from '@mui/icons-material';\nimport { format, addDays } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport moment from 'moment-timezone';\nimport { formatDateInStudentTimezone } from '../utils/timezone';\n\nconst WeeklyBookingsTable = ({\n  bookings,\n  loading = false,\n  currentWeekStart,\n  daysOfWeek,\n  onViewDetails,\n  onCancelBooking,\n  studentProfile,\n  formatBookingTime,\n  getStatusColor,\n  isTeacherView = false,\n  availableHours = null,\n  onTakeBreak = null,\n  weeklyBreaks = []\n}) => {\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  // State for break dialog\n  const [breakDialogOpen, setBreakDialogOpen] = React.useState(false);\n  const [selectedBreakSlot, setSelectedBreakSlot] = React.useState(null);\n\n  // Handle take break\n  const handleTakeBreak = (day, timeSlot, isFirstHalf) => {\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const slotMinute = isFirstHalf ? 0 : 30;\n\n    setSelectedBreakSlot({\n      day,\n      date,\n      hour: timeSlot.hour,\n      minute: slotMinute,\n      isFirstHalf,\n      timeSlot\n    });\n    setBreakDialogOpen(true);\n  };\n\n  const confirmTakeBreak = () => {\n    if (selectedBreakSlot && onTakeBreak) {\n      onTakeBreak(selectedBreakSlot);\n    }\n    setBreakDialogOpen(false);\n    setSelectedBreakSlot(null);\n  };\n\n  // Check if a half-hour slot is in the past (considering teacher's timezone)\n  const isHalfHourSlotInPast = (day, timeSlot, isFirstHalf) => {\n    try {\n      const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n      const date = addDays(currentWeekStart, dayIndex);\n\n      const slotMinute = isFirstHalf ? 0 : 30;\n\n      // Create the slot start datetime\n      const slotStartTime = new Date(date);\n      slotStartTime.setHours(timeSlot.hour, slotMinute, 0, 0);\n\n      // Get current time in teacher's timezone using the same method as the table\n      let currentTime;\n      if (studentProfile && studentProfile.timezone) {\n        // Use teacher's timezone\n        const currentTimeStr = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n        currentTime = new Date(currentTimeStr);\n      } else {\n        // Fallback to browser local time\n        currentTime = new Date();\n      }\n\n      // A slot is considered \"past\" if its start time has already passed\n      // This means the current half-hour slot is also considered past\n      return slotStartTime <= currentTime;\n\n    } catch (error) {\n      console.error('Error checking if slot is in past:', error);\n      return false;\n    }\n  };\n\n  // Check if a specific half-hour slot is available\n  const isHalfHourSlotAvailable = (day, timeSlot, isFirstHalf) => {\n    if (!availableHours || !isTeacherView) return false;\n\n    const dayKey = day.toLowerCase();\n    const daySlots = availableHours[dayKey];\n\n    if (!daySlots || !Array.isArray(daySlots)) return false;\n\n    // Check if this slot is taken as a break\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const slotMinute = isFirstHalf ? 0 : 30;\n\n    // Check if this slot matches any break from the backend\n    // Backend now sends UTC datetimes, so we need to convert them to teacher's timezone\n    // and check if they match this slot\n\n    console.log('🔍 FRONTEND CHECK: Checking if slot is break');\n    console.log('  📅 Day:', day, 'DayIndex:', dayIndex);\n    console.log('  📅 CurrentWeekStart:', currentWeekStart?.toISOString());\n    console.log('  📅 Calculated Date:', date.toISOString());\n    console.log('  📅 Slot Date String:', date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0') + '-' + String(date.getDate()).padStart(2, '0'));\n    console.log('  ⏰ Slot Hour:', timeSlot.hour, 'Minute:', slotMinute);\n    console.log('  🔍 Available breaks:', weeklyBreaks.length, weeklyBreaks);\n\n    // Check if any break matches this slot\n    const isBreakSlot = weeklyBreaks.some(breakData => {\n      if (typeof breakData === 'string') {\n        // Old format - ignore for now\n        return false;\n      }\n\n      if (breakData && breakData.datetime) {\n        // New format - UTC datetime from backend\n        const breakUtcDateTime = new Date(breakData.datetime);\n\n        // Convert break UTC time to teacher's timezone\n        if (studentProfile && studentProfile.timezone) {\n          const teacherTimezone = studentProfile.timezone;\n          const breakInTeacherTz = formatDateInStudentTimezone(breakData.datetime, teacherTimezone, 'YYYY-MM-DD HH:mm:ss');\n          const [breakDate, breakTime] = breakInTeacherTz.split(' ');\n          const [breakHour, breakMinute] = breakTime.split(':').map(Number);\n\n          // Get the current slot date in the same format (YYYY-MM-DD)\n          const slotDateStr = date.getFullYear() + '-' +\n                             String(date.getMonth() + 1).padStart(2, '0') + '-' +\n                             String(date.getDate()).padStart(2, '0');\n\n          const isDateMatch = breakDate === slotDateStr;\n          const isTimeMatch = breakHour === timeSlot.hour && breakMinute === slotMinute;\n\n          console.log('    🔍 Checking break:', breakData.datetime);\n          console.log('      🌍 UTC:', breakUtcDateTime.toISOString());\n          console.log('      🏠 Teacher TZ:', breakInTeacherTz);\n          console.log('      🔍 Break Date:', breakDate, 'Break Time:', `${breakHour}:${breakMinute}`);\n          console.log('      🔍 Slot Date:', slotDateStr, 'Slot Time:', `${timeSlot.hour}:${slotMinute}`);\n          console.log('      📅 Date match:', isDateMatch, `(${breakDate} vs ${slotDateStr})`);\n          console.log('      ⏰ Time match:', isTimeMatch, `(${breakHour}:${breakMinute} vs ${timeSlot.hour}:${slotMinute})`);\n\n          return isDateMatch && isTimeMatch;\n        }\n      }\n\n      return false;\n    });\n\n    console.log('  ❓ Is break slot?', isBreakSlot);\n\n    if (isBreakSlot) {\n      console.log('  🚫 SLOT IS BREAK - hiding slot');\n      return false; // Slot is taken as break\n    }\n\n    // Calculate the exact 30-minute slot we're checking (using existing slotMinute)\n    const slotStartMinutes = timeSlot.hour * 60 + slotMinute;\n    const slotEndMinutes = slotStartMinutes + 30;\n\n    // Handle edge case for last half hour of the day (23:30-24:00)\n    if (timeSlot.hour === 23 && !isFirstHalf) {\n      // For 23:30-24:00, check if any available slot covers 23:30\n      return daySlots.some(slot => {\n        const [startTime, endTime] = slot.split('-');\n        const [startHour, startMinute] = startTime.split(':').map(Number);\n        let [endHour, endMinute] = endTime.split(':').map(Number);\n\n        // Handle 24:00 or 00:00 as end time\n        if (endHour === 0 || endHour === 24) {\n          endHour = 24;\n          endMinute = 0;\n        }\n\n        const availableStartMinutes = startHour * 60 + startMinute;\n        const availableEndMinutes = endHour * 60 + endMinute;\n\n        // Check if 23:30 is covered\n        return slotStartMinutes >= availableStartMinutes &&\n               slotStartMinutes < availableEndMinutes;\n      });\n    }\n\n    // Check if this specific 30-minute slot is in the available hours\n    return daySlots.some(slot => {\n      const [startTime, endTime] = slot.split('-');\n      const [startHour, startMinute] = startTime.split(':').map(Number);\n      let [endHour, endMinute] = endTime.split(':').map(Number);\n\n      // Handle 24:00 or 00:00 as end time (next day)\n      if (endHour === 0) {\n        endHour = 24;\n        endMinute = 0;\n      }\n\n      const availableStartMinutes = startHour * 60 + startMinute;\n      const availableEndMinutes = endHour * 60 + endMinute;\n\n      // Check if the 30-minute slot fits within the available time range\n      return slotStartMinutes >= availableStartMinutes && slotEndMinutes <= availableEndMinutes;\n    });\n  };\n\n  // Define time slots - full hours from 00:00 to 23:00\n  const timeSlots = [];\n  for (let hour = 0; hour < 24; hour++) {\n    const startTime = `${hour.toString().padStart(2, '0')}:00`;\n    const midTime = `${hour.toString().padStart(2, '0')}:30`;\n    const endTime = hour < 23 ? `${(hour + 1).toString().padStart(2, '0')}:00` : '00:00';\n    timeSlots.push({\n      key: `${startTime}-${endTime}`,\n      label: startTime,\n      midLabel: midTime,\n      hour,\n      minute: 0,\n      // Include both half-hour slots for this hour\n      firstHalf: `${startTime}-${midTime}`,\n      secondHalf: hour < 23 ? `${midTime}-${endTime}` : '23:30-00:00'\n    });\n  }\n\n  // Define days of the week\n  const defaultDaysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n  const daysOfWeekData = daysOfWeek ? daysOfWeek.map(day => ({\n    key: day,\n    label: t(`days.${day}`)\n  })) : defaultDaysOfWeek.map(day => ({\n    key: day,\n    label: t(`days.${day}`)\n  }));\n\n  // Get abbreviated day names for mobile\n  const getAbbreviatedDayName = (dayKey) => {\n    const abbreviations = {\n      sunday: t('days.sundayShort') || 'Sun',\n      monday: t('days.mondayShort') || 'Mon',\n      tuesday: t('days.tuesdayShort') || 'Tue',\n      wednesday: t('days.wednesdayShort') || 'Wed',\n      thursday: t('days.thursdayShort') || 'Thu',\n      friday: t('days.fridayShort') || 'Fri',\n      saturday: t('days.saturdayShort') || 'Sat'\n    };\n    return abbreviations[dayKey] || dayKey.substring(0, 3);\n  };\n\n  // Get meeting status based on current time\n  const getMeetingStatus = (booking) => {\n    const meetingStartTime = new Date(booking.datetime);\n    const meetingEndTime = new Date(booking.datetime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));\n    const now = new Date();\n\n    if (booking.status === 'cancelled') {\n      return 'cancelled';\n    }\n\n    if (now >= meetingStartTime && now < meetingEndTime) {\n      return 'ongoing';\n    }\n\n    if (now >= meetingEndTime) {\n      return 'completed';\n    }\n\n    return 'scheduled';\n  };\n\n  // Get background color based on meeting status\n  const getBookingBackgroundColor = (booking) => {\n    const status = getMeetingStatus(booking);\n\n    switch (status) {\n      case 'ongoing':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.85)} 0%, ${alpha(theme.palette.success.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.95)} 0%, ${alpha(theme.palette.success.dark, 1)} 100%)`\n        };\n      case 'completed':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.85)} 0%, ${alpha(theme.palette.grey[700], 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.grey[500], 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.95)} 0%, ${alpha(theme.palette.grey[700], 1)} 100%)`\n        };\n      case 'cancelled':\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.85)} 0%, ${alpha(theme.palette.error.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.95)} 0%, ${alpha(theme.palette.error.dark, 1)} 100%)`\n        };\n      default: // scheduled\n        return {\n          background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.85)} 0%, ${alpha(theme.palette.info.dark, 0.95)} 100%)`,\n          border: `2px solid ${alpha(theme.palette.info.main, 0.3)}`,\n          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.95)} 0%, ${alpha(theme.palette.info.dark, 1)} 100%)`\n        };\n    }\n  };\n\n  // Find all bookings for specific day and time slot\n  const findBookingsForSlot = (day, timeSlot) => {\n    if (!bookings || !currentWeekStart) return [];\n\n    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);\n    const date = addDays(currentWeekStart, dayIndex);\n    const dateStr = format(date, 'yyyy-MM-dd');\n\n    return bookings.filter(booking => {\n      // Get booking date and time in student timezone\n      let bookingDate, bookingHour, bookingMinute;\n\n      if (studentProfile && studentProfile.timezone) {\n        // Use student timezone for both date and time\n        const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n        const [datePart, timePart] = formattedDateTime.split(' ');\n        bookingDate = datePart;\n\n        const [hourStr, minuteStr] = timePart.split(':');\n        bookingHour = parseInt(hourStr);\n        bookingMinute = parseInt(minuteStr);\n      } else {\n        // Fallback to browser local time\n        const bookingDateTime = new Date(booking.datetime);\n        bookingDate = format(bookingDateTime, 'yyyy-MM-dd');\n        bookingHour = bookingDateTime.getHours();\n        bookingMinute = bookingDateTime.getMinutes();\n      }\n\n      // Check if booking matches this date\n      const isDateMatch = bookingDate === dateStr;\n      if (!isDateMatch) return false;\n\n      // Calculate booking start and end times in minutes from midnight\n      const duration = parseInt(booking.duration) || 25;\n      const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n      const bookingEndMinutes = bookingStartMinutes + duration;\n\n      // Calculate slot start and end times in minutes from midnight\n      const slotStartMinutes = timeSlot.hour * 60;\n      const slotEndMinutes = slotStartMinutes + 60;\n\n      // For 50-minute lessons, show in both starting hour and next hour if it spans\n      if (duration === 50) {\n        // Check if booking starts in this slot\n        const startsInThisSlot = bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotEndMinutes;\n\n        // Check if booking extends into this slot from previous hour\n        const extendsIntoThisSlot = bookingStartMinutes < slotStartMinutes && bookingEndMinutes > slotStartMinutes;\n\n        return startsInThisSlot || extendsIntoThisSlot;\n      } else {\n        // For 25-minute lessons, show in the exact 30-minute slot\n        const slotMiddle = slotStartMinutes + 30;\n\n        // Check if booking is in first half (00-30) or second half (30-60)\n        if (bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle) {\n          // First half booking\n          return bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle;\n        } else if (bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes) {\n          // Second half booking\n          return bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes;\n        }\n      }\n\n      return false;\n    });\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  if (!bookings || bookings.length === 0) {\n    return (\n      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2, textAlign: 'center' }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          {t('bookings.noBookings')}\n        </Typography>\n      </Paper>\n    );\n  }\n\n  return (\n    <Paper elevation={3} sx={{ mb: 4, borderRadius: 2 }}>\n      {/* Header */}\n      <Box sx={{\n        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n        p: 3,\n        color: 'white',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        flexWrap: 'wrap',\n        gap: 2\n      }}>\n        <Box>\n          <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 1 }}>\n            📅 {t('bookings.weeklyTitle')}\n          </Typography>\n          <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n            {t('bookings.weeklyDescription')}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Calendar Table */}\n      <Box sx={{ overflow: 'auto' }}>\n        {/* Days Header */}\n        <Box sx={{\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '60px repeat(7, minmax(80px, 1fr))',\n            sm: '80px repeat(7, minmax(100px, 1fr))',\n            md: '120px repeat(7, minmax(120px, 1fr))',\n          },\n          bgcolor: alpha(theme.palette.primary.main, 0.05),\n          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          position: 'sticky',\n          top: 0,\n          zIndex: 10\n        }}>\n          <Box sx={{\n            p: { xs: 1.5, sm: 2, md: 2.5 },\n            minHeight: { xs: '60px', sm: '75px', md: '90px' },\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`\n          }}>\n            <Typography variant=\"body2\" sx={{\n              fontWeight: 'bold',\n              color: theme.palette.text.secondary,\n              fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' }\n            }}>\n              ⏰ {t('teacher.time')}\n            </Typography>\n          </Box>\n          {daysOfWeekData.map((day, index) => {\n            // Calculate the date for this day\n            const dayDate = currentWeekStart ? addDays(currentWeekStart, index) : new Date();\n            \n            return (\n              <Box\n                key={day.key}\n                sx={{\n                  p: { xs: 1.5, sm: 2, md: 2.5 },\n                  minHeight: { xs: '60px', sm: '75px', md: '90px' },\n                  textAlign: 'center',\n                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                  '&:last-child': { borderRight: 'none' },\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center',\n                  alignItems: 'center'\n                }}\n              >\n                <Typography variant=\"h6\" sx={{\n                  fontWeight: 'bold',\n                  color: theme.palette.primary.main,\n                  fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' },\n                  lineHeight: 1.2\n                }}>\n                  {/* Day name */}\n                  <Box component=\"span\" sx={{ display: { xs: 'none', md: 'block' } }}>\n                    {day.label}\n                  </Box>\n                  <Box component=\"span\" sx={{ display: { xs: 'none', sm: 'block', md: 'none' } }}>\n                    {day.label.length > 6 ? day.label.substring(0, 6) : day.label}\n                  </Box>\n                  <Box component=\"span\" sx={{ display: { xs: 'block', sm: 'none' } }}>\n                    {getAbbreviatedDayName(day.key)}\n                  </Box>\n                </Typography>\n                \n                {/* Date */}\n                <Typography variant=\"caption\" sx={{\n                  display: 'block',\n                  color: theme.palette.text.secondary,\n                  fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' },\n                  mt: 0.5\n                }}>\n                  {format(dayDate, 'MMM d', { locale: isRtl ? ar : enUS })}\n                </Typography>\n              </Box>\n            );\n          })}\n        </Box>\n\n        {/* Time Slots Grid */}\n        <Box>\n          {timeSlots.map((timeSlot, index) => {\n            const isHourStart = timeSlot.minute === 0;\n            return (\n              <Box\n                key={timeSlot.key}\n                sx={{\n                  display: 'grid',\n                  gridTemplateColumns: {\n                    xs: '60px repeat(7, minmax(80px, 1fr))',\n                    sm: '80px repeat(7, minmax(100px, 1fr))',\n                    md: '120px repeat(7, minmax(120px, 1fr))',\n                  },\n                  borderBottom: `1px solid ${alpha(theme.palette.divider, isHourStart ? 0.3 : 0.1)}`,\n                  bgcolor: index % 4 < 2 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',\n                  '&:hover': {\n                    bgcolor: alpha(theme.palette.primary.main, 0.05)\n                  }\n                }}\n              >\n                {/* Time Labels */}\n                <Box sx={{\n                  p: { xs: 0.5, sm: 1, md: 1.5 },\n                  display: 'grid',\n                  placeItems: 'center',\n                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                  bgcolor: isHourStart ? alpha(theme.palette.primary.main, 0.05) : 'transparent',\n                  position: 'relative'\n                }}>\n                  <Box sx={{ \n                    display: 'flex', \n                    flexDirection: 'column', \n                    alignItems: 'center', \n                    gap: 0.5,\n                    position: 'absolute',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    top: timeSlot.minute === 0 ? '-8px' : 'calc(50% + 20px)'\n                  }}\n                  >\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        fontWeight: 'bold',\n                        color: theme.palette.primary.main,\n                        fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' }\n                      }}\n                    >\n                      {timeSlot.label}\n                    </Typography>\n                  </Box>\n                </Box>\n\n                {/* Day Cells */}\n                {daysOfWeekData.map((day) => {\n                  const allBookingsInSlot = findBookingsForSlot(day.key, timeSlot);\n\n                  // Filter out extended bookings from previous hour\n                  // Filter out extended bookings from previous hour\n          const bookingsInSlot = allBookingsInSlot.filter(booking => {\n                    // Get booking start time to determine if it's extended from previous hour\n                    let bookingHour = 0;\n                    let bookingMinute = 0;\n\n                    if (studentProfile && studentProfile.timezone) {\n                      const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n                      const [, timePart] = formattedDateTime.split(' ');\n                      const [hourStr, minuteStr] = timePart.split(':');\n                      bookingHour = parseInt(hourStr);\n                      bookingMinute = parseInt(minuteStr);\n                    } else {\n                      const bookingDate = new Date(booking.datetime);\n                      bookingHour = bookingDate.getHours();\n                      bookingMinute = bookingDate.getMinutes();\n                    }\n\n                    const duration = parseInt(booking.duration) || 25;\n                    const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n                    const currentSlotStartMinutes = timeSlot.hour * 60;\n\n                    // Only show bookings that start in this slot or earlier in the same hour\n                    // Don't show bookings that extend from previous hour\n                    if (duration === 50) {\n                      const extendsIntoThisSlot = bookingStartMinutes < currentSlotStartMinutes;\n                      return !extendsIntoThisSlot; // Filter out extended bookings\n                    }\n\n                    return true; // Show all 25-minute bookings\n                  });\n\n                  // Deduplicate overlapping bookings that share the exact same start time (datetime)\n                  // If a non-cancelled booking and a cancelled booking overlap, keep the non-cancelled one\n                  // so the student sees the active reservation instead of the cancelled one.\n                  const visibleBookingsMap = bookingsInSlot.reduce((acc, curr) => {\n                    const key = curr.datetime; // ISO string coming from backend\n                    const existing = acc[key];\n\n                    if (!existing) {\n                      acc[key] = curr; // first occurrence\n                    } else {\n                      // If the existing booking is cancelled and the new one is not, replace it\n                      if (existing.status === 'cancelled' && curr.status !== 'cancelled') {\n                        acc[key] = curr;\n                      }\n                      // If both have same non-cancelled status keep the earlier inserted one\n                      // otherwise, leave as is.\n                    }\n                    return acc;\n                  }, {});\n\n                  const visibleBookings = Object.values(visibleBookingsMap);\n\n                  return (\n                    <Box\n                      key={`${day.key}-${timeSlot.key}`}\n                      sx={{\n                        p: { xs: 0.2, sm: 0.4, md: 0.6 },\n                        minHeight: { xs: '80px', sm: '100px', md: '120px' }, // Increased height for better content fit\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n                        '&:last-child': { borderRight: 'none' },\n                        position: 'relative'\n                      }}\n                    >\n                      {/* Continuous hour line */}\n                      <Box\n                        sx={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          right: 0,\n                          height: '4px',\n                          bgcolor: theme.palette.primary.main,\n                          zIndex: 0,\n                          borderRadius: '2px',\n                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                          pointerEvents: 'none'\n                        }}\n                      />\n                      {/* Lighter half-hour line */}\n                      <Box\n                        sx={{\n                          position: 'absolute',\n                          top: '50%',\n                          left: 0,\n                          right: 0,\n                          height: '2px',\n                          bgcolor: alpha(theme.palette.divider, 0.3), // More transparent\n                          zIndex: 0, // Lower z-index to stay behind bookings\n                          borderRadius: '1px'\n                        }}\n                      />\n\n                      {visibleBookings.length > 0 ? (\n                        <>\n                          {visibleBookings.map((booking, bookingIndex) => {\n                            const bookingColors = getBookingBackgroundColor(booking);\n                            const isFullLesson = parseInt(booking.duration) === 50;\n\n                            // Get booking start time to determine position\n                            let bookingMinute = 0;\n                            let bookingHour = 0;\n                            if (studentProfile && studentProfile.timezone) {\n                              const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n                              const [, timePart] = formattedDateTime.split(' ');\n                              const [hourStr, minuteStr] = timePart.split(':');\n                              bookingHour = parseInt(hourStr);\n                              bookingMinute = parseInt(minuteStr);\n                            } else {\n                              const bookingDate = new Date(booking.datetime);\n                              bookingHour = bookingDate.getHours();\n                              bookingMinute = bookingDate.getMinutes();\n                            }\n\n                            // For full lessons, determine which half of this slot the booking occupies\n                            // For half lessons, show in correct half based on start time\n                            let isFirstHalf = bookingMinute === 0;\n\n                            // For full lessons, determine the visual representation\n                            if (isFullLesson) {\n                              // Calculate if this booking spans into the next hour\n                              const bookingStartMinutes = bookingHour * 60 + bookingMinute;\n                              const bookingEndMinutes = bookingStartMinutes + 50; // 50-minute lesson\n                              const currentSlotStartMinutes = timeSlot.hour * 60;\n                              const currentSlotEndMinutes = currentSlotStartMinutes + 60;\n\n                              // Check if booking starts in this slot\n                              const startsInThisSlot = bookingStartMinutes >= currentSlotStartMinutes && bookingStartMinutes < currentSlotEndMinutes;\n\n                              if (startsInThisSlot) {\n                                // Booking starts in this slot\n                                const extendsToNextHour = bookingEndMinutes > currentSlotEndMinutes;\n                                if (extendsToNextHour) {\n                                  // This is a cross-hour booking, show it spanning from current position to next hour\n                                  isFirstHalf = 'spanning'; // Special case for spanning bookings\n                                } else {\n                                  // Regular full lesson within one hour - don't override first half bookings\n                                  isFirstHalf = bookingMinute === 0 ? null : 'secondHalfFull';\n                                }\n                              }\n                            }\n\n                            return (\n                              <Box\n                                key={`${booking.id}-${bookingIndex}`}\n                                sx={{\n                                  width: '90%',\n                                  height: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      // For cross-hour bookings, extend from second half to next hour\n                                      return '100%'; // Extend to cover both halves visually\n                                    }\n                                    if (isFirstHalf === 'secondHalfFull') {\n                                      // Full lesson starting from second half - only show in second half\n                                      return '40%';\n                                    }\n\n                                    return isFullLesson ? '95%' : '40%';\n                                  })(),\n                                  position: 'absolute',\n                                  top: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      // Start from the second half of current slot\n                                      return '50%';\n                                    }\n                                    if (isFirstHalf === 'secondHalfFull') {\n                                      // Position in second half only\n                                      return '55%';\n                                    }\n\n                                    return isFullLesson ? '2.5%' : (isFirstHalf ? '5%' : '55%');\n                                  })(),\n                                  left: '5%',\n                                  zIndex: (() => {\n                                    if (isFirstHalf === 'spanning') {\n                                      return 100; // Very high z-index for spanning bookings\n                                    }\n\n                                    return 10 + bookingIndex; // Stacked z-index for multiple bookings\n                                  })(),\n                                  borderRadius: 1,\n                                  background: bookingColors.background,\n                                  display: 'flex',\n                                  flexDirection: 'column',\n                                  alignItems: 'center',\n                                  justifyContent: 'center',\n                                  gap: { xs: 0.2, sm: 0.3, md: 0.4 },\n                                  p: { xs: 0.2, sm: 0.3, md: 0.4 },\n                                  cursor: 'pointer',\n                                  border: bookingColors.border,\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                                  '&:hover': {\n                                    background: bookingColors.hoverBackground,\n                                    transform: 'translateY(-1px)',\n                                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                                  },\n                                  transition: 'all 0.2s ease-in-out',\n                                  overflow: 'hidden'\n                                }}\n                                onClick={() => onViewDetails(booking)}\n                              >\n\n\n                                <Typography\n                                  variant=\"body2\"\n                                  sx={{\n                                    color: 'white',\n                                    fontWeight: 'bold',\n                                    fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.75rem' },\n                                    textAlign: 'center',\n                                    lineHeight: 1.1,\n                                    overflow: 'hidden',\n                                    textOverflow: 'ellipsis',\n                                    display: '-webkit-box',\n                                    WebkitLineClamp: 1,\n                                    WebkitBoxOrient: 'vertical',\n                                    maxWidth: '100%',\n                                    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n                                    mb: { xs: 0.1, sm: 0.2, md: 0.2 }\n                                  }}\n                                >\n                                  {isTeacherView ? booking.student_name : booking.teacher_name}\n                                </Typography>\n\n                                <Box sx={{ display: 'flex', gap: { xs: 0.2, sm: 0.3, md: 0.3 } }}>\n                                  <Button\n                                    size=\"small\"\n                                    variant=\"contained\"\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      onViewDetails(booking);\n                                    }}\n                                    sx={{\n                                      minWidth: 'auto',\n                                      width: { xs: 18, sm: 22, md: 24 },\n                                      height: { xs: 16, sm: 20, md: 22 },\n                                      p: 0,\n                                      bgcolor: 'rgba(255, 255, 255, 0.2)',\n                                      color: 'white',\n                                      borderRadius: 0.5,\n                                      '&:hover': {\n                                        bgcolor: 'rgba(255, 255, 255, 0.3)',\n                                      }\n                                    }}\n                                  >\n                                    <ViewIcon sx={{ fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' } }} />\n                                  </Button>\n                                  {booking.status === 'scheduled' && onCancelBooking && !isTeacherView && (\n                                    <Button\n                                      size=\"small\"\n                                      variant=\"contained\"\n                                      onClick={(e) => {\n                                        e.stopPropagation();\n                                        onCancelBooking(booking);\n                                      }}\n                                      sx={{\n                                        minWidth: 'auto',\n                                        width: { xs: 18, sm: 22, md: 24 },\n                                        height: { xs: 16, sm: 20, md: 22 },\n                                        p: 0,\n                                        bgcolor: 'rgba(244, 67, 54, 0.8)',\n                                        color: 'white',\n                                        borderRadius: 0.5,\n                                        '&:hover': {\n                                          bgcolor: 'rgba(244, 67, 54, 1)',\n                                        }\n                                      }}\n                                    >\n                                      <CancelIcon sx={{ fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' } }} />\n                                    </Button>\n                                  )}\n                                </Box>\n                              </Box>\n                            );\n                          })}\n                        </>\n                      ) : (\n                        // Empty slot - show two halves for 30-minute slots\n                        <Box sx={{\n                          width: '90%',\n                          height: { xs: '65px', sm: '85px', md: '105px' },\n                          borderRadius: 1.5,\n                          border: `1px solid ${alpha(theme.palette.grey[300], 0.5)}`,\n                          display: 'flex',\n                          flexDirection: 'column',\n                          overflow: 'hidden',\n                          zIndex: 1\n                        }}>\n                          {/* First Half (00-30 minutes) */}\n                          {(() => {\n                            const firstHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, true);\n                            const firstHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, true);\n\n                            return (\n                              <Box sx={{\n                                flex: 1,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                borderBottom: `1px solid ${alpha(theme.palette.grey[300], 0.3)}`,\n                                bgcolor: firstHalfInPast\n                                  ? alpha(theme.palette.grey[600], 0.15)\n                                  : firstHalfAvailable\n                                    ? alpha(theme.palette.success.main, 0.08)\n                                    : alpha(theme.palette.grey[100], 0.3),\n                                transition: 'all 0.2s ease'\n                              }}>\n                                {firstHalfInPast ? (\n                                  <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.pastSlot', 'Past Time')}`} arrow>\n                                    <RadioButtonUncheckedIcon\n                                      sx={{\n                                        color: theme.palette.grey[600],\n                                        fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                      }}\n                                    />\n                                  </Tooltip>\n                                ) : firstHalfAvailable ? (\n                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                                    <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.availableSlot', 'Available')}`} arrow>\n                                      <CheckCircleIcon\n                                        sx={{\n                                          color: theme.palette.success.main,\n                                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },\n                                          cursor: 'pointer'\n                                        }}\n                                      />\n                                    </Tooltip>\n                                    {onTakeBreak && (\n                                      <Tooltip title={t('bookings.takeBreak', 'Take Break')} arrow>\n                                        <IconButton\n                                          size=\"small\"\n                                          onClick={() => handleTakeBreak(day.key, timeSlot, true)}\n                                          sx={{\n                                            color: theme.palette.warning.main,\n                                            fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },\n                                            padding: '2px',\n                                            '&:hover': {\n                                              bgcolor: alpha(theme.palette.warning.main, 0.1)\n                                            }\n                                          }}\n                                        >\n                                          <RestIcon sx={{ fontSize: 'inherit' }} />\n                                        </IconButton>\n                                      </Tooltip>\n                                    )}\n                                  </Box>\n                                ) : (\n                                  <RadioButtonUncheckedIcon\n                                    sx={{\n                                      color: alpha(theme.palette.grey[400], 0.5),\n                                      fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                    }}\n                                  />\n                                )}\n                              </Box>\n                            );\n                          })()}\n\n                          {/* Second Half (30-60 minutes) */}\n                          {(() => {\n                            const secondHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, false);\n                            const secondHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, false);\n\n                            return (\n                              <Box sx={{\n                                flex: 1,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                bgcolor: secondHalfInPast\n                                  ? alpha(theme.palette.grey[600], 0.15)\n                                  : secondHalfAvailable\n                                    ? alpha(theme.palette.success.main, 0.08)\n                                    : alpha(theme.palette.grey[100], 0.3),\n                                transition: 'all 0.2s ease'\n                              }}>\n                                {secondHalfInPast ? (\n                                  <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.pastSlot', 'Past Time')}`} arrow>\n                                    <RadioButtonUncheckedIcon\n                                      sx={{\n                                        color: theme.palette.grey[600],\n                                        fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                      }}\n                                    />\n                                  </Tooltip>\n                                ) : secondHalfAvailable ? (\n                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                                    <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.availableSlot', 'Available')}`} arrow>\n                                      <CheckCircleIcon\n                                        sx={{\n                                          color: theme.palette.success.main,\n                                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },\n                                          cursor: 'pointer'\n                                        }}\n                                      />\n                                    </Tooltip>\n                                    {onTakeBreak && (\n                                      <Tooltip title={t('bookings.takeBreak', 'Take Break')} arrow>\n                                        <IconButton\n                                          size=\"small\"\n                                          onClick={() => handleTakeBreak(day.key, timeSlot, false)}\n                                          sx={{\n                                            color: theme.palette.warning.main,\n                                            fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },\n                                            padding: '2px',\n                                            '&:hover': {\n                                              bgcolor: alpha(theme.palette.warning.main, 0.1)\n                                            }\n                                          }}\n                                        >\n                                          <RestIcon sx={{ fontSize: 'inherit' }} />\n                                        </IconButton>\n                                      </Tooltip>\n                                    )}\n                                  </Box>\n                                ) : (\n                                  <RadioButtonUncheckedIcon\n                                    sx={{\n                                      color: alpha(theme.palette.grey[400], 0.5),\n                                      fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }\n                                    }}\n                                  />\n                                )}\n                              </Box>\n                            );\n                          })()}\n                        </Box>\n                      )}\n                    </Box>\n                  );\n                })}\n              </Box>\n            );\n          })}\n        </Box>\n      </Box>\n\n      {/* Take Break Dialog */}\n      <Dialog\n        open={breakDialogOpen}\n        onClose={() => setBreakDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>\n          <RestIcon sx={{ fontSize: '2rem', color: 'warning.main', mb: 1 }} />\n          <Typography variant=\"h6\" component=\"div\">\n            {t('bookings.takeBreakTitle', 'أخذ هذا الوقت راحة')}\n          </Typography>\n        </DialogTitle>\n\n        <DialogContent sx={{ textAlign: 'center', py: 3 }}>\n          {selectedBreakSlot && (\n            <Box>\n              <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                {t('bookings.takeBreakMessage', 'هل تريد أخذ هذا الوقت راحة؟')}\n              </Typography>\n\n              <Box sx={{\n                bgcolor: alpha(theme.palette.warning.main, 0.1),\n                p: 2,\n                borderRadius: 2,\n                border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`\n              }}>\n                <Typography variant=\"h6\" sx={{ color: 'warning.main', mb: 1 }}>\n                  📅 {format(selectedBreakSlot.date, 'EEEE, MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                </Typography>\n                <Typography variant=\"h5\" sx={{ fontWeight: 'bold' }}>\n                  🕐 {(() => {\n                    const startHour = selectedBreakSlot.hour;\n                    const startMinute = selectedBreakSlot.minute;\n                    const endMinute = startMinute + 30;\n                    const endHour = endMinute >= 60 ? startHour + 1 : startHour;\n                    const finalEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;\n\n                    return `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${finalEndMinute.toString().padStart(2, '0')}`;\n                  })()}\n                </Typography>\n              </Box>\n\n              <Typography variant=\"body2\" sx={{ mt: 2, color: 'text.secondary' }}>\n                {t('bookings.takeBreakNote', 'سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط')}\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n\n        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>\n          <Button\n            onClick={() => setBreakDialogOpen(false)}\n            variant=\"outlined\"\n            sx={{ minWidth: 100 }}\n          >\n            {t('common.cancel', 'إلغاء')}\n          </Button>\n          <Button\n            onClick={confirmTakeBreak}\n            variant=\"contained\"\n            color=\"warning\"\n            sx={{ minWidth: 100, ml: 2 }}\n          >\n            {t('common.confirm', 'موافق')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Paper>\n  );\n};\n\nexport default WeeklyBookingsTable;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,KAAK,CACLC,gBAAgB,CAChBC,OAAO,CACPC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,UAAU,KACL,eAAe,CACtB,OACEC,UAAU,GAAI,CAAAC,QAAQ,CACtBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,oBAAoB,GAAI,CAAAC,wBAAwB,CAChDC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,eAAe,GAAI,CAAAC,QAAQ,KACtB,qBAAqB,CAC5B,OAASC,MAAM,CAAEC,OAAO,KAAQ,UAAU,CAC1C,OAASC,EAAE,CAAEC,IAAI,KAAQ,iBAAiB,CAC1C,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CACpC,OAASC,2BAA2B,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEhE,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EActB,IAduB,CAC3BC,QAAQ,CACRC,OAAO,CAAG,KAAK,CACfC,gBAAgB,CAChBC,UAAU,CACVC,aAAa,CACbC,eAAe,CACfC,cAAc,CACdC,iBAAiB,CACjBC,cAAc,CACdC,aAAa,CAAG,KAAK,CACrBC,cAAc,CAAG,IAAI,CACrBC,WAAW,CAAG,IAAI,CAClBC,YAAY,CAAG,EACjB,CAAC,CAAAb,IAAA,CACC,KAAM,CAAEc,CAAC,CAAEC,IAAK,CAAC,CAAGvD,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAwD,KAAK,CAAGlD,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAmD,KAAK,CAAGF,IAAI,CAACG,QAAQ,GAAK,IAAI,CAEpC;AACA,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAG7D,KAAK,CAAC8D,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhE,KAAK,CAAC8D,QAAQ,CAAC,IAAI,CAAC,CAEtE;AACA,KAAM,CAAAG,eAAe,CAAGA,CAACC,GAAG,CAAEC,QAAQ,CAAEC,WAAW,GAAK,CACtD,KAAM,CAAAC,QAAQ,CAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKP,GAAG,CAAC,CAC7D,KAAM,CAAAQ,IAAI,CAAG7C,OAAO,CAACe,gBAAgB,CAAEyB,QAAQ,CAAC,CAChD,KAAM,CAAAM,UAAU,CAAGP,WAAW,CAAG,CAAC,CAAG,EAAE,CAEvCJ,oBAAoB,CAAC,CACnBE,GAAG,CACHQ,IAAI,CACJE,IAAI,CAAET,QAAQ,CAACS,IAAI,CACnBC,MAAM,CAAEF,UAAU,CAClBP,WAAW,CACXD,QACF,CAAC,CAAC,CACFN,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAiB,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAIf,iBAAiB,EAAIV,WAAW,CAAE,CACpCA,WAAW,CAACU,iBAAiB,CAAC,CAChC,CACAF,kBAAkB,CAAC,KAAK,CAAC,CACzBG,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAe,oBAAoB,CAAGA,CAACb,GAAG,CAAEC,QAAQ,CAAEC,WAAW,GAAK,CAC3D,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKP,GAAG,CAAC,CAC7D,KAAM,CAAAQ,IAAI,CAAG7C,OAAO,CAACe,gBAAgB,CAAEyB,QAAQ,CAAC,CAEhD,KAAM,CAAAM,UAAU,CAAGP,WAAW,CAAG,CAAC,CAAG,EAAE,CAEvC;AACA,KAAM,CAAAY,aAAa,CAAG,GAAI,CAAAC,IAAI,CAACP,IAAI,CAAC,CACpCM,aAAa,CAACE,QAAQ,CAACf,QAAQ,CAACS,IAAI,CAAED,UAAU,CAAE,CAAC,CAAE,CAAC,CAAC,CAEvD;AACA,GAAI,CAAAQ,WAAW,CACf,GAAInC,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C;AACA,KAAM,CAAAC,cAAc,CAAGpD,2BAA2B,CAAC,GAAI,CAAAgD,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CAAEtC,cAAc,CAACoC,QAAQ,CAAE,qBAAqB,CAAC,CAC5HD,WAAW,CAAG,GAAI,CAAAF,IAAI,CAACI,cAAc,CAAC,CACxC,CAAC,IAAM,CACL;AACAF,WAAW,CAAG,GAAI,CAAAF,IAAI,CAAC,CAAC,CAC1B,CAEA;AACA;AACA,MAAO,CAAAD,aAAa,EAAIG,WAAW,CAErC,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAE,uBAAuB,CAAGA,CAACvB,GAAG,CAAEC,QAAQ,CAAEC,WAAW,GAAK,CAC9D,GAAI,CAAChB,cAAc,EAAI,CAACD,aAAa,CAAE,MAAO,MAAK,CAEnD,KAAM,CAAAuC,MAAM,CAAGxB,GAAG,CAACyB,WAAW,CAAC,CAAC,CAChC,KAAM,CAAAC,QAAQ,CAAGxC,cAAc,CAACsC,MAAM,CAAC,CAEvC,GAAI,CAACE,QAAQ,EAAI,CAACC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,CAAE,MAAO,MAAK,CAEvD;AACA,KAAM,CAAAvB,QAAQ,CAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKP,GAAG,CAAC,CAC7D,KAAM,CAAAQ,IAAI,CAAG7C,OAAO,CAACe,gBAAgB,CAAEyB,QAAQ,CAAC,CAChD,KAAM,CAAAM,UAAU,CAAGP,WAAW,CAAG,CAAC,CAAG,EAAE,CAEvC;AACA;AACA;AAEAoB,OAAO,CAACO,GAAG,CAAC,8CAA8C,CAAC,CAC3DP,OAAO,CAACO,GAAG,CAAC,WAAW,CAAE7B,GAAG,CAAE,WAAW,CAAEG,QAAQ,CAAC,CACpDmB,OAAO,CAACO,GAAG,CAAC,wBAAwB,CAAEnD,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE0C,WAAW,CAAC,CAAC,CAAC,CACtEE,OAAO,CAACO,GAAG,CAAC,uBAAuB,CAAErB,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,CACxDE,OAAO,CAACO,GAAG,CAAC,wBAAwB,CAAErB,IAAI,CAACsB,WAAW,CAAC,CAAC,CAAG,GAAG,CAAGC,MAAM,CAACvB,IAAI,CAACwB,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAG,GAAG,CAAGF,MAAM,CAACvB,IAAI,CAAC0B,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAC,CAC9JX,OAAO,CAACO,GAAG,CAAC,gBAAgB,CAAE5B,QAAQ,CAACS,IAAI,CAAE,SAAS,CAAED,UAAU,CAAC,CACnEa,OAAO,CAACO,GAAG,CAAC,wBAAwB,CAAEzC,YAAY,CAAC+C,MAAM,CAAE/C,YAAY,CAAC,CAExE;AACA,KAAM,CAAAgD,WAAW,CAAGhD,YAAY,CAACiD,IAAI,CAACC,SAAS,EAAI,CACjD,GAAI,MAAO,CAAAA,SAAS,GAAK,QAAQ,CAAE,CACjC;AACA,MAAO,MAAK,CACd,CAEA,GAAIA,SAAS,EAAIA,SAAS,CAACC,QAAQ,CAAE,CACnC;AACA,KAAM,CAAAC,gBAAgB,CAAG,GAAI,CAAAzB,IAAI,CAACuB,SAAS,CAACC,QAAQ,CAAC,CAErD;AACA,GAAIzD,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C,KAAM,CAAAuB,eAAe,CAAG3D,cAAc,CAACoC,QAAQ,CAC/C,KAAM,CAAAwB,gBAAgB,CAAG3E,2BAA2B,CAACuE,SAAS,CAACC,QAAQ,CAAEE,eAAe,CAAE,qBAAqB,CAAC,CAChH,KAAM,CAACE,SAAS,CAAEC,SAAS,CAAC,CAAGF,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAC1D,KAAM,CAACC,SAAS,CAAEC,WAAW,CAAC,CAAGH,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC,CAEjE;AACA,KAAM,CAAAC,WAAW,CAAG1C,IAAI,CAACsB,WAAW,CAAC,CAAC,CAAG,GAAG,CACzBC,MAAM,CAACvB,IAAI,CAACwB,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAG,GAAG,CAClDF,MAAM,CAACvB,IAAI,CAAC0B,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAE1D,KAAM,CAAAkB,WAAW,CAAGR,SAAS,GAAKO,WAAW,CAC7C,KAAM,CAAAE,WAAW,CAAGN,SAAS,GAAK7C,QAAQ,CAACS,IAAI,EAAIqC,WAAW,GAAKtC,UAAU,CAE7Ea,OAAO,CAACO,GAAG,CAAC,wBAAwB,CAAES,SAAS,CAACC,QAAQ,CAAC,CACzDjB,OAAO,CAACO,GAAG,CAAC,eAAe,CAAEW,gBAAgB,CAACpB,WAAW,CAAC,CAAC,CAAC,CAC5DE,OAAO,CAACO,GAAG,CAAC,sBAAsB,CAAEa,gBAAgB,CAAC,CACrDpB,OAAO,CAACO,GAAG,CAAC,sBAAsB,CAAEc,SAAS,CAAE,aAAa,CAAE,GAAGG,SAAS,IAAIC,WAAW,EAAE,CAAC,CAC5FzB,OAAO,CAACO,GAAG,CAAC,qBAAqB,CAAEqB,WAAW,CAAE,YAAY,CAAE,GAAGjD,QAAQ,CAACS,IAAI,IAAID,UAAU,EAAE,CAAC,CAC/Fa,OAAO,CAACO,GAAG,CAAC,sBAAsB,CAAEsB,WAAW,CAAE,IAAIR,SAAS,OAAOO,WAAW,GAAG,CAAC,CACpF5B,OAAO,CAACO,GAAG,CAAC,qBAAqB,CAAEuB,WAAW,CAAE,IAAIN,SAAS,IAAIC,WAAW,OAAO9C,QAAQ,CAACS,IAAI,IAAID,UAAU,GAAG,CAAC,CAElH,MAAO,CAAA0C,WAAW,EAAIC,WAAW,CACnC,CACF,CAEA,MAAO,MAAK,CACd,CAAC,CAAC,CAEF9B,OAAO,CAACO,GAAG,CAAC,oBAAoB,CAAEO,WAAW,CAAC,CAE9C,GAAIA,WAAW,CAAE,CACfd,OAAO,CAACO,GAAG,CAAC,kCAAkC,CAAC,CAC/C,MAAO,MAAK,CAAE;AAChB,CAEA;AACA,KAAM,CAAAwB,gBAAgB,CAAGpD,QAAQ,CAACS,IAAI,CAAG,EAAE,CAAGD,UAAU,CACxD,KAAM,CAAA6C,cAAc,CAAGD,gBAAgB,CAAG,EAAE,CAE5C;AACA,GAAIpD,QAAQ,CAACS,IAAI,GAAK,EAAE,EAAI,CAACR,WAAW,CAAE,CACxC;AACA,MAAO,CAAAwB,QAAQ,CAACW,IAAI,CAACkB,IAAI,EAAI,CAC3B,KAAM,CAACC,SAAS,CAAEC,OAAO,CAAC,CAAGF,IAAI,CAACV,KAAK,CAAC,GAAG,CAAC,CAC5C,KAAM,CAACa,SAAS,CAAEC,WAAW,CAAC,CAAGH,SAAS,CAACX,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC,CACjE,GAAI,CAACW,OAAO,CAAEC,SAAS,CAAC,CAAGJ,OAAO,CAACZ,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC,CAEzD;AACA,GAAIW,OAAO,GAAK,CAAC,EAAIA,OAAO,GAAK,EAAE,CAAE,CACnCA,OAAO,CAAG,EAAE,CACZC,SAAS,CAAG,CAAC,CACf,CAEA,KAAM,CAAAC,qBAAqB,CAAGJ,SAAS,CAAG,EAAE,CAAGC,WAAW,CAC1D,KAAM,CAAAI,mBAAmB,CAAGH,OAAO,CAAG,EAAE,CAAGC,SAAS,CAEpD;AACA,MAAO,CAAAR,gBAAgB,EAAIS,qBAAqB,EACzCT,gBAAgB,CAAGU,mBAAmB,CAC/C,CAAC,CAAC,CACJ,CAEA;AACA,MAAO,CAAArC,QAAQ,CAACW,IAAI,CAACkB,IAAI,EAAI,CAC3B,KAAM,CAACC,SAAS,CAAEC,OAAO,CAAC,CAAGF,IAAI,CAACV,KAAK,CAAC,GAAG,CAAC,CAC5C,KAAM,CAACa,SAAS,CAAEC,WAAW,CAAC,CAAGH,SAAS,CAACX,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC,CACjE,GAAI,CAACW,OAAO,CAAEC,SAAS,CAAC,CAAGJ,OAAO,CAACZ,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC,CAEzD;AACA,GAAIW,OAAO,GAAK,CAAC,CAAE,CACjBA,OAAO,CAAG,EAAE,CACZC,SAAS,CAAG,CAAC,CACf,CAEA,KAAM,CAAAC,qBAAqB,CAAGJ,SAAS,CAAG,EAAE,CAAGC,WAAW,CAC1D,KAAM,CAAAI,mBAAmB,CAAGH,OAAO,CAAG,EAAE,CAAGC,SAAS,CAEpD;AACA,MAAO,CAAAR,gBAAgB,EAAIS,qBAAqB,EAAIR,cAAc,EAAIS,mBAAmB,CAC3F,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAG,EAAE,CACpB,IAAK,GAAI,CAAAtD,IAAI,CAAG,CAAC,CAAEA,IAAI,CAAG,EAAE,CAAEA,IAAI,EAAE,CAAE,CACpC,KAAM,CAAA8C,SAAS,CAAG,GAAG9C,IAAI,CAACuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,CAC1D,KAAM,CAAAiC,OAAO,CAAG,GAAGxD,IAAI,CAACuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,CACxD,KAAM,CAAAwB,OAAO,CAAG/C,IAAI,CAAG,EAAE,CAAG,GAAG,CAACA,IAAI,CAAG,CAAC,EAAEuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,CAAG,OAAO,CACpF+B,SAAS,CAACG,IAAI,CAAC,CACb5D,GAAG,CAAE,GAAGiD,SAAS,IAAIC,OAAO,EAAE,CAC9BW,KAAK,CAAEZ,SAAS,CAChBa,QAAQ,CAAEH,OAAO,CACjBxD,IAAI,CACJC,MAAM,CAAE,CAAC,CACT;AACA2D,SAAS,CAAE,GAAGd,SAAS,IAAIU,OAAO,EAAE,CACpCK,UAAU,CAAE7D,IAAI,CAAG,EAAE,CAAG,GAAGwD,OAAO,IAAIT,OAAO,EAAE,CAAG,aACpD,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAe,iBAAiB,CAAG,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CACxG,KAAM,CAAApE,cAAc,CAAGzB,UAAU,CAAGA,UAAU,CAACqE,GAAG,CAAChD,GAAG,GAAK,CACzDO,GAAG,CAAEP,GAAG,CACRoE,KAAK,CAAE/E,CAAC,CAAC,QAAQW,GAAG,EAAE,CACxB,CAAC,CAAC,CAAC,CAAGwE,iBAAiB,CAACxB,GAAG,CAAChD,GAAG,GAAK,CAClCO,GAAG,CAAEP,GAAG,CACRoE,KAAK,CAAE/E,CAAC,CAAC,QAAQW,GAAG,EAAE,CACxB,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAyE,qBAAqB,CAAIjD,MAAM,EAAK,CACxC,KAAM,CAAAkD,aAAa,CAAG,CACpBC,MAAM,CAAEtF,CAAC,CAAC,kBAAkB,CAAC,EAAI,KAAK,CACtCuF,MAAM,CAAEvF,CAAC,CAAC,kBAAkB,CAAC,EAAI,KAAK,CACtCwF,OAAO,CAAExF,CAAC,CAAC,mBAAmB,CAAC,EAAI,KAAK,CACxCyF,SAAS,CAAEzF,CAAC,CAAC,qBAAqB,CAAC,EAAI,KAAK,CAC5C0F,QAAQ,CAAE1F,CAAC,CAAC,oBAAoB,CAAC,EAAI,KAAK,CAC1C2F,MAAM,CAAE3F,CAAC,CAAC,kBAAkB,CAAC,EAAI,KAAK,CACtC4F,QAAQ,CAAE5F,CAAC,CAAC,oBAAoB,CAAC,EAAI,KACvC,CAAC,CACD,MAAO,CAAAqF,aAAa,CAAClD,MAAM,CAAC,EAAIA,MAAM,CAAC0D,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CACxD,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAIC,OAAO,EAAK,CACpC,KAAM,CAAAC,gBAAgB,CAAG,GAAI,CAAAtE,IAAI,CAACqE,OAAO,CAAC7C,QAAQ,CAAC,CACnD,KAAM,CAAA+C,cAAc,CAAG,GAAI,CAAAvE,IAAI,CAACqE,OAAO,CAAC7C,QAAQ,CAAC,CACjD+C,cAAc,CAACC,UAAU,CAACD,cAAc,CAACE,UAAU,CAAC,CAAC,CAAGC,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,CAAC,CACnF,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAA5E,IAAI,CAAC,CAAC,CAEtB,GAAIqE,OAAO,CAACQ,MAAM,GAAK,WAAW,CAAE,CAClC,MAAO,WAAW,CACpB,CAEA,GAAID,GAAG,EAAIN,gBAAgB,EAAIM,GAAG,CAAGL,cAAc,CAAE,CACnD,MAAO,SAAS,CAClB,CAEA,GAAIK,GAAG,EAAIL,cAAc,CAAE,CACzB,MAAO,WAAW,CACpB,CAEA,MAAO,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAO,yBAAyB,CAAIT,OAAO,EAAK,CAC7C,KAAM,CAAAQ,MAAM,CAAGT,gBAAgB,CAACC,OAAO,CAAC,CAExC,OAAQQ,MAAM,EACZ,IAAK,SAAS,CACZ,MAAO,CACLE,UAAU,CAAE,2BAA2BxJ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACE,IAAI,CAAE,IAAI,CAAC,QAAQ,CACrIC,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,EAAE,CAC7DG,eAAe,CAAE,2BAA2B9J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACE,IAAI,CAAE,CAAC,CAAC,QACjI,CAAC,CACH,IAAK,WAAW,CACd,MAAO,CACLJ,UAAU,CAAE,2BAA2BxJ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,QAAQ/J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,QAAQ,CAC/HF,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,EAAE,CAC1DD,eAAe,CAAE,2BAA2B9J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,QAAQ/J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC,QAC3H,CAAC,CACH,IAAK,WAAW,CACd,MAAO,CACLP,UAAU,CAAE,2BAA2BxJ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC4E,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC6E,IAAI,CAAE,IAAI,CAAC,QAAQ,CACjIC,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC4E,IAAI,CAAE,GAAG,CAAC,EAAE,CAC3DG,eAAe,CAAE,2BAA2B9J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC4E,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAAC1E,KAAK,CAAC6E,IAAI,CAAE,CAAC,CAAC,QAC7H,CAAC,CACH,QAAS;AACP,MAAO,CACLJ,UAAU,CAAE,2BAA2BxJ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACL,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACJ,IAAI,CAAE,IAAI,CAAC,QAAQ,CAC/HC,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACL,IAAI,CAAE,GAAG,CAAC,EAAE,CAC1DG,eAAe,CAAE,2BAA2B9J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACL,IAAI,CAAE,IAAI,CAAC,QAAQ3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACO,IAAI,CAACJ,IAAI,CAAE,CAAC,CAAC,QAC3H,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAK,mBAAmB,CAAGA,CAACvG,GAAG,CAAEC,QAAQ,GAAK,CAC7C,GAAI,CAACzB,QAAQ,EAAI,CAACE,gBAAgB,CAAE,MAAO,EAAE,CAE7C,KAAM,CAAAyB,QAAQ,CAAGC,cAAc,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKP,GAAG,CAAC,CAC7D,KAAM,CAAAQ,IAAI,CAAG7C,OAAO,CAACe,gBAAgB,CAAEyB,QAAQ,CAAC,CAChD,KAAM,CAAAqG,OAAO,CAAG9I,MAAM,CAAC8C,IAAI,CAAE,YAAY,CAAC,CAE1C,MAAO,CAAAhC,QAAQ,CAACiI,MAAM,CAACrB,OAAO,EAAI,CAChC;AACA,GAAI,CAAAsB,WAAW,CAAEC,WAAW,CAAEC,aAAa,CAE3C,GAAI9H,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C;AACA,KAAM,CAAA2F,iBAAiB,CAAG9I,2BAA2B,CAACqH,OAAO,CAAC7C,QAAQ,CAAEzD,cAAc,CAACoC,QAAQ,CAAE,qBAAqB,CAAC,CACvH,KAAM,CAAC4F,QAAQ,CAAEC,QAAQ,CAAC,CAAGF,iBAAiB,CAAChE,KAAK,CAAC,GAAG,CAAC,CACzD6D,WAAW,CAAGI,QAAQ,CAEtB,KAAM,CAACE,OAAO,CAAEC,SAAS,CAAC,CAAGF,QAAQ,CAAClE,KAAK,CAAC,GAAG,CAAC,CAChD8D,WAAW,CAAGlB,QAAQ,CAACuB,OAAO,CAAC,CAC/BJ,aAAa,CAAGnB,QAAQ,CAACwB,SAAS,CAAC,CACrC,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,eAAe,CAAG,GAAI,CAAAnG,IAAI,CAACqE,OAAO,CAAC7C,QAAQ,CAAC,CAClDmE,WAAW,CAAGhJ,MAAM,CAACwJ,eAAe,CAAE,YAAY,CAAC,CACnDP,WAAW,CAAGO,eAAe,CAACC,QAAQ,CAAC,CAAC,CACxCP,aAAa,CAAGM,eAAe,CAAC1B,UAAU,CAAC,CAAC,CAC9C,CAEA;AACA,KAAM,CAAArC,WAAW,CAAGuD,WAAW,GAAKF,OAAO,CAC3C,GAAI,CAACrD,WAAW,CAAE,MAAO,MAAK,CAE9B;AACA,KAAM,CAAAuC,QAAQ,CAAGD,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,EAAI,EAAE,CACjD,KAAM,CAAA0B,mBAAmB,CAAGT,WAAW,CAAG,EAAE,CAAGC,aAAa,CAC5D,KAAM,CAAAS,iBAAiB,CAAGD,mBAAmB,CAAG1B,QAAQ,CAExD;AACA,KAAM,CAAArC,gBAAgB,CAAGpD,QAAQ,CAACS,IAAI,CAAG,EAAE,CAC3C,KAAM,CAAA4C,cAAc,CAAGD,gBAAgB,CAAG,EAAE,CAE5C;AACA,GAAIqC,QAAQ,GAAK,EAAE,CAAE,CACnB;AACA,KAAM,CAAA4B,gBAAgB,CAAGF,mBAAmB,EAAI/D,gBAAgB,EAAI+D,mBAAmB,CAAG9D,cAAc,CAExG;AACA,KAAM,CAAAiE,mBAAmB,CAAGH,mBAAmB,CAAG/D,gBAAgB,EAAIgE,iBAAiB,CAAGhE,gBAAgB,CAE1G,MAAO,CAAAiE,gBAAgB,EAAIC,mBAAmB,CAChD,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,UAAU,CAAGnE,gBAAgB,CAAG,EAAE,CAExC;AACA,GAAI+D,mBAAmB,EAAI/D,gBAAgB,EAAI+D,mBAAmB,CAAGI,UAAU,CAAE,CAC/E;AACA,MAAO,CAAAJ,mBAAmB,EAAI/D,gBAAgB,EAAI+D,mBAAmB,CAAGI,UAAU,CACpF,CAAC,IAAM,IAAIJ,mBAAmB,EAAII,UAAU,EAAIJ,mBAAmB,CAAG9D,cAAc,CAAE,CACpF;AACA,MAAO,CAAA8D,mBAAmB,EAAII,UAAU,EAAIJ,mBAAmB,CAAG9D,cAAc,CAClF,CACF,CAEA,MAAO,MAAK,CACd,CAAC,CAAC,CACJ,CAAC,CAED,GAAI7E,OAAO,CAAE,CACX,mBACER,IAAA,CAACjC,GAAG,EAACyL,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cAC5D5J,IAAA,CAAC1B,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA,GAAI,CAACiC,QAAQ,EAAIA,QAAQ,CAAC2D,MAAM,GAAK,CAAC,CAAE,CACtC,mBACElE,IAAA,CAAC/B,KAAK,EAAC4L,SAAS,CAAE,CAAE,CAACL,EAAE,CAAE,CAAEM,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,YAAY,CAAE,CAAC,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAL,QAAA,cAC7E5J,IAAA,CAAChC,UAAU,EAACkM,OAAO,CAAC,IAAI,CAACC,KAAK,CAAC,gBAAgB,CAAAP,QAAA,CAC5CxI,CAAC,CAAC,qBAAqB,CAAC,CACf,CAAC,CACR,CAAC,CAEZ,CAEA,mBACElB,KAAA,CAACjC,KAAK,EAAC4L,SAAS,CAAE,CAAE,CAACL,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAC,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAElD5J,IAAA,CAACjC,GAAG,EAACyL,EAAE,CAAE,CACP3B,UAAU,CAAE,2BAA2BvG,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,QAAQ1G,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACnC,IAAI,QAAQ,CAC3G6B,CAAC,CAAE,CAAC,CACJK,KAAK,CAAE,OAAO,CACdV,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BW,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,MAAM,CAChBC,GAAG,CAAE,CACP,CAAE,CAAAX,QAAA,cACA1J,KAAA,CAACnC,GAAG,EAAA6L,QAAA,eACF1J,KAAA,CAAClC,UAAU,EAACkM,OAAO,CAAC,IAAI,CAACV,EAAE,CAAE,CAAEgB,UAAU,CAAE,MAAM,CAAET,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,EAAC,eACvD,CAACxI,CAAC,CAAC,sBAAsB,CAAC,EACnB,CAAC,cACbpB,IAAA,CAAChC,UAAU,EAACkM,OAAO,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEiB,OAAO,CAAE,GAAI,CAAE,CAAAb,QAAA,CAC9CxI,CAAC,CAAC,4BAA4B,CAAC,CACtB,CAAC,EACV,CAAC,CACH,CAAC,cAGNlB,KAAA,CAACnC,GAAG,EAACyL,EAAE,CAAE,CAAEkB,QAAQ,CAAE,MAAO,CAAE,CAAAd,QAAA,eAE5B1J,KAAA,CAACnC,GAAG,EAACyL,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfkB,mBAAmB,CAAE,CACnBC,EAAE,CAAE,mCAAmC,CACvCC,EAAE,CAAE,oCAAoC,CACxCC,EAAE,CAAE,qCACN,CAAC,CACDC,OAAO,CAAE1M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,IAAI,CAAC,CAChDgD,YAAY,CAAE,aAAa3M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,GAAG,CAAC,EAAE,CACnEiD,QAAQ,CAAE,QAAQ,CAClBC,GAAG,CAAE,CAAC,CACNC,MAAM,CAAE,EACV,CAAE,CAAAvB,QAAA,eACA5J,IAAA,CAACjC,GAAG,EAACyL,EAAE,CAAE,CACPM,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC9BM,SAAS,CAAE,CAAER,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CACjDrB,OAAO,CAAE,MAAM,CACfY,UAAU,CAAE,QAAQ,CACpBX,cAAc,CAAE,QAAQ,CACxB2B,WAAW,CAAE,aAAahN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,GAAG,CAAC,EAClE,CAAE,CAAA4B,QAAA,cACA1J,KAAA,CAAClC,UAAU,EAACkM,OAAO,CAAC,OAAO,CAACV,EAAE,CAAE,CAC9BgB,UAAU,CAAE,MAAM,CAClBL,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACwD,IAAI,CAACC,SAAS,CACnCC,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CACrD,CAAE,CAAAlB,QAAA,EAAC,SACC,CAACxI,CAAC,CAAC,cAAc,CAAC,EACV,CAAC,CACV,CAAC,CACLe,cAAc,CAAC4C,GAAG,CAAC,CAAChD,GAAG,CAAE0J,KAAK,GAAK,CAClC;AACA,KAAM,CAAAC,OAAO,CAAGjL,gBAAgB,CAAGf,OAAO,CAACe,gBAAgB,CAAEgL,KAAK,CAAC,CAAG,GAAI,CAAA3I,IAAI,CAAC,CAAC,CAEhF,mBACE5C,KAAA,CAACnC,GAAG,EAEFyL,EAAE,CAAE,CACFM,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC9BM,SAAS,CAAE,CAAER,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CACjDb,SAAS,CAAE,QAAQ,CACnBoB,WAAW,CAAE,aAAahN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,GAAG,CAAC,EAAE,CAClE,cAAc,CAAE,CAAEqD,WAAW,CAAE,MAAO,CAAC,CACvC5B,OAAO,CAAE,MAAM,CACfkC,aAAa,CAAE,QAAQ,CACvBjC,cAAc,CAAE,QAAQ,CACxBW,UAAU,CAAE,QACd,CAAE,CAAAT,QAAA,eAEF1J,KAAA,CAAClC,UAAU,EAACkM,OAAO,CAAC,IAAI,CAACV,EAAE,CAAE,CAC3BgB,UAAU,CAAE,MAAM,CAClBL,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CACjCwD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACpDc,UAAU,CAAE,GACd,CAAE,CAAAhC,QAAA,eAEA5J,IAAA,CAACjC,GAAG,EAAC8N,SAAS,CAAC,MAAM,CAACrC,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAEmB,EAAE,CAAE,MAAM,CAAEE,EAAE,CAAE,OAAQ,CAAE,CAAE,CAAAlB,QAAA,CAChE7H,GAAG,CAACoE,KAAK,CACP,CAAC,cACNnG,IAAA,CAACjC,GAAG,EAAC8N,SAAS,CAAC,MAAM,CAACrC,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAEmB,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,MAAO,CAAE,CAAE,CAAAlB,QAAA,CAC5E7H,GAAG,CAACoE,KAAK,CAACjC,MAAM,CAAG,CAAC,CAAGnC,GAAG,CAACoE,KAAK,CAACc,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAGlF,GAAG,CAACoE,KAAK,CAC1D,CAAC,cACNnG,IAAA,CAACjC,GAAG,EAAC8N,SAAS,CAAC,MAAM,CAACrC,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAEmB,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,MAAO,CAAE,CAAE,CAAAjB,QAAA,CAChEpD,qBAAqB,CAACzE,GAAG,CAACO,GAAG,CAAC,CAC5B,CAAC,EACI,CAAC,cAGbtC,IAAA,CAAChC,UAAU,EAACkM,OAAO,CAAC,SAAS,CAACV,EAAE,CAAE,CAChCC,OAAO,CAAE,OAAO,CAChBU,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACwD,IAAI,CAACC,SAAS,CACnCC,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtDgB,EAAE,CAAE,GACN,CAAE,CAAAlC,QAAA,CACCnK,MAAM,CAACiM,OAAO,CAAE,OAAO,CAAE,CAAEK,MAAM,CAAExK,KAAK,CAAG5B,EAAE,CAAGC,IAAK,CAAC,CAAC,CAC9C,CAAC,GAvCRmC,GAAG,CAACO,GAwCN,CAAC,CAEV,CAAC,CAAC,EACC,CAAC,cAGNtC,IAAA,CAACjC,GAAG,EAAA6L,QAAA,CACD7D,SAAS,CAAChB,GAAG,CAAC,CAAC/C,QAAQ,CAAEyJ,KAAK,GAAK,CAClC,KAAM,CAAAO,WAAW,CAAGhK,QAAQ,CAACU,MAAM,GAAK,CAAC,CACzC,mBACExC,KAAA,CAACnC,GAAG,EAEFyL,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfkB,mBAAmB,CAAE,CACnBC,EAAE,CAAE,mCAAmC,CACvCC,EAAE,CAAE,oCAAoC,CACxCC,EAAE,CAAE,qCACN,CAAC,CACDE,YAAY,CAAE,aAAa3M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACmE,OAAO,CAAED,WAAW,CAAG,GAAG,CAAG,GAAG,CAAC,EAAE,CAClFjB,OAAO,CAAEU,KAAK,CAAG,CAAC,CAAG,CAAC,CAAGpN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,IAAI,CAAC,CAAG,aAAa,CAChF,SAAS,CAAE,CACT+C,OAAO,CAAE1M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,IAAI,CACjD,CACF,CAAE,CAAA4B,QAAA,eAGF5J,IAAA,CAACjC,GAAG,EAACyL,EAAE,CAAE,CACPM,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC9BrB,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpBb,WAAW,CAAE,aAAahN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,GAAG,CAAC,EAAE,CAClE+C,OAAO,CAAEiB,WAAW,CAAG3N,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,IAAI,CAAC,CAAG,aAAa,CAC9EiD,QAAQ,CAAE,UACZ,CAAE,CAAArB,QAAA,cACA5J,IAAA,CAACjC,GAAG,EAACyL,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfkC,aAAa,CAAE,QAAQ,CACvBtB,UAAU,CAAE,QAAQ,CACpBE,GAAG,CAAE,GAAG,CACRU,QAAQ,CAAE,UAAU,CACpBkB,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7BlB,GAAG,CAAElJ,QAAQ,CAACU,MAAM,GAAK,CAAC,CAAG,MAAM,CAAG,kBACxC,CAAE,CAAAkH,QAAA,cAEA5J,IAAA,CAAChC,UAAU,EACTkM,OAAO,CAAC,OAAO,CACfV,EAAE,CAAE,CACFgB,UAAU,CAAE,MAAM,CAClBL,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CACjCwD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CAAAlB,QAAA,CAED5H,QAAQ,CAACmE,KAAK,CACL,CAAC,CACV,CAAC,CACH,CAAC,CAGLhE,cAAc,CAAC4C,GAAG,CAAEhD,GAAG,EAAK,CAC3B,KAAM,CAAAsK,iBAAiB,CAAG/D,mBAAmB,CAACvG,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAC,CAEhE;AACA;AACR,KAAM,CAAAsK,cAAc,CAAGD,iBAAiB,CAAC7D,MAAM,CAACrB,OAAO,EAAI,CACjD;AACA,GAAI,CAAAuB,WAAW,CAAG,CAAC,CACnB,GAAI,CAAAC,aAAa,CAAG,CAAC,CAErB,GAAI9H,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C,KAAM,CAAA2F,iBAAiB,CAAG9I,2BAA2B,CAACqH,OAAO,CAAC7C,QAAQ,CAAEzD,cAAc,CAACoC,QAAQ,CAAE,qBAAqB,CAAC,CACvH,KAAM,EAAG6F,QAAQ,CAAC,CAAGF,iBAAiB,CAAChE,KAAK,CAAC,GAAG,CAAC,CACjD,KAAM,CAACmE,OAAO,CAAEC,SAAS,CAAC,CAAGF,QAAQ,CAAClE,KAAK,CAAC,GAAG,CAAC,CAChD8D,WAAW,CAAGlB,QAAQ,CAACuB,OAAO,CAAC,CAC/BJ,aAAa,CAAGnB,QAAQ,CAACwB,SAAS,CAAC,CACrC,CAAC,IAAM,CACL,KAAM,CAAAP,WAAW,CAAG,GAAI,CAAA3F,IAAI,CAACqE,OAAO,CAAC7C,QAAQ,CAAC,CAC9CoE,WAAW,CAAGD,WAAW,CAACS,QAAQ,CAAC,CAAC,CACpCP,aAAa,CAAGF,WAAW,CAAClB,UAAU,CAAC,CAAC,CAC1C,CAEA,KAAM,CAAAE,QAAQ,CAAGD,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,EAAI,EAAE,CACjD,KAAM,CAAA0B,mBAAmB,CAAGT,WAAW,CAAG,EAAE,CAAGC,aAAa,CAC5D,KAAM,CAAA4D,uBAAuB,CAAGvK,QAAQ,CAACS,IAAI,CAAG,EAAE,CAElD;AACA;AACA,GAAIgF,QAAQ,GAAK,EAAE,CAAE,CACnB,KAAM,CAAA6B,mBAAmB,CAAGH,mBAAmB,CAAGoD,uBAAuB,CACzE,MAAO,CAACjD,mBAAmB,CAAE;AAC/B,CAEA,MAAO,KAAI,CAAE;AACf,CAAC,CAAC,CAEF;AACA;AACA;AACA,KAAM,CAAAkD,kBAAkB,CAAGF,cAAc,CAACG,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAK,CAC9D,KAAM,CAAArK,GAAG,CAAGqK,IAAI,CAACrI,QAAQ,CAAE;AAC3B,KAAM,CAAAsI,QAAQ,CAAGF,GAAG,CAACpK,GAAG,CAAC,CAEzB,GAAI,CAACsK,QAAQ,CAAE,CACbF,GAAG,CAACpK,GAAG,CAAC,CAAGqK,IAAI,CAAE;AACnB,CAAC,IAAM,CACL;AACA,GAAIC,QAAQ,CAACjF,MAAM,GAAK,WAAW,EAAIgF,IAAI,CAAChF,MAAM,GAAK,WAAW,CAAE,CAClE+E,GAAG,CAACpK,GAAG,CAAC,CAAGqK,IAAI,CACjB,CACA;AACA;AACF,CACA,MAAO,CAAAD,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAG,eAAe,CAAGC,MAAM,CAACC,MAAM,CAACP,kBAAkB,CAAC,CAEzD,mBACEtM,KAAA,CAACnC,GAAG,EAEFyL,EAAE,CAAE,CACFM,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CAChCM,SAAS,CAAE,CAAER,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,OAAQ,CAAC,CAAE;AACrDrB,OAAO,CAAE,MAAM,CACfY,UAAU,CAAE,QAAQ,CACpBX,cAAc,CAAE,QAAQ,CACxB2B,WAAW,CAAE,aAAahN,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CAAE,GAAG,CAAC,EAAE,CAClE,cAAc,CAAE,CAAEqD,WAAW,CAAE,MAAO,CAAC,CACvCJ,QAAQ,CAAE,UACZ,CAAE,CAAArB,QAAA,eAGF5J,IAAA,CAACjC,GAAG,EACFyL,EAAE,CAAE,CACFyB,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNiB,IAAI,CAAE,CAAC,CACPa,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,KAAK,CACblC,OAAO,CAAEzJ,KAAK,CAACwG,OAAO,CAACsC,OAAO,CAACpC,IAAI,CACnCmD,MAAM,CAAE,CAAC,CACTnB,YAAY,CAAE,KAAK,CACnBkD,SAAS,CAAE,2BAA2B,CACtCC,aAAa,CAAE,MACjB,CAAE,CACH,CAAC,cAEFnN,IAAA,CAACjC,GAAG,EACFyL,EAAE,CAAE,CACFyB,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,KAAK,CACViB,IAAI,CAAE,CAAC,CACPa,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,KAAK,CACblC,OAAO,CAAE1M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACmE,OAAO,CAAE,GAAG,CAAC,CAAE;AAC5Cd,MAAM,CAAE,CAAC,CAAE;AACXnB,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,CAED6C,eAAe,CAAC3I,MAAM,CAAG,CAAC,cACzBlE,IAAA,CAAAI,SAAA,EAAAwJ,QAAA,CACGiD,eAAe,CAAC9H,GAAG,CAAC,CAACoC,OAAO,CAAEiG,YAAY,GAAK,CAC9C,KAAM,CAAAC,aAAa,CAAGzF,yBAAyB,CAACT,OAAO,CAAC,CACxD,KAAM,CAAAmG,YAAY,CAAG9F,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,GAAK,EAAE,CAEtD;AACA,GAAI,CAAAkB,aAAa,CAAG,CAAC,CACrB,GAAI,CAAAD,WAAW,CAAG,CAAC,CACnB,GAAI7H,cAAc,EAAIA,cAAc,CAACoC,QAAQ,CAAE,CAC7C,KAAM,CAAA2F,iBAAiB,CAAG9I,2BAA2B,CAACqH,OAAO,CAAC7C,QAAQ,CAAEzD,cAAc,CAACoC,QAAQ,CAAE,qBAAqB,CAAC,CACvH,KAAM,EAAG6F,QAAQ,CAAC,CAAGF,iBAAiB,CAAChE,KAAK,CAAC,GAAG,CAAC,CACjD,KAAM,CAACmE,OAAO,CAAEC,SAAS,CAAC,CAAGF,QAAQ,CAAClE,KAAK,CAAC,GAAG,CAAC,CAChD8D,WAAW,CAAGlB,QAAQ,CAACuB,OAAO,CAAC,CAC/BJ,aAAa,CAAGnB,QAAQ,CAACwB,SAAS,CAAC,CACrC,CAAC,IAAM,CACL,KAAM,CAAAP,WAAW,CAAG,GAAI,CAAA3F,IAAI,CAACqE,OAAO,CAAC7C,QAAQ,CAAC,CAC9CoE,WAAW,CAAGD,WAAW,CAACS,QAAQ,CAAC,CAAC,CACpCP,aAAa,CAAGF,WAAW,CAAClB,UAAU,CAAC,CAAC,CAC1C,CAEA;AACA;AACA,GAAI,CAAAtF,WAAW,CAAG0G,aAAa,GAAK,CAAC,CAErC;AACA,GAAI2E,YAAY,CAAE,CAChB;AACA,KAAM,CAAAnE,mBAAmB,CAAGT,WAAW,CAAG,EAAE,CAAGC,aAAa,CAC5D,KAAM,CAAAS,iBAAiB,CAAGD,mBAAmB,CAAG,EAAE,CAAE;AACpD,KAAM,CAAAoD,uBAAuB,CAAGvK,QAAQ,CAACS,IAAI,CAAG,EAAE,CAClD,KAAM,CAAA8K,qBAAqB,CAAGhB,uBAAuB,CAAG,EAAE,CAE1D;AACA,KAAM,CAAAlD,gBAAgB,CAAGF,mBAAmB,EAAIoD,uBAAuB,EAAIpD,mBAAmB,CAAGoE,qBAAqB,CAEtH,GAAIlE,gBAAgB,CAAE,CACpB;AACA,KAAM,CAAAmE,iBAAiB,CAAGpE,iBAAiB,CAAGmE,qBAAqB,CACnE,GAAIC,iBAAiB,CAAE,CACrB;AACAvL,WAAW,CAAG,UAAU,CAAE;AAC5B,CAAC,IAAM,CACL;AACAA,WAAW,CAAG0G,aAAa,GAAK,CAAC,CAAG,IAAI,CAAG,gBAAgB,CAC7D,CACF,CACF,CAEA,mBACEzI,KAAA,CAACnC,GAAG,EAEFyL,EAAE,CAAE,CACFiE,KAAK,CAAE,KAAK,CACZR,MAAM,CAAE,CAAC,IAAM,CACb,GAAIhL,WAAW,GAAK,UAAU,CAAE,CAC9B;AACA,MAAO,MAAM,CAAE;AACjB,CACA,GAAIA,WAAW,GAAK,gBAAgB,CAAE,CACpC;AACA,MAAO,KAAK,CACd,CAEA,MAAO,CAAAqL,YAAY,CAAG,KAAK,CAAG,KAAK,CACrC,CAAC,EAAE,CAAC,CACJrC,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,IAAM,CACV,GAAIjJ,WAAW,GAAK,UAAU,CAAE,CAC9B;AACA,MAAO,KAAK,CACd,CACA,GAAIA,WAAW,GAAK,gBAAgB,CAAE,CACpC;AACA,MAAO,KAAK,CACd,CAEA,MAAO,CAAAqL,YAAY,CAAG,MAAM,CAAIrL,WAAW,CAAG,IAAI,CAAG,KAAM,CAC7D,CAAC,EAAE,CAAC,CACJkK,IAAI,CAAE,IAAI,CACVhB,MAAM,CAAE,CAAC,IAAM,CACb,GAAIlJ,WAAW,GAAK,UAAU,CAAE,CAC9B,MAAO,IAAG,CAAE;AACd,CAEA,MAAO,GAAE,CAAGmL,YAAY,CAAE;AAC5B,CAAC,EAAE,CAAC,CACJpD,YAAY,CAAE,CAAC,CACfnC,UAAU,CAAEwF,aAAa,CAACxF,UAAU,CACpC4B,OAAO,CAAE,MAAM,CACfkC,aAAa,CAAE,QAAQ,CACvBtB,UAAU,CAAE,QAAQ,CACpBX,cAAc,CAAE,QAAQ,CACxBa,GAAG,CAAE,CAAEK,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CAClChB,CAAC,CAAE,CAAEc,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CAChC4C,MAAM,CAAE,SAAS,CACjBxF,MAAM,CAAEmF,aAAa,CAACnF,MAAM,CAC5BgF,SAAS,CAAE,2BAA2B,CACtC,SAAS,CAAE,CACTrF,UAAU,CAAEwF,aAAa,CAAClF,eAAe,CACzCiE,SAAS,CAAE,kBAAkB,CAC7Bc,SAAS,CAAE,6BACb,CAAC,CACDS,UAAU,CAAE,sBAAsB,CAClCjD,QAAQ,CAAE,QACZ,CAAE,CACFkD,OAAO,CAAEA,CAAA,GAAMjN,aAAa,CAACwG,OAAO,CAAE,CAAAyC,QAAA,eAItC5J,IAAA,CAAChC,UAAU,EACTkM,OAAO,CAAC,OAAO,CACfV,EAAE,CAAE,CACFW,KAAK,CAAE,OAAO,CACdK,UAAU,CAAE,MAAM,CAClBgB,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,SAAU,CAAC,CACvDb,SAAS,CAAE,QAAQ,CACnB2B,UAAU,CAAE,GAAG,CACflB,QAAQ,CAAE,QAAQ,CAClBmD,YAAY,CAAE,UAAU,CACxBpE,OAAO,CAAE,aAAa,CACtBqE,eAAe,CAAE,CAAC,CAClBC,eAAe,CAAE,UAAU,CAC3BC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,2BAA2B,CACvClE,EAAE,CAAE,CAAEa,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAClC,CAAE,CAAAlB,QAAA,CAED5I,aAAa,CAAGmG,OAAO,CAAC+G,YAAY,CAAG/G,OAAO,CAACgH,YAAY,CAClD,CAAC,cAEbjO,KAAA,CAACnC,GAAG,EAACyL,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEc,GAAG,CAAE,CAAEK,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAE,CAAAlB,QAAA,eAC/D5J,IAAA,CAAC9B,MAAM,EACLkQ,IAAI,CAAC,OAAO,CACZlE,OAAO,CAAC,WAAW,CACnB0D,OAAO,CAAGS,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnB3N,aAAa,CAACwG,OAAO,CAAC,CACxB,CAAE,CACFqC,EAAE,CAAE,CACF+E,QAAQ,CAAE,MAAM,CAChBd,KAAK,CAAE,CAAE7C,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CACjCmC,MAAM,CAAE,CAAErC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAClChB,CAAC,CAAE,CAAC,CACJiB,OAAO,CAAE,0BAA0B,CACnCZ,KAAK,CAAE,OAAO,CACdH,YAAY,CAAE,GAAG,CACjB,SAAS,CAAE,CACTe,OAAO,CAAE,0BACX,CACF,CAAE,CAAAnB,QAAA,cAEF5J,IAAA,CAAClB,QAAQ,EAAC0K,EAAE,CAAE,CAAEgC,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAE,CAAE,CAAE,CAAC,CACtE,CAAC,CACR3D,OAAO,CAACQ,MAAM,GAAK,WAAW,EAAI/G,eAAe,EAAI,CAACI,aAAa,eAClEhB,IAAA,CAAC9B,MAAM,EACLkQ,IAAI,CAAC,OAAO,CACZlE,OAAO,CAAC,WAAW,CACnB0D,OAAO,CAAGS,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnB1N,eAAe,CAACuG,OAAO,CAAC,CAC1B,CAAE,CACFqC,EAAE,CAAE,CACF+E,QAAQ,CAAE,MAAM,CAChBd,KAAK,CAAE,CAAE7C,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CACjCmC,MAAM,CAAE,CAAErC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAClChB,CAAC,CAAE,CAAC,CACJiB,OAAO,CAAE,wBAAwB,CACjCZ,KAAK,CAAE,OAAO,CACdH,YAAY,CAAE,GAAG,CACjB,SAAS,CAAE,CACTe,OAAO,CAAE,sBACX,CACF,CAAE,CAAAnB,QAAA,cAEF5J,IAAA,CAAChB,UAAU,EAACwK,EAAE,CAAE,CAAEgC,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAE,CAAE,CAAE,CAAC,CACxE,CACT,EACE,CAAC,GA/HD,GAAG3D,OAAO,CAACqH,EAAE,IAAIpB,YAAY,EAgI/B,CAAC,CAEV,CAAC,CAAC,CACF,CAAC,cAEH;AACAlN,KAAA,CAACnC,GAAG,EAACyL,EAAE,CAAE,CACPiE,KAAK,CAAE,KAAK,CACZR,MAAM,CAAE,CAAErC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAQ,CAAC,CAC/Cd,YAAY,CAAE,GAAG,CACjB9B,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,EAAE,CAC1DqB,OAAO,CAAE,MAAM,CACfkC,aAAa,CAAE,QAAQ,CACvBjB,QAAQ,CAAE,QAAQ,CAClBS,MAAM,CAAE,CACV,CAAE,CAAAvB,QAAA,EAEC,CAAC,IAAM,CACN,KAAM,CAAA6E,kBAAkB,CAAGnL,uBAAuB,CAACvB,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,IAAI,CAAC,CAC3E,KAAM,CAAA0M,eAAe,CAAG9L,oBAAoB,CAACb,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,IAAI,CAAC,CAErE,mBACEhC,IAAA,CAACjC,GAAG,EAACyL,EAAE,CAAE,CACPmF,IAAI,CAAE,CAAC,CACPlF,OAAO,CAAE,MAAM,CACfY,UAAU,CAAE,QAAQ,CACpBX,cAAc,CAAE,QAAQ,CACxBsB,YAAY,CAAE,aAAa3M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,EAAE,CAChE2C,OAAO,CAAE2D,eAAe,CACpBrQ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,CACpCqG,kBAAkB,CAChBpQ,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvC3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,CACzCuF,UAAU,CAAE,eACd,CAAE,CAAA/D,QAAA,CACC8E,eAAe,cACd1O,IAAA,CAACzB,OAAO,EAACqQ,KAAK,CAAE,GAAG5M,QAAQ,CAACS,IAAI,CAACuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,SAAS5C,CAAC,CAAC,mBAAmB,CAAE,WAAW,CAAC,EAAG,CAACyN,KAAK,MAAAjF,QAAA,cAC/G5J,IAAA,CAACZ,wBAAwB,EACvBoK,EAAE,CAAE,CACFW,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAC9BoD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CACH,CAAC,CACK,CAAC,CACR2D,kBAAkB,cACpBvO,KAAA,CAACnC,GAAG,EAACyL,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEY,UAAU,CAAE,QAAQ,CAAEE,GAAG,CAAE,GAAI,CAAE,CAAAX,QAAA,eAC3D5J,IAAA,CAACzB,OAAO,EAACqQ,KAAK,CAAE,GAAG5M,QAAQ,CAACS,IAAI,CAACuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,SAAS5C,CAAC,CAAC,wBAAwB,CAAE,WAAW,CAAC,EAAG,CAACyN,KAAK,MAAAjF,QAAA,cACpH5J,IAAA,CAACV,eAAe,EACdkK,EAAE,CAAE,CACFW,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCwD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACpD4C,MAAM,CAAE,SACV,CAAE,CACH,CAAC,CACK,CAAC,CACTxM,WAAW,eACVlB,IAAA,CAACzB,OAAO,EAACqQ,KAAK,CAAExN,CAAC,CAAC,oBAAoB,CAAE,YAAY,CAAE,CAACyN,KAAK,MAAAjF,QAAA,cAC1D5J,IAAA,CAACpB,UAAU,EACTwP,IAAI,CAAC,OAAO,CACZR,OAAO,CAAEA,CAAA,GAAM9L,eAAe,CAACC,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,IAAI,CAAE,CACxDwH,EAAE,CAAE,CACFW,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACgH,OAAO,CAAC9G,IAAI,CACjCwD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtDiE,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,CACThE,OAAO,CAAE1M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACgH,OAAO,CAAC9G,IAAI,CAAE,GAAG,CAChD,CACF,CAAE,CAAA4B,QAAA,cAEF5J,IAAA,CAACR,QAAQ,EAACgK,EAAE,CAAE,CAAEgC,QAAQ,CAAE,SAAU,CAAE,CAAE,CAAC,CAC/B,CAAC,CACN,CACV,EACE,CAAC,cAENxL,IAAA,CAACZ,wBAAwB,EACvBoK,EAAE,CAAE,CACFW,KAAK,CAAE9L,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,CAC1CoD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CACH,CACF,CACE,CAAC,CAEV,CAAC,EAAE,CAAC,CAGH,CAAC,IAAM,CACN,KAAM,CAAAkE,mBAAmB,CAAG1L,uBAAuB,CAACvB,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,KAAK,CAAC,CAC7E,KAAM,CAAAiN,gBAAgB,CAAGrM,oBAAoB,CAACb,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,KAAK,CAAC,CAEvE,mBACEhC,IAAA,CAACjC,GAAG,EAACyL,EAAE,CAAE,CACPmF,IAAI,CAAE,CAAC,CACPlF,OAAO,CAAE,MAAM,CACfY,UAAU,CAAE,QAAQ,CACpBX,cAAc,CAAE,QAAQ,CACxBqB,OAAO,CAAEkE,gBAAgB,CACrB5Q,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,CACpC4G,mBAAmB,CACjB3Q,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvC3J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,CACzCuF,UAAU,CAAE,eACd,CAAE,CAAA/D,QAAA,CACCqF,gBAAgB,cACfjP,IAAA,CAACzB,OAAO,EAACqQ,KAAK,CAAE,GAAG5M,QAAQ,CAACS,IAAI,CAACuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAMhC,QAAQ,CAACS,IAAI,GAAK,EAAE,CAAG,QAAQ,CAAG,IAAI,CAACT,QAAQ,CAACS,IAAI,CAAG,CAAC,EAAEuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,MAAM5C,CAAC,CAAC,mBAAmB,CAAE,WAAW,CAAC,EAAG,CAACyN,KAAK,MAAAjF,QAAA,cAC5M5J,IAAA,CAACZ,wBAAwB,EACvBoK,EAAE,CAAE,CACFW,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAC9BoD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CACH,CAAC,CACK,CAAC,CACRkE,mBAAmB,cACrB9O,KAAA,CAACnC,GAAG,EAACyL,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEY,UAAU,CAAE,QAAQ,CAAEE,GAAG,CAAE,GAAI,CAAE,CAAAX,QAAA,eAC3D5J,IAAA,CAACzB,OAAO,EAACqQ,KAAK,CAAE,GAAG5M,QAAQ,CAACS,IAAI,CAACuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAMhC,QAAQ,CAACS,IAAI,GAAK,EAAE,CAAG,QAAQ,CAAG,IAAI,CAACT,QAAQ,CAACS,IAAI,CAAG,CAAC,EAAEuD,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,KAAK,MAAM5C,CAAC,CAAC,wBAAwB,CAAE,WAAW,CAAC,EAAG,CAACyN,KAAK,MAAAjF,QAAA,cACjN5J,IAAA,CAACV,eAAe,EACdkK,EAAE,CAAE,CACFW,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCwD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACpD4C,MAAM,CAAE,SACV,CAAE,CACH,CAAC,CACK,CAAC,CACTxM,WAAW,eACVlB,IAAA,CAACzB,OAAO,EAACqQ,KAAK,CAAExN,CAAC,CAAC,oBAAoB,CAAE,YAAY,CAAE,CAACyN,KAAK,MAAAjF,QAAA,cAC1D5J,IAAA,CAACpB,UAAU,EACTwP,IAAI,CAAC,OAAO,CACZR,OAAO,CAAEA,CAAA,GAAM9L,eAAe,CAACC,GAAG,CAACO,GAAG,CAAEN,QAAQ,CAAE,KAAK,CAAE,CACzDwH,EAAE,CAAE,CACFW,KAAK,CAAE7I,KAAK,CAACwG,OAAO,CAACgH,OAAO,CAAC9G,IAAI,CACjCwD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtDiE,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,CACThE,OAAO,CAAE1M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACgH,OAAO,CAAC9G,IAAI,CAAE,GAAG,CAChD,CACF,CAAE,CAAA4B,QAAA,cAEF5J,IAAA,CAACR,QAAQ,EAACgK,EAAE,CAAE,CAAEgC,QAAQ,CAAE,SAAU,CAAE,CAAE,CAAC,CAC/B,CAAC,CACN,CACV,EACE,CAAC,cAENxL,IAAA,CAACZ,wBAAwB,EACvBoK,EAAE,CAAE,CACFW,KAAK,CAAE9L,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAE,GAAG,CAAC,CAC1CoD,QAAQ,CAAE,CAAEZ,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CACvD,CAAE,CACH,CACF,CACE,CAAC,CAEV,CAAC,EAAE,CAAC,EACD,CACN,GAvXI,GAAG/I,GAAG,CAACO,GAAG,IAAIN,QAAQ,CAACM,GAAG,EAwX5B,CAAC,CAEV,CAAC,CAAC,GAveGN,QAAQ,CAACM,GAweX,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CAAC,cAGNpC,KAAA,CAAC1B,MAAM,EACL0Q,IAAI,CAAEzN,eAAgB,CACtB0N,OAAO,CAAEA,CAAA,GAAMzN,kBAAkB,CAAC,KAAK,CAAE,CACzCsM,QAAQ,CAAC,IAAI,CACboB,SAAS,MAAAxF,QAAA,eAET1J,KAAA,CAACzB,WAAW,EAAC+K,EAAE,CAAE,CAAES,SAAS,CAAE,QAAQ,CAAEoF,EAAE,CAAE,CAAE,CAAE,CAAAzF,QAAA,eAC9C5J,IAAA,CAACR,QAAQ,EAACgK,EAAE,CAAE,CAAEgC,QAAQ,CAAE,MAAM,CAAErB,KAAK,CAAE,cAAc,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACpE/J,IAAA,CAAChC,UAAU,EAACkM,OAAO,CAAC,IAAI,CAAC2B,SAAS,CAAC,KAAK,CAAAjC,QAAA,CACrCxI,CAAC,CAAC,yBAAyB,CAAE,oBAAoB,CAAC,CACzC,CAAC,EACF,CAAC,cAEdpB,IAAA,CAACtB,aAAa,EAAC8K,EAAE,CAAE,CAAES,SAAS,CAAE,QAAQ,CAAEqF,EAAE,CAAE,CAAE,CAAE,CAAA1F,QAAA,CAC/ChI,iBAAiB,eAChB1B,KAAA,CAACnC,GAAG,EAAA6L,QAAA,eACF5J,IAAA,CAAChC,UAAU,EAACkM,OAAO,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CACvCxI,CAAC,CAAC,2BAA2B,CAAE,6BAA6B,CAAC,CACpD,CAAC,cAEblB,KAAA,CAACnC,GAAG,EAACyL,EAAE,CAAE,CACPuB,OAAO,CAAE1M,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACgH,OAAO,CAAC9G,IAAI,CAAE,GAAG,CAAC,CAC/C8B,CAAC,CAAE,CAAC,CACJE,YAAY,CAAE,CAAC,CACf9B,MAAM,CAAE,aAAa7J,KAAK,CAACiD,KAAK,CAACwG,OAAO,CAACgH,OAAO,CAAC9G,IAAI,CAAE,GAAG,CAAC,EAC7D,CAAE,CAAA4B,QAAA,eACA1J,KAAA,CAAClC,UAAU,EAACkM,OAAO,CAAC,IAAI,CAACV,EAAE,CAAE,CAAEW,KAAK,CAAE,cAAc,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,EAAC,eAC1D,CAACnK,MAAM,CAACmC,iBAAiB,CAACW,IAAI,CAAE,mBAAmB,CAAE,CAAEwJ,MAAM,CAAExK,KAAK,CAAG5B,EAAE,CAAGC,IAAK,CAAC,CAAC,EAC5E,CAAC,cACbM,KAAA,CAAClC,UAAU,EAACkM,OAAO,CAAC,IAAI,CAACV,EAAE,CAAE,CAAEgB,UAAU,CAAE,MAAO,CAAE,CAAAZ,QAAA,EAAC,eAChD,CAAC,CAAC,IAAM,CACT,KAAM,CAAAnE,SAAS,CAAG7D,iBAAiB,CAACa,IAAI,CACxC,KAAM,CAAAiD,WAAW,CAAG9D,iBAAiB,CAACc,MAAM,CAC5C,KAAM,CAAAkD,SAAS,CAAGF,WAAW,CAAG,EAAE,CAClC,KAAM,CAAAC,OAAO,CAAGC,SAAS,EAAI,EAAE,CAAGH,SAAS,CAAG,CAAC,CAAGA,SAAS,CAC3D,KAAM,CAAA8J,cAAc,CAAG3J,SAAS,EAAI,EAAE,CAAGA,SAAS,CAAG,EAAE,CAAGA,SAAS,CAEnE,MAAO,GAAGH,SAAS,CAACO,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAI0B,WAAW,CAACM,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAM2B,OAAO,CAACK,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIuL,cAAc,CAACvJ,QAAQ,CAAC,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACrL,CAAC,EAAE,CAAC,EACM,CAAC,EACV,CAAC,cAENhE,IAAA,CAAChC,UAAU,EAACkM,OAAO,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEsC,EAAE,CAAE,CAAC,CAAE3B,KAAK,CAAE,gBAAiB,CAAE,CAAAP,QAAA,CAChExI,CAAC,CAAC,wBAAwB,CAAE,iDAAiD,CAAC,CACrE,CAAC,EACV,CACN,CACY,CAAC,cAEhBlB,KAAA,CAACvB,aAAa,EAAC6K,EAAE,CAAE,CAAEE,cAAc,CAAE,QAAQ,CAAE2F,EAAE,CAAE,CAAE,CAAE,CAAAzF,QAAA,eACrD5J,IAAA,CAAC9B,MAAM,EACL0P,OAAO,CAAEA,CAAA,GAAMlM,kBAAkB,CAAC,KAAK,CAAE,CACzCwI,OAAO,CAAC,UAAU,CAClBV,EAAE,CAAE,CAAE+E,QAAQ,CAAE,GAAI,CAAE,CAAA3E,QAAA,CAErBxI,CAAC,CAAC,eAAe,CAAE,OAAO,CAAC,CACtB,CAAC,cACTpB,IAAA,CAAC9B,MAAM,EACL0P,OAAO,CAAEjL,gBAAiB,CAC1BuH,OAAO,CAAC,WAAW,CACnBC,KAAK,CAAC,SAAS,CACfX,EAAE,CAAE,CAAE+E,QAAQ,CAAE,GAAG,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAA5F,QAAA,CAE5BxI,CAAC,CAAC,gBAAgB,CAAE,OAAO,CAAC,CACvB,CAAC,EACI,CAAC,EACV,CAAC,EACJ,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAf,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}