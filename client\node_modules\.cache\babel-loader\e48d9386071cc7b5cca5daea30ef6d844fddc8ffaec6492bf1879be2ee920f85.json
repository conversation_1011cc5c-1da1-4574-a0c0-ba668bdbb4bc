{"ast": null, "code": "import { useEffect as f, useRef as s } from \"react\";\nimport { useEvent as i } from './use-event.js';\nfunction m(u, t) {\n  let e = s([]),\n    r = i(u);\n  f(() => {\n    let o = [...e.current];\n    for (let [a, l] of t.entries()) if (e.current[a] !== l) {\n      let n = r(t, o);\n      return e.current = t, n;\n    }\n  }, [r, ...t]);\n}\nexport { m as useWatch };", "map": {"version": 3, "names": ["useEffect", "f", "useRef", "s", "useEvent", "i", "m", "u", "t", "e", "r", "o", "current", "a", "l", "entries", "n", "useWatch"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/hooks/use-watch.js"], "sourcesContent": ["import{useEffect as f,useRef as s}from\"react\";import{useEvent as i}from'./use-event.js';function m(u,t){let e=s([]),r=i(u);f(()=>{let o=[...e.current];for(let[a,l]of t.entries())if(e.current[a]!==l){let n=r(t,o);return e.current=t,n}},[r,...t])}export{m as useWatch};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACN,CAAC,CAAC,EAAE,CAAC;IAACO,CAAC,GAACL,CAAC,CAACE,CAAC,CAAC;EAACN,CAAC,CAAC,MAAI;IAAC,IAAIU,CAAC,GAAC,CAAC,GAAGF,CAAC,CAACG,OAAO,CAAC;IAAC,KAAI,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,IAAGN,CAAC,CAACO,OAAO,CAAC,CAAC,EAAC,IAAGN,CAAC,CAACG,OAAO,CAACC,CAAC,CAAC,KAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACN,CAAC,CAACF,CAAC,EAACG,CAAC,CAAC;MAAC,OAAOF,CAAC,CAACG,OAAO,GAACJ,CAAC,EAACQ,CAAC;IAAA;EAAC,CAAC,EAAC,CAACN,CAAC,EAAC,GAAGF,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}