{"ast": null, "code": "import { isElementVisible as $645f2e67b85a24c9$export$e989c0fffaa6b27a } from \"./isElementVisible.mjs\";\nimport { useLayoutEffect as $cgawC$useLayoutEffect, getActiveElement as $cgawC$getActiveElement, getOwnerDocument as $cgawC$getOwnerDocument, getEventTarget as $cgawC$getEventTarget, isAndroid as $cgawC$isAndroid, isChrome as $cgawC$isChrome, isTabbable as $cgawC$isTabbable, isFocusable as $cgawC$isFocusable, createShadowTreeWalker as $cgawC$createShadowTreeWalker } from \"@react-aria/utils\";\nimport { getInteractionModality as $cgawC$getInteractionModality, focusSafely as $cgawC$focusSafely } from \"@react-aria/interactions\";\nimport $cgawC$react, { useRef as $cgawC$useRef, useContext as $cgawC$useContext, useMemo as $cgawC$useMemo, useEffect as $cgawC$useEffect } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nconst $9bf71ea28793e738$var$FocusContext = /*#__PURE__*/(0, $cgawC$react).createContext(null);\nconst $9bf71ea28793e738$var$RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\nlet $9bf71ea28793e738$var$activeScope = null;\nfunction $9bf71ea28793e738$export$20e40289641fbbb6(props) {\n  let {\n    children: children,\n    contain: contain,\n    restoreFocus: restoreFocus,\n    autoFocus: autoFocus\n  } = props;\n  let startRef = (0, $cgawC$useRef)(null);\n  let endRef = (0, $cgawC$useRef)(null);\n  let scopeRef = (0, $cgawC$useRef)([]);\n  let {\n    parentNode: parentNode\n  } = (0, $cgawC$useContext)($9bf71ea28793e738$var$FocusContext) || {};\n  // Create a tree node here so we can add children to it even before it is added to the tree.\n  let node = (0, $cgawC$useMemo)(() => new $9bf71ea28793e738$var$TreeNode({\n    scopeRef: scopeRef\n  }), [scopeRef]);\n  (0, $cgawC$useLayoutEffect)(() => {\n    // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n    // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n    // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n    // that is being added should get the activeScope as its parent.\n    let parent = parentNode || $9bf71ea28793e738$export$d06fae2ee68b101e.root;\n    if ($9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(parent.scopeRef) && $9bf71ea28793e738$var$activeScope && !$9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, parent.scopeRef)) {\n      let activeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n      if (activeNode) parent = activeNode;\n    }\n    // Add the node to the parent, and to the tree.\n    parent.addChild(node);\n    $9bf71ea28793e738$export$d06fae2ee68b101e.addNode(node);\n  }, [node, parentNode]);\n  (0, $cgawC$useLayoutEffect)(() => {\n    let node = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n    if (node) node.contain = !!contain;\n  }, [contain]);\n  (0, $cgawC$useLayoutEffect)(() => {\n    var _startRef_current;\n    // Find all rendered nodes between the sentinels and add them to the scope.\n    let node = (_startRef_current = startRef.current) === null || _startRef_current === void 0 ? void 0 : _startRef_current.nextSibling;\n    let nodes = [];\n    let stopPropagation = e => e.stopPropagation();\n    while (node && node !== endRef.current) {\n      nodes.push(node);\n      // Stop custom restore focus event from propagating to parent focus scopes.\n      node.addEventListener($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, stopPropagation);\n      node = node.nextSibling;\n    }\n    scopeRef.current = nodes;\n    return () => {\n      for (let node of nodes) node.removeEventListener($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, stopPropagation);\n    };\n  }, [children]);\n  $9bf71ea28793e738$var$useActiveScopeTracker(scopeRef, restoreFocus, contain);\n  $9bf71ea28793e738$var$useFocusContainment(scopeRef, contain);\n  $9bf71ea28793e738$var$useRestoreFocus(scopeRef, restoreFocus, contain);\n  $9bf71ea28793e738$var$useAutoFocus(scopeRef, autoFocus);\n  // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n  // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n  (0, $cgawC$useEffect)(() => {\n    const activeElement = (0, $cgawC$getActiveElement)((0, $cgawC$getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined));\n    let scope = null;\n    if ($9bf71ea28793e738$var$isElementInScope(activeElement, scopeRef.current)) {\n      // We need to traverse the focusScope tree and find the bottom most scope that\n      // contains the active element and set that as the activeScope.\n      for (let node of $9bf71ea28793e738$export$d06fae2ee68b101e.traverse()) if (node.scopeRef && $9bf71ea28793e738$var$isElementInScope(activeElement, node.scopeRef.current)) scope = node;\n      if (scope === $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef)) $9bf71ea28793e738$var$activeScope = scope.scopeRef;\n    }\n  }, [scopeRef]);\n  // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n  // in useRestoreFocus cleanup runs.\n  (0, $cgawC$useLayoutEffect)(() => {\n    return () => {\n      var _focusScopeTree_getTreeNode_parent, _focusScopeTree_getTreeNode;\n      var _focusScopeTree_getTreeNode_parent_scopeRef;\n      // Scope may have been re-parented.\n      let parentScope = (_focusScopeTree_getTreeNode_parent_scopeRef = (_focusScopeTree_getTreeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef)) === null || _focusScopeTree_getTreeNode === void 0 ? void 0 : (_focusScopeTree_getTreeNode_parent = _focusScopeTree_getTreeNode.parent) === null || _focusScopeTree_getTreeNode_parent === void 0 ? void 0 : _focusScopeTree_getTreeNode_parent.scopeRef) !== null && _focusScopeTree_getTreeNode_parent_scopeRef !== void 0 ? _focusScopeTree_getTreeNode_parent_scopeRef : null;\n      if ((scopeRef === $9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope(scopeRef, $9bf71ea28793e738$var$activeScope)) && (!parentScope || $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(parentScope))) $9bf71ea28793e738$var$activeScope = parentScope;\n      $9bf71ea28793e738$export$d06fae2ee68b101e.removeTreeNode(scopeRef);\n    };\n  }, [scopeRef]);\n  let focusManager = (0, $cgawC$useMemo)(() => $9bf71ea28793e738$var$createFocusManagerForScope(scopeRef), []);\n  let value = (0, $cgawC$useMemo)(() => ({\n    focusManager: focusManager,\n    parentNode: node\n  }), [node, focusManager]);\n  return /*#__PURE__*/(0, $cgawC$react).createElement($9bf71ea28793e738$var$FocusContext.Provider, {\n    value: value\n  }, /*#__PURE__*/(0, $cgawC$react).createElement(\"span\", {\n    \"data-focus-scope-start\": true,\n    hidden: true,\n    ref: startRef\n  }), children, /*#__PURE__*/(0, $cgawC$react).createElement(\"span\", {\n    \"data-focus-scope-end\": true,\n    hidden: true,\n    ref: endRef\n  }));\n}\nfunction $9bf71ea28793e738$export$10c5169755ce7bd7() {\n  var _useContext;\n  return (_useContext = (0, $cgawC$useContext)($9bf71ea28793e738$var$FocusContext)) === null || _useContext === void 0 ? void 0 : _useContext.focusManager;\n}\nfunction $9bf71ea28793e738$var$createFocusManagerForScope(scopeRef) {\n  return {\n    focusNext(opts = {}) {\n      let scope = scopeRef.current;\n      let {\n        from: from,\n        tabbable: tabbable,\n        wrap: wrap,\n        accept: accept\n      } = opts;\n      var _scope_;\n      let node = from || (0, $cgawC$getActiveElement)((0, $cgawC$getOwnerDocument)((_scope_ = scope[0]) !== null && _scope_ !== void 0 ? _scope_ : undefined));\n      let sentinel = scope[0].previousElementSibling;\n      let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n        tabbable: tabbable,\n        accept: accept\n      }, scope);\n      walker.currentNode = $9bf71ea28793e738$var$isElementInScope(node, scope) ? node : sentinel;\n      let nextNode = walker.nextNode();\n      if (!nextNode && wrap) {\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode();\n      }\n      if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n      return nextNode;\n    },\n    focusPrevious(opts = {}) {\n      let scope = scopeRef.current;\n      let {\n        from: from,\n        tabbable: tabbable,\n        wrap: wrap,\n        accept: accept\n      } = opts;\n      var _scope_;\n      let node = from || (0, $cgawC$getActiveElement)((0, $cgawC$getOwnerDocument)((_scope_ = scope[0]) !== null && _scope_ !== void 0 ? _scope_ : undefined));\n      let sentinel = scope[scope.length - 1].nextElementSibling;\n      let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n        tabbable: tabbable,\n        accept: accept\n      }, scope);\n      walker.currentNode = $9bf71ea28793e738$var$isElementInScope(node, scope) ? node : sentinel;\n      let previousNode = walker.previousNode();\n      if (!previousNode && wrap) {\n        walker.currentNode = sentinel;\n        previousNode = walker.previousNode();\n      }\n      if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n      return previousNode;\n    },\n    focusFirst(opts = {}) {\n      let scope = scopeRef.current;\n      let {\n        tabbable: tabbable,\n        accept: accept\n      } = opts;\n      let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n        tabbable: tabbable,\n        accept: accept\n      }, scope);\n      walker.currentNode = scope[0].previousElementSibling;\n      let nextNode = walker.nextNode();\n      if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n      return nextNode;\n    },\n    focusLast(opts = {}) {\n      let scope = scopeRef.current;\n      let {\n        tabbable: tabbable,\n        accept: accept\n      } = opts;\n      let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n        tabbable: tabbable,\n        accept: accept\n      }, scope);\n      walker.currentNode = scope[scope.length - 1].nextElementSibling;\n      let previousNode = walker.previousNode();\n      if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n      return previousNode;\n    }\n  };\n}\nfunction $9bf71ea28793e738$var$getScopeRoot(scope) {\n  return scope[0].parentElement;\n}\nfunction $9bf71ea28793e738$var$shouldContainFocus(scopeRef) {\n  let scope = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.contain) return false;\n    scope = scope.parent;\n  }\n  return true;\n}\nfunction $9bf71ea28793e738$var$useFocusContainment(scopeRef, contain) {\n  let focusedNode = (0, $cgawC$useRef)(undefined);\n  let raf = (0, $cgawC$useRef)(undefined);\n  (0, $cgawC$useLayoutEffect)(() => {\n    let scope = scopeRef.current;\n    if (!contain) {\n      // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n        raf.current = undefined;\n      }\n      return;\n    }\n    const ownerDocument = (0, $cgawC$getOwnerDocument)(scope ? scope[0] : undefined);\n    // Handle the Tab key to contain focus within the scope\n    let onKeyDown = e => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !$9bf71ea28793e738$var$shouldContainFocus(scopeRef) || e.isComposing) return;\n      let focusedElement = (0, $cgawC$getActiveElement)(ownerDocument);\n      let scope = scopeRef.current;\n      if (!scope || !$9bf71ea28793e738$var$isElementInScope(focusedElement, scope)) return;\n      let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n        tabbable: true\n      }, scope);\n      if (!focusedElement) return;\n      walker.currentNode = focusedElement;\n      let nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n      if (!nextElement) {\n        walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling : scope[0].previousElementSibling;\n        nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n      }\n      e.preventDefault();\n      if (nextElement) $9bf71ea28793e738$var$focusElement(nextElement, true);\n    };\n    let onFocus = e => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!$9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, scopeRef)) && $9bf71ea28793e738$var$isElementInScope((0, $cgawC$getEventTarget)(e), scopeRef.current)) {\n        $9bf71ea28793e738$var$activeScope = scopeRef;\n        focusedNode.current = (0, $cgawC$getEventTarget)(e);\n      } else if ($9bf71ea28793e738$var$shouldContainFocus(scopeRef) && !$9bf71ea28793e738$var$isElementInChildScope((0, $cgawC$getEventTarget)(e), scopeRef)) {\n        // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n        // restore focus to the previously focused node or the first tabbable element in the active scope.\n        if (focusedNode.current) focusedNode.current.focus();else if ($9bf71ea28793e738$var$activeScope && $9bf71ea28793e738$var$activeScope.current) $9bf71ea28793e738$var$focusFirstInScope($9bf71ea28793e738$var$activeScope.current);\n      } else if ($9bf71ea28793e738$var$shouldContainFocus(scopeRef)) focusedNode.current = (0, $cgawC$getEventTarget)(e);\n    };\n    let onBlur = e => {\n      // Firefox doesn't shift focus back to the Dialog properly without this\n      if (raf.current) cancelAnimationFrame(raf.current);\n      raf.current = requestAnimationFrame(() => {\n        // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n        // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n        // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n        let modality = (0, $cgawC$getInteractionModality)();\n        let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && (0, $cgawC$isAndroid)() && (0, $cgawC$isChrome)();\n        // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n        let activeElement = (0, $cgawC$getActiveElement)(ownerDocument);\n        if (!shouldSkipFocusRestore && activeElement && $9bf71ea28793e738$var$shouldContainFocus(scopeRef) && !$9bf71ea28793e738$var$isElementInChildScope(activeElement, scopeRef)) {\n          $9bf71ea28793e738$var$activeScope = scopeRef;\n          let target = (0, $cgawC$getEventTarget)(e);\n          if (target && target.isConnected) {\n            var _focusedNode_current;\n            focusedNode.current = target;\n            (_focusedNode_current = focusedNode.current) === null || _focusedNode_current === void 0 ? void 0 : _focusedNode_current.focus();\n          } else if ($9bf71ea28793e738$var$activeScope.current) $9bf71ea28793e738$var$focusFirstInScope($9bf71ea28793e738$var$activeScope.current);\n        }\n      });\n    };\n    ownerDocument.addEventListener('keydown', onKeyDown, false);\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope === null || scope === void 0 ? void 0 : scope.forEach(element => element.addEventListener('focusin', onFocus, false));\n    scope === null || scope === void 0 ? void 0 : scope.forEach(element => element.addEventListener('focusout', onBlur, false));\n    return () => {\n      ownerDocument.removeEventListener('keydown', onKeyDown, false);\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope === null || scope === void 0 ? void 0 : scope.forEach(element => element.removeEventListener('focusin', onFocus, false));\n      scope === null || scope === void 0 ? void 0 : scope.forEach(element => element.removeEventListener('focusout', onBlur, false));\n    };\n  }, [scopeRef, contain]);\n  // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n  (0, $cgawC$useLayoutEffect)(() => {\n    return () => {\n      if (raf.current) cancelAnimationFrame(raf.current);\n    };\n  }, [raf]);\n}\nfunction $9bf71ea28793e738$var$isElementInAnyScope(element) {\n  return $9bf71ea28793e738$var$isElementInChildScope(element);\n}\nfunction $9bf71ea28793e738$var$isElementInScope(element, scope) {\n  if (!element) return false;\n  if (!scope) return false;\n  return scope.some(node => node.contains(element));\n}\nfunction $9bf71ea28793e738$var$isElementInChildScope(element, scope = null) {\n  // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n  if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) return true;\n  // node.contains in isElementInScope covers child scopes that are also DOM children,\n  // but does not cover child scopes in portals.\n  for (let {\n    scopeRef: s\n  } of $9bf71ea28793e738$export$d06fae2ee68b101e.traverse($9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scope))) {\n    if (s && $9bf71ea28793e738$var$isElementInScope(element, s.current)) return true;\n  }\n  return false;\n}\nfunction $9bf71ea28793e738$export$1258395f99bf9cbf(element) {\n  return $9bf71ea28793e738$var$isElementInChildScope(element, $9bf71ea28793e738$var$activeScope);\n}\nfunction $9bf71ea28793e738$var$isAncestorScope(ancestor, scope) {\n  var _focusScopeTree_getTreeNode;\n  let parent = (_focusScopeTree_getTreeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scope)) === null || _focusScopeTree_getTreeNode === void 0 ? void 0 : _focusScopeTree_getTreeNode.parent;\n  while (parent) {\n    if (parent.scopeRef === ancestor) return true;\n    parent = parent.parent;\n  }\n  return false;\n}\nfunction $9bf71ea28793e738$var$focusElement(element, scroll = false) {\n  if (element != null && !scroll) try {\n    (0, $cgawC$focusSafely)(element);\n  } catch {\n    // ignore\n  } else if (element != null) try {\n    element.focus();\n  } catch {\n    // ignore\n  }\n}\nfunction $9bf71ea28793e738$var$getFirstInScope(scope, tabbable = true) {\n  let sentinel = scope[0].previousElementSibling;\n  let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n  let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n    tabbable: tabbable\n  }, scope);\n  walker.currentNode = sentinel;\n  let nextNode = walker.nextNode();\n  // If the scope does not contain a tabbable element, use the first focusable element.\n  if (tabbable && !nextNode) {\n    scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n    walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n      tabbable: false\n    }, scope);\n    walker.currentNode = sentinel;\n    nextNode = walker.nextNode();\n  }\n  return nextNode;\n}\nfunction $9bf71ea28793e738$var$focusFirstInScope(scope, tabbable = true) {\n  $9bf71ea28793e738$var$focusElement($9bf71ea28793e738$var$getFirstInScope(scope, tabbable));\n}\nfunction $9bf71ea28793e738$var$useAutoFocus(scopeRef, autoFocus) {\n  const autoFocusRef = (0, $cgawC$react).useRef(autoFocus);\n  (0, $cgawC$useEffect)(() => {\n    if (autoFocusRef.current) {\n      $9bf71ea28793e738$var$activeScope = scopeRef;\n      const ownerDocument = (0, $cgawC$getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n      if (!$9bf71ea28793e738$var$isElementInScope((0, $cgawC$getActiveElement)(ownerDocument), $9bf71ea28793e738$var$activeScope.current) && scopeRef.current) $9bf71ea28793e738$var$focusFirstInScope(scopeRef.current);\n    }\n    autoFocusRef.current = false;\n  }, [scopeRef]);\n}\nfunction $9bf71ea28793e738$var$useActiveScopeTracker(scopeRef, restore, contain) {\n  // tracks the active scope, in case restore and contain are both false.\n  // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n  (0, $cgawC$useLayoutEffect)(() => {\n    if (restore || contain) return;\n    let scope = scopeRef.current;\n    const ownerDocument = (0, $cgawC$getOwnerDocument)(scope ? scope[0] : undefined);\n    let onFocus = e => {\n      let target = (0, $cgawC$getEventTarget)(e);\n      if ($9bf71ea28793e738$var$isElementInScope(target, scopeRef.current)) $9bf71ea28793e738$var$activeScope = scopeRef;else if (!$9bf71ea28793e738$var$isElementInAnyScope(target)) $9bf71ea28793e738$var$activeScope = null;\n    };\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope === null || scope === void 0 ? void 0 : scope.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope === null || scope === void 0 ? void 0 : scope.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n  }, [scopeRef, restore, contain]);\n}\nfunction $9bf71ea28793e738$var$shouldRestoreFocus(scopeRef) {\n  let scope = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.nodeToRestore) return false;\n    scope = scope.parent;\n  }\n  return (scope === null || scope === void 0 ? void 0 : scope.scopeRef) === scopeRef;\n}\nfunction $9bf71ea28793e738$var$useRestoreFocus(scopeRef, restoreFocus, contain) {\n  // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n  // eslint-disable-next-line no-restricted-globals\n  const nodeToRestoreRef = (0, $cgawC$useRef)(typeof document !== 'undefined' ? (0, $cgawC$getActiveElement)((0, $cgawC$getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined)) : null);\n  // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n  // restoring-non-containing scopes should only care if they become active so they can perform the restore\n  (0, $cgawC$useLayoutEffect)(() => {\n    let scope = scopeRef.current;\n    const ownerDocument = (0, $cgawC$getOwnerDocument)(scope ? scope[0] : undefined);\n    if (!restoreFocus || contain) return;\n    let onFocus = () => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!$9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, scopeRef)) && $9bf71ea28793e738$var$isElementInScope((0, $cgawC$getActiveElement)(ownerDocument), scopeRef.current)) $9bf71ea28793e738$var$activeScope = scopeRef;\n    };\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope === null || scope === void 0 ? void 0 : scope.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope === null || scope === void 0 ? void 0 : scope.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [scopeRef, contain]);\n  (0, $cgawC$useLayoutEffect)(() => {\n    const ownerDocument = (0, $cgawC$getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n    if (!restoreFocus) return;\n    // Handle the Tab key so that tabbing out of the scope goes to the next element\n    // after the node that had focus when the scope mounted. This is important when\n    // using portals for overlays, so that focus goes to the expected element when\n    // tabbing out of the overlay.\n    let onKeyDown = e => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !$9bf71ea28793e738$var$shouldContainFocus(scopeRef) || e.isComposing) return;\n      let focusedElement = ownerDocument.activeElement;\n      if (!$9bf71ea28793e738$var$isElementInChildScope(focusedElement, scopeRef) || !$9bf71ea28793e738$var$shouldRestoreFocus(scopeRef)) return;\n      let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n      if (!treeNode) return;\n      let nodeToRestore = treeNode.nodeToRestore;\n      // Create a DOM tree walker that matches all tabbable elements\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(ownerDocument.body, {\n        tabbable: true\n      });\n      // Find the next tabbable element after the currently focused element\n      walker.currentNode = focusedElement;\n      let nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n      if (!nodeToRestore || !nodeToRestore.isConnected || nodeToRestore === ownerDocument.body) {\n        nodeToRestore = undefined;\n        treeNode.nodeToRestore = undefined;\n      }\n      // If there is no next element, or it is outside the current scope, move focus to the\n      // next element after the node to restore to instead.\n      if ((!nextElement || !$9bf71ea28793e738$var$isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n        walker.currentNode = nodeToRestore;\n        // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n        do nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode(); while ($9bf71ea28793e738$var$isElementInChildScope(nextElement, scopeRef));\n        e.preventDefault();\n        e.stopPropagation();\n        if (nextElement) $9bf71ea28793e738$var$focusElement(nextElement, true);else\n          // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n          // then move focus to the body.\n          // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n          if (!$9bf71ea28793e738$var$isElementInAnyScope(nodeToRestore)) focusedElement.blur();else $9bf71ea28793e738$var$focusElement(nodeToRestore, true);\n      }\n    };\n    if (!contain) ownerDocument.addEventListener('keydown', onKeyDown, true);\n    return () => {\n      if (!contain) ownerDocument.removeEventListener('keydown', onKeyDown, true);\n    };\n  }, [scopeRef, restoreFocus, contain]);\n  // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n  (0, $cgawC$useLayoutEffect)(() => {\n    const ownerDocument = (0, $cgawC$getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n    if (!restoreFocus) return;\n    let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n    if (!treeNode) return;\n    var _nodeToRestoreRef_current;\n    treeNode.nodeToRestore = (_nodeToRestoreRef_current = nodeToRestoreRef.current) !== null && _nodeToRestoreRef_current !== void 0 ? _nodeToRestoreRef_current : undefined;\n    return () => {\n      let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n      if (!treeNode) return;\n      let nodeToRestore = treeNode.nodeToRestore;\n      // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n      let activeElement = (0, $cgawC$getActiveElement)(ownerDocument);\n      if (restoreFocus && nodeToRestore && (activeElement && $9bf71ea28793e738$var$isElementInChildScope(activeElement, scopeRef) || activeElement === ownerDocument.body && $9bf71ea28793e738$var$shouldRestoreFocus(scopeRef))) {\n        // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n        let clonedTree = $9bf71ea28793e738$export$d06fae2ee68b101e.clone();\n        requestAnimationFrame(() => {\n          // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n          if (ownerDocument.activeElement === ownerDocument.body) {\n            // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n            let treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                $9bf71ea28793e738$var$restoreFocusToElement(treeNode.nodeToRestore);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n            // If no nodeToRestore was found, focus the first element in the nearest\n            // ancestor scope that is still in the tree.\n            treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.scopeRef && treeNode.scopeRef.current && $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(treeNode.scopeRef)) {\n                let node = $9bf71ea28793e738$var$getFirstInScope(treeNode.scopeRef.current, true);\n                $9bf71ea28793e738$var$restoreFocusToElement(node);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n          }\n        });\n      }\n    };\n  }, [scopeRef, restoreFocus]);\n}\nfunction $9bf71ea28793e738$var$restoreFocusToElement(node) {\n  // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n  // For example, virtualized collection components reuse DOM elements, so the original element\n  // might still exist in the DOM but representing a different item.\n  if (node.dispatchEvent(new CustomEvent($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, {\n    bubbles: true,\n    cancelable: true\n  }))) $9bf71ea28793e738$var$focusElement(node);\n}\nfunction $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, opts, scope) {\n  let filter = (opts === null || opts === void 0 ? void 0 : opts.tabbable) ? (0, $cgawC$isTabbable) : (0, $cgawC$isFocusable);\n  // Ensure that root is an Element or fall back appropriately\n  let rootElement = (root === null || root === void 0 ? void 0 : root.nodeType) === Node.ELEMENT_NODE ? root : null;\n  // Determine the document to use\n  let doc = (0, $cgawC$getOwnerDocument)(rootElement);\n  // Create a TreeWalker, ensuring the root is an Element or Document\n  let walker = (0, $cgawC$createShadowTreeWalker)(doc, root || doc, NodeFilter.SHOW_ELEMENT, {\n    acceptNode(node) {\n      var _opts_from;\n      // Skip nodes inside the starting node.\n      if (opts === null || opts === void 0 ? void 0 : (_opts_from = opts.from) === null || _opts_from === void 0 ? void 0 : _opts_from.contains(node)) return NodeFilter.FILTER_REJECT;\n      if (filter(node) && (0, $645f2e67b85a24c9$export$e989c0fffaa6b27a)(node) && (!scope || $9bf71ea28793e738$var$isElementInScope(node, scope)) && (!(opts === null || opts === void 0 ? void 0 : opts.accept) || opts.accept(node))) return NodeFilter.FILTER_ACCEPT;\n      return NodeFilter.FILTER_SKIP;\n    }\n  });\n  if (opts === null || opts === void 0 ? void 0 : opts.from) walker.currentNode = opts.from;\n  return walker;\n}\nfunction $9bf71ea28793e738$export$c5251b9e124bf29(ref, defaultOptions = {}) {\n  return {\n    focusNext(opts = {}) {\n      let root = ref.current;\n      if (!root) return null;\n      let {\n        from: from,\n        tabbable = defaultOptions.tabbable,\n        wrap = defaultOptions.wrap,\n        accept = defaultOptions.accept\n      } = opts;\n      let node = from || (0, $cgawC$getActiveElement)((0, $cgawC$getOwnerDocument)(root));\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n        tabbable: tabbable,\n        accept: accept\n      });\n      if (root.contains(node)) walker.currentNode = node;\n      let nextNode = walker.nextNode();\n      if (!nextNode && wrap) {\n        walker.currentNode = root;\n        nextNode = walker.nextNode();\n      }\n      if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n      return nextNode;\n    },\n    focusPrevious(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) return null;\n      let {\n        from: from,\n        tabbable = defaultOptions.tabbable,\n        wrap = defaultOptions.wrap,\n        accept = defaultOptions.accept\n      } = opts;\n      let node = from || (0, $cgawC$getActiveElement)((0, $cgawC$getOwnerDocument)(root));\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n        tabbable: tabbable,\n        accept: accept\n      });\n      if (root.contains(node)) walker.currentNode = node;else {\n        let next = $9bf71ea28793e738$var$last(walker);\n        if (next) $9bf71ea28793e738$var$focusElement(next, true);\n        return next !== null && next !== void 0 ? next : null;\n      }\n      let previousNode = walker.previousNode();\n      if (!previousNode && wrap) {\n        walker.currentNode = root;\n        let lastNode = $9bf71ea28793e738$var$last(walker);\n        if (!lastNode)\n          // couldn't wrap\n          return null;\n        previousNode = lastNode;\n      }\n      if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n      return previousNode !== null && previousNode !== void 0 ? previousNode : null;\n    },\n    focusFirst(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) return null;\n      let {\n        tabbable = defaultOptions.tabbable,\n        accept = defaultOptions.accept\n      } = opts;\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n        tabbable: tabbable,\n        accept: accept\n      });\n      let nextNode = walker.nextNode();\n      if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n      return nextNode;\n    },\n    focusLast(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) return null;\n      let {\n        tabbable = defaultOptions.tabbable,\n        accept = defaultOptions.accept\n      } = opts;\n      let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n        tabbable: tabbable,\n        accept: accept\n      });\n      let next = $9bf71ea28793e738$var$last(walker);\n      if (next) $9bf71ea28793e738$var$focusElement(next, true);\n      return next !== null && next !== void 0 ? next : null;\n    }\n  };\n}\nfunction $9bf71ea28793e738$var$last(walker) {\n  let next = undefined;\n  let last;\n  do {\n    last = walker.lastChild();\n    if (last) next = last;\n  } while (last);\n  return next;\n}\nclass $9bf71ea28793e738$var$Tree {\n  get size() {\n    return this.fastMap.size;\n  }\n  getTreeNode(data) {\n    return this.fastMap.get(data);\n  }\n  addTreeNode(scopeRef, parent, nodeToRestore) {\n    let parentNode = this.fastMap.get(parent !== null && parent !== void 0 ? parent : null);\n    if (!parentNode) return;\n    let node = new $9bf71ea28793e738$var$TreeNode({\n      scopeRef: scopeRef\n    });\n    parentNode.addChild(node);\n    node.parent = parentNode;\n    this.fastMap.set(scopeRef, node);\n    if (nodeToRestore) node.nodeToRestore = nodeToRestore;\n  }\n  addNode(node) {\n    this.fastMap.set(node.scopeRef, node);\n  }\n  removeTreeNode(scopeRef) {\n    // never remove the root\n    if (scopeRef === null) return;\n    let node = this.fastMap.get(scopeRef);\n    if (!node) return;\n    let parentNode = node.parent;\n    // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n    // if we are, then replace the siblings restore with the restore from the scope we're removing\n    for (let current of this.traverse()) if (current !== node && node.nodeToRestore && current.nodeToRestore && node.scopeRef && node.scopeRef.current && $9bf71ea28793e738$var$isElementInScope(current.nodeToRestore, node.scopeRef.current)) current.nodeToRestore = node.nodeToRestore;\n    let children = node.children;\n    if (parentNode) {\n      parentNode.removeChild(node);\n      if (children.size > 0) children.forEach(child => parentNode && parentNode.addChild(child));\n    }\n    this.fastMap.delete(node.scopeRef);\n  }\n  // Pre Order Depth First\n  *traverse(node = this.root) {\n    if (node.scopeRef != null) yield node;\n    if (node.children.size > 0) for (let child of node.children) yield* this.traverse(child);\n  }\n  clone() {\n    var _node_parent;\n    let newTree = new $9bf71ea28793e738$var$Tree();\n    var _node_parent_scopeRef;\n    for (let node of this.traverse()) newTree.addTreeNode(node.scopeRef, (_node_parent_scopeRef = (_node_parent = node.parent) === null || _node_parent === void 0 ? void 0 : _node_parent.scopeRef) !== null && _node_parent_scopeRef !== void 0 ? _node_parent_scopeRef : null, node.nodeToRestore);\n    return newTree;\n  }\n  constructor() {\n    this.fastMap = new Map();\n    this.root = new $9bf71ea28793e738$var$TreeNode({\n      scopeRef: null\n    });\n    this.fastMap.set(null, this.root);\n  }\n}\nclass $9bf71ea28793e738$var$TreeNode {\n  addChild(node) {\n    this.children.add(node);\n    node.parent = this;\n  }\n  removeChild(node) {\n    this.children.delete(node);\n    node.parent = undefined;\n  }\n  constructor(props) {\n    this.children = new Set();\n    this.contain = false;\n    this.scopeRef = props.scopeRef;\n  }\n}\nlet $9bf71ea28793e738$export$d06fae2ee68b101e = new $9bf71ea28793e738$var$Tree();\nexport { $9bf71ea28793e738$export$20e40289641fbbb6 as FocusScope, $9bf71ea28793e738$export$d06fae2ee68b101e as focusScopeTree, $9bf71ea28793e738$export$10c5169755ce7bd7 as useFocusManager, $9bf71ea28793e738$export$2d6ec8fc375ceafa as getFocusableTreeWalker, $9bf71ea28793e738$export$1258395f99bf9cbf as isElementInChildOfActiveScope, $9bf71ea28793e738$export$c5251b9e124bf29 as createFocusManager };", "map": {"version": 3, "names": ["$9bf71ea28793e738$var$FocusContext", "$cgawC$react", "createContext", "$9bf71ea28793e738$var$RESTORE_FOCUS_EVENT", "$9bf71ea28793e738$var$activeScope", "$9bf71ea28793e738$export$20e40289641fbbb6", "props", "children", "contain", "restoreFocus", "autoFocus", "startRef", "$cgawC$useRef", "endRef", "scopeRef", "parentNode", "$cgawC$useContext", "node", "$cgawC$useMemo", "$9bf71ea28793e738$var$TreeNode", "$cgawC$useLayoutEffect", "parent", "$9bf71ea28793e738$export$d06fae2ee68b101e", "root", "getTreeNode", "$9bf71ea28793e738$var$isAncestorScope", "activeNode", "<PERSON><PERSON><PERSON><PERSON>", "addNode", "_startRef_current", "current", "nextS<PERSON>ling", "nodes", "stopPropagation", "e", "push", "addEventListener", "removeEventListener", "$9bf71ea28793e738$var$useActiveScopeTracker", "$9bf71ea28793e738$var$useFocusContainment", "$9bf71ea28793e738$var$useRestoreFocus", "$9bf71ea28793e738$var$useAutoFocus", "$cgawC$useEffect", "activeElement", "$cgawC$getActiveElement", "$cgawC$getOwnerDocument", "undefined", "scope", "$9bf71ea28793e738$var$isElementInScope", "traverse", "_focusScopeTree_getTreeNode_parent", "_focusScopeTree_getTreeNode", "_focusScopeTree_getTreeNode_parent_scopeRef", "parentScope", "removeTreeNode", "focusManager", "$9bf71ea28793e738$var$createFocusManagerForScope", "value", "createElement", "Provider", "hidden", "ref", "$9bf71ea28793e738$export$10c5169755ce7bd7", "_useContext", "focusNext", "opts", "from", "tabbable", "wrap", "accept", "_scope_", "sentinel", "previousElementSibling", "scopeRoot", "$9bf71ea28793e738$var$getScopeRoot", "walker", "$9bf71ea28793e738$export$2d6ec8fc375ceafa", "currentNode", "nextNode", "$9bf71ea28793e738$var$focusElement", "focusPrevious", "length", "nextElement<PERSON><PERSON>ling", "previousNode", "focusFirst", "focusLast", "parentElement", "$9bf71ea28793e738$var$shouldContainFocus", "focusedNode", "raf", "cancelAnimationFrame", "ownerDocument", "onKeyDown", "key", "altKey", "ctrl<PERSON>ey", "metaKey", "isComposing", "focusedElement", "nextElement", "shift<PERSON>ey", "preventDefault", "onFocus", "$cgawC$getEventTarget", "$9bf71ea28793e738$var$isElementInChildScope", "focus", "$9bf71ea28793e738$var$focusFirstInScope", "onBlur", "requestAnimationFrame", "modality", "$cgawC$getInteractionModality", "shouldSkipFocusRestore", "$cgawC$isAndroid", "$cgawC$isChrome", "target", "isConnected", "_focusedNode_current", "for<PERSON>ach", "element", "$9bf71ea28793e738$var$isElementInAnyScope", "some", "contains", "Element", "closest", "s", "$9bf71ea28793e738$export$1258395f99bf9cbf", "ancestor", "scroll", "$cgawC$focusSafely", "$9bf71ea28793e738$var$getFirstInScope", "autoFocusRef", "useRef", "restore", "$9bf71ea28793e738$var$shouldRestoreFocus", "nodeToRestore", "nodeToRestoreRef", "document", "treeNode", "body", "blur", "_nodeToRestoreRef_current", "cloned<PERSON>ree", "clone", "$9bf71ea28793e738$var$restoreFocusToElement", "dispatchEvent", "CustomEvent", "bubbles", "cancelable", "filter", "$cgawC$isTabbable", "$cgawC$isFocusable", "rootElement", "nodeType", "Node", "ELEMENT_NODE", "doc", "$cgawC$createShadowTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "_opts_from", "FILTER_REJECT", "$645f2e67b85a24c9$export$e989c0fffaa6b27a", "FILTER_ACCEPT", "FILTER_SKIP", "$9bf71ea28793e738$export$c5251b9e124bf29", "defaultOptions", "next", "$9bf71ea28793e738$var$last", "lastNode", "last", "<PERSON><PERSON><PERSON><PERSON>", "$9bf71ea28793e738$var$Tree", "size", "fastMap", "data", "get", "addTreeNode", "set", "<PERSON><PERSON><PERSON><PERSON>", "child", "delete", "_node_parent", "newTree", "_node_parent_scopeRef", "constructor", "Map", "add", "Set"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\focus\\dist\\packages\\@react-aria\\focus\\src\\FocusScope.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  createShadowTreeWalker,\n  getActiveElement,\n  getEventTarget,\n  getOwnerDocument,\n  isAndroid,\n  isChrome,\n  isFocusable,\n  isTabbable,\n  ShadowTreeWalker,\n  useLayoutEffect\n} from '@react-aria/utils';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {focusSafely, getInteractionModality} from '@react-aria/interactions';\nimport {isElementVisible} from './isElementVisible';\nimport React, {JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\nexport interface FocusScopeProps {\n  /** The contents of the focus scope. */\n  children: ReactNode,\n\n  /**\n   * Whether to contain focus inside the scope, so users cannot\n   * move focus outside, for example in a modal dialog.\n   */\n  contain?: boolean,\n\n  /**\n   * Whether to restore focus back to the element that was focused\n   * when the focus scope mounted, after the focus scope unmounts.\n   */\n  restoreFocus?: boolean,\n\n  /** Whether to auto focus the first focusable element in the focus scope on mount. */\n  autoFocus?: boolean\n}\n\nexport interface FocusManagerOptions {\n  /** The element to start searching from. The currently focused element by default. */\n  from?: Element,\n  /** Whether to only include tabbable elements, or all focusable elements. */\n  tabbable?: boolean,\n  /** Whether focus should wrap around when it reaches the end of the scope. */\n  wrap?: boolean,\n  /** A callback that determines whether the given element is focused. */\n  accept?: (node: Element) => boolean\n}\n\nexport interface FocusManager {\n  /** Moves focus to the next focusable or tabbable element in the focus scope. */\n  focusNext(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the previous focusable or tabbable element in the focus scope. */\n  focusPrevious(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the first focusable or tabbable element in the focus scope. */\n  focusFirst(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the last focusable or tabbable element in the focus scope. */\n  focusLast(opts?: FocusManagerOptions): FocusableElement | null\n}\n\ntype ScopeRef = RefObject<Element[] | null> | null;\ninterface IFocusContext {\n  focusManager: FocusManager,\n  parentNode: TreeNode | null\n}\n\nconst FocusContext = React.createContext<IFocusContext | null>(null);\nconst RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\n\nlet activeScope: ScopeRef = null;\n\n// This is a hacky DOM-based implementation of a FocusScope until this RFC lands in React:\n// https://github.com/reactjs/rfcs/pull/109\n\n/**\n * A FocusScope manages focus for its descendants. It supports containing focus inside\n * the scope, restoring focus to the previously focused element on unmount, and auto\n * focusing children on mount. It also acts as a container for a programmatic focus\n * management interface that can be used to move focus forward and back in response\n * to user events.\n */\nexport function FocusScope(props: FocusScopeProps): JSX.Element {\n  let {children, contain, restoreFocus, autoFocus} = props;\n  let startRef = useRef<HTMLSpanElement>(null);\n  let endRef = useRef<HTMLSpanElement>(null);\n  let scopeRef = useRef<Element[]>([]);\n  let {parentNode} = useContext(FocusContext) || {};\n\n  // Create a tree node here so we can add children to it even before it is added to the tree.\n  let node = useMemo(() => new TreeNode({scopeRef}), [scopeRef]);\n\n  useLayoutEffect(() => {\n    // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n    // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n    // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n    // that is being added should get the activeScope as its parent.\n    let parent = parentNode || focusScopeTree.root;\n    if (focusScopeTree.getTreeNode(parent.scopeRef) && activeScope && !isAncestorScope(activeScope, parent.scopeRef)) {\n      let activeNode = focusScopeTree.getTreeNode(activeScope);\n      if (activeNode) {\n        parent = activeNode;\n      }\n    }\n\n    // Add the node to the parent, and to the tree.\n    parent.addChild(node);\n    focusScopeTree.addNode(node);\n  }, [node, parentNode]);\n\n  useLayoutEffect(() => {\n    let node = focusScopeTree.getTreeNode(scopeRef);\n    if (node) {\n      node.contain = !!contain;\n    }\n  }, [contain]);\n\n  useLayoutEffect(() => {\n    // Find all rendered nodes between the sentinels and add them to the scope.\n    let node = startRef.current?.nextSibling!;\n    let nodes: Element[] = [];\n    let stopPropagation = e => e.stopPropagation();\n    while (node && node !== endRef.current) {\n      nodes.push(node as Element);\n      // Stop custom restore focus event from propagating to parent focus scopes.\n      node.addEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      node = node.nextSibling as Element;\n    }\n\n    scopeRef.current = nodes;\n\n    return () => {\n      for (let node of nodes) {\n        node.removeEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      }\n    };\n  }, [children]);\n\n  useActiveScopeTracker(scopeRef, restoreFocus, contain);\n  useFocusContainment(scopeRef, contain);\n  useRestoreFocus(scopeRef, restoreFocus, contain);\n  useAutoFocus(scopeRef, autoFocus);\n\n  // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n  // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n  useEffect(() => {\n    const activeElement = getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined));\n    let scope: TreeNode | null = null;\n\n    if (isElementInScope(activeElement, scopeRef.current)) {\n      // We need to traverse the focusScope tree and find the bottom most scope that\n      // contains the active element and set that as the activeScope.\n      for (let node of focusScopeTree.traverse()) {\n        if (node.scopeRef && isElementInScope(activeElement, node.scopeRef.current)) {\n          scope = node;\n        }\n      }\n\n      if (scope === focusScopeTree.getTreeNode(scopeRef)) {\n        activeScope = scope.scopeRef;\n      }\n    }\n  }, [scopeRef]);\n\n  // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n  // in useRestoreFocus cleanup runs.\n  useLayoutEffect(() => {\n    return () => {\n      // Scope may have been re-parented.\n      let parentScope = focusScopeTree.getTreeNode(scopeRef)?.parent?.scopeRef ?? null;\n\n      if (\n        (scopeRef === activeScope || isAncestorScope(scopeRef, activeScope)) &&\n        (!parentScope || focusScopeTree.getTreeNode(parentScope))\n      ) {\n        activeScope = parentScope;\n      }\n      focusScopeTree.removeTreeNode(scopeRef);\n    };\n  }, [scopeRef]);\n\n  let focusManager = useMemo(() => createFocusManagerForScope(scopeRef), []);\n  let value = useMemo(() => ({\n    focusManager,\n    parentNode: node\n  }), [node, focusManager]);\n\n  return (\n    <FocusContext.Provider value={value}>\n      <span data-focus-scope-start hidden ref={startRef} />\n      {children}\n      <span data-focus-scope-end hidden ref={endRef} />\n    </FocusContext.Provider>\n  );\n}\n\n/**\n * Returns a FocusManager interface for the parent FocusScope.\n * A FocusManager can be used to programmatically move focus within\n * a FocusScope, e.g. in response to user events like keyboard navigation.\n */\nexport function useFocusManager(): FocusManager | undefined {\n  return useContext(FocusContext)?.focusManager;\n}\n\nfunction createFocusManagerForScope(scopeRef: React.RefObject<Element[] | null>): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[0].previousElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node : sentinel;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[scope.length - 1].nextElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node  : sentinel;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = sentinel;\n        previousNode = walker.previousNode() as FocusableElement;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    },\n    focusFirst(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[0].previousElementSibling!;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[scope.length - 1].nextElementSibling!;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    }\n  };\n}\n\nfunction getScopeRoot(scope: Element[]) {\n  return scope[0].parentElement!;\n}\n\nfunction shouldContainFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.contain) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return true;\n}\n\nfunction useFocusContainment(scopeRef: RefObject<Element[] | null>, contain?: boolean) {\n  let focusedNode = useRef<FocusableElement>(undefined);\n\n  let raf = useRef<ReturnType<typeof requestAnimationFrame>>(undefined);\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    if (!contain) {\n      // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n        raf.current = undefined;\n      }\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    // Handle the Tab key to contain focus within the scope\n    let onKeyDown = (e) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = getActiveElement(ownerDocument);\n      let scope = scopeRef.current;\n      if (!scope || !isElementInScope(focusedElement, scope)) {\n        return;\n      }\n\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable: true}, scope);\n      if (!focusedElement) {\n        return;\n      }\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      if (!nextElement) {\n        walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling! : scope[0].previousElementSibling!;\n        nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      }\n\n      e.preventDefault();\n      if (nextElement) {\n        focusElement(nextElement, true);\n      }\n    };\n\n    let onFocus: EventListener = (e) => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) && isElementInScope(getEventTarget(e) as Element, scopeRef.current)) {\n        activeScope = scopeRef;\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      } else if (shouldContainFocus(scopeRef) && !isElementInChildScope(getEventTarget(e) as Element, scopeRef)) {\n        // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n        // restore focus to the previously focused node or the first tabbable element in the active scope.\n        if (focusedNode.current) {\n          focusedNode.current.focus();\n        } else if (activeScope && activeScope.current) {\n          focusFirstInScope(activeScope.current);\n        }\n      } else if (shouldContainFocus(scopeRef)) {\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      }\n    };\n\n    let onBlur: EventListener = (e) => {\n      // Firefox doesn't shift focus back to the Dialog properly without this\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n      raf.current = requestAnimationFrame(() => {\n        // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n        // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n        // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n        let modality = getInteractionModality();\n        let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && isAndroid() && isChrome();\n\n        // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n        let activeElement = getActiveElement(ownerDocument);\n        if (!shouldSkipFocusRestore && activeElement && shouldContainFocus(scopeRef) && !isElementInChildScope(activeElement, scopeRef)) {\n          activeScope = scopeRef;\n          let target = getEventTarget(e) as FocusableElement;\n          if (target && target.isConnected) {\n            focusedNode.current = target;\n            focusedNode.current?.focus();\n          } else if (activeScope.current) {\n            focusFirstInScope(activeScope.current);\n          }\n        }\n      });\n    };\n\n    ownerDocument.addEventListener('keydown', onKeyDown, false);\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    scope?.forEach(element => element.addEventListener('focusout', onBlur, false));\n    return () => {\n      ownerDocument.removeEventListener('keydown', onKeyDown, false);\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n      scope?.forEach(element => element.removeEventListener('focusout', onBlur, false));\n    };\n  }, [scopeRef, contain]);\n\n  // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n\n  useLayoutEffect(() => {\n    return () => {\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n    };\n  }, [raf]);\n}\n\nfunction isElementInAnyScope(element: Element) {\n  return isElementInChildScope(element);\n}\n\nfunction isElementInScope(element?: Element | null, scope?: Element[] | null) {\n  if (!element) {\n    return false;\n  }\n  if (!scope) {\n    return false;\n  }\n  return scope.some(node => node.contains(element));\n}\n\nfunction isElementInChildScope(element: Element, scope: ScopeRef = null) {\n  // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n  if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) {\n    return true;\n  }\n\n  // node.contains in isElementInScope covers child scopes that are also DOM children,\n  // but does not cover child scopes in portals.\n  for (let {scopeRef: s} of focusScopeTree.traverse(focusScopeTree.getTreeNode(scope))) {\n    if (s && isElementInScope(element, s.current)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/** @private */\nexport function isElementInChildOfActiveScope(element: Element): boolean {\n  return isElementInChildScope(element, activeScope);\n}\n\nfunction isAncestorScope(ancestor: ScopeRef, scope: ScopeRef) {\n  let parent = focusScopeTree.getTreeNode(scope)?.parent;\n  while (parent) {\n    if (parent.scopeRef === ancestor) {\n      return true;\n    }\n    parent = parent.parent;\n  }\n  return false;\n}\n\nfunction focusElement(element: FocusableElement | null, scroll = false) {\n  if (element != null && !scroll) {\n    try {\n      focusSafely(element);\n    } catch {\n      // ignore\n    }\n  } else if (element != null) {\n    try {\n      element.focus();\n    } catch {\n      // ignore\n    }\n  }\n}\n\nfunction getFirstInScope(scope: Element[], tabbable = true) {\n  let sentinel = scope[0].previousElementSibling!;\n  let scopeRoot = getScopeRoot(scope);\n  let walker = getFocusableTreeWalker(scopeRoot, {tabbable}, scope);\n  walker.currentNode = sentinel;\n  let nextNode = walker.nextNode();\n\n  // If the scope does not contain a tabbable element, use the first focusable element.\n  if (tabbable && !nextNode) {\n    scopeRoot = getScopeRoot(scope);\n    walker = getFocusableTreeWalker(scopeRoot, {tabbable: false}, scope);\n    walker.currentNode = sentinel;\n    nextNode = walker.nextNode();\n  }\n\n  return nextNode as FocusableElement;\n}\n\nfunction focusFirstInScope(scope: Element[], tabbable:boolean = true) {\n  focusElement(getFirstInScope(scope, tabbable));\n}\n\nfunction useAutoFocus(scopeRef: RefObject<Element[] | null>, autoFocus?: boolean) {\n  const autoFocusRef = React.useRef(autoFocus);\n  useEffect(() => {\n    if (autoFocusRef.current) {\n      activeScope = scopeRef;\n      const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n      if (!isElementInScope(getActiveElement(ownerDocument), activeScope.current) && scopeRef.current) {\n        focusFirstInScope(scopeRef.current);\n      }\n    }\n    autoFocusRef.current = false;\n  }, [scopeRef]);\n}\n\nfunction useActiveScopeTracker(scopeRef: RefObject<Element[] | null>, restore?: boolean, contain?: boolean) {\n  // tracks the active scope, in case restore and contain are both false.\n  // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n  useLayoutEffect(() => {\n    if (restore || contain) {\n      return;\n    }\n\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    let onFocus = (e) => {\n      let target = getEventTarget(e) as Element;\n      if (isElementInScope(target, scopeRef.current)) {\n        activeScope = scopeRef;\n      } else if (!isElementInAnyScope(target)) {\n        activeScope = null;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n  }, [scopeRef, restore, contain]);\n}\n\nfunction shouldRestoreFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.nodeToRestore) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return scope?.scopeRef === scopeRef;\n}\n\nfunction useRestoreFocus(scopeRef: RefObject<Element[] | null>, restoreFocus?: boolean, contain?: boolean) {\n  // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n  // eslint-disable-next-line no-restricted-globals\n  const nodeToRestoreRef = useRef(typeof document !== 'undefined' ? getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined)) as FocusableElement : null);\n\n  // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n  // restoring-non-containing scopes should only care if they become active so they can perform the restore\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n    if (!restoreFocus || contain) {\n      return;\n    }\n\n    let onFocus = () => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) &&\n        isElementInScope(getActiveElement(ownerDocument), scopeRef.current)\n      ) {\n        activeScope = scopeRef;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [scopeRef, contain]);\n\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    // Handle the Tab key so that tabbing out of the scope goes to the next element\n    // after the node that had focus when the scope mounted. This is important when\n    // using portals for overlays, so that focus goes to the expected element when\n    // tabbing out of the overlay.\n    let onKeyDown = (e: KeyboardEvent) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = ownerDocument.activeElement as FocusableElement;\n      if (!isElementInChildScope(focusedElement, scopeRef) || !shouldRestoreFocus(scopeRef)) {\n        return;\n      }\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // Create a DOM tree walker that matches all tabbable elements\n      let walker = getFocusableTreeWalker(ownerDocument.body, {tabbable: true});\n\n      // Find the next tabbable element after the currently focused element\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n\n      if (!nodeToRestore || !nodeToRestore.isConnected || nodeToRestore === ownerDocument.body) {\n        nodeToRestore = undefined;\n        treeNode.nodeToRestore = undefined;\n      }\n\n      // If there is no next element, or it is outside the current scope, move focus to the\n      // next element after the node to restore to instead.\n      if ((!nextElement || !isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n        walker.currentNode = nodeToRestore;\n\n        // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n        do {\n          nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n        } while (isElementInChildScope(nextElement, scopeRef));\n\n        e.preventDefault();\n        e.stopPropagation();\n        if (nextElement) {\n          focusElement(nextElement, true);\n        } else {\n          // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n          // then move focus to the body.\n          // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n          if (!isElementInAnyScope(nodeToRestore)) {\n            focusedElement.blur();\n          } else {\n            focusElement(nodeToRestore, true);\n          }\n        }\n      }\n    };\n\n    if (!contain) {\n      ownerDocument.addEventListener('keydown', onKeyDown as EventListener, true);\n    }\n\n    return () => {\n      if (!contain) {\n        ownerDocument.removeEventListener('keydown', onKeyDown as EventListener, true);\n      }\n    };\n  }, [scopeRef, restoreFocus, contain]);\n\n  // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    let treeNode = focusScopeTree.getTreeNode(scopeRef);\n    if (!treeNode) {\n      return;\n    }\n    treeNode.nodeToRestore = nodeToRestoreRef.current ?? undefined;\n    return () => {\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n      let activeElement = getActiveElement(ownerDocument);\n      if (\n        restoreFocus\n        && nodeToRestore\n        && (\n          ((activeElement && isElementInChildScope(activeElement, scopeRef)) || (activeElement === ownerDocument.body && shouldRestoreFocus(scopeRef)))\n        )\n      ) {\n        // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n        let clonedTree = focusScopeTree.clone();\n        requestAnimationFrame(() => {\n          // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n          if (ownerDocument.activeElement === ownerDocument.body) {\n            // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n            let treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                restoreFocusToElement(treeNode.nodeToRestore);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n\n            // If no nodeToRestore was found, focus the first element in the nearest\n            // ancestor scope that is still in the tree.\n            treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.scopeRef && treeNode.scopeRef.current && focusScopeTree.getTreeNode(treeNode.scopeRef)) {\n                let node = getFirstInScope(treeNode.scopeRef.current, true);\n                restoreFocusToElement(node);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n          }\n        });\n      }\n    };\n  }, [scopeRef, restoreFocus]);\n}\n\nfunction restoreFocusToElement(node: FocusableElement) {\n  // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n  // For example, virtualized collection components reuse DOM elements, so the original element\n  // might still exist in the DOM but representing a different item.\n  if (node.dispatchEvent(new CustomEvent(RESTORE_FOCUS_EVENT, {bubbles: true, cancelable: true}))) {\n    focusElement(node);\n  }\n}\n\n/**\n * Create a [TreeWalker]{@link https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker}\n * that matches all focusable/tabbable elements.\n */\nexport function getFocusableTreeWalker(root: Element, opts?: FocusManagerOptions, scope?: Element[]): ShadowTreeWalker | TreeWalker {\n  let filter = opts?.tabbable ? isTabbable : isFocusable;\n\n  // Ensure that root is an Element or fall back appropriately\n  let rootElement = root?.nodeType === Node.ELEMENT_NODE ? (root as Element) : null;\n\n  // Determine the document to use\n  let doc = getOwnerDocument(rootElement);\n\n  // Create a TreeWalker, ensuring the root is an Element or Document\n  let walker = createShadowTreeWalker(\n    doc,\n    root || doc,\n    NodeFilter.SHOW_ELEMENT,\n    {\n      acceptNode(node) {\n        // Skip nodes inside the starting node.\n        if (opts?.from?.contains(node)) {\n          return NodeFilter.FILTER_REJECT;\n        }\n\n        if (filter(node as Element)\n          && isElementVisible(node as Element)\n          && (!scope || isElementInScope(node as Element, scope))\n          && (!opts?.accept || opts.accept(node as Element))\n        ) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n\n        return NodeFilter.FILTER_SKIP;\n      }\n    }\n  );\n\n  if (opts?.from) {\n    walker.currentNode = opts.from;\n  }\n\n  return walker;\n}\n\n/**\n * Creates a FocusManager object that can be used to move focus within an element.\n */\nexport function createFocusManager(ref: RefObject<Element | null>, defaultOptions: FocusManagerOptions = {}): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      }\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = root;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      } else {\n        let next = last(walker);\n        if (next) {\n          focusElement(next, true);\n        }\n        return next ?? null;\n      }\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = root;\n        let lastNode = last(walker);\n        if (!lastNode) {\n          // couldn't wrap\n          return null;\n        }\n        previousNode = lastNode;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode ?? null;\n    },\n    focusFirst(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let next = last(walker);\n      if (next) {\n        focusElement(next, true);\n      }\n      return next ?? null;\n    }\n  };\n}\n\nfunction last(walker: ShadowTreeWalker | TreeWalker) {\n  let next: FocusableElement | undefined = undefined;\n  let last: FocusableElement;\n  do {\n    last = walker.lastChild() as FocusableElement;\n    if (last) {\n      next = last;\n    }\n  } while (last);\n  return next;\n}\n\n\nclass Tree {\n  root: TreeNode;\n  private fastMap = new Map<ScopeRef, TreeNode>();\n\n  constructor() {\n    this.root = new TreeNode({scopeRef: null});\n    this.fastMap.set(null, this.root);\n  }\n\n  get size() {\n    return this.fastMap.size;\n  }\n\n  getTreeNode(data: ScopeRef) {\n    return this.fastMap.get(data);\n  }\n\n  addTreeNode(scopeRef: ScopeRef, parent: ScopeRef, nodeToRestore?: FocusableElement) {\n    let parentNode = this.fastMap.get(parent ?? null);\n    if (!parentNode) {\n      return;\n    }\n    let node = new TreeNode({scopeRef});\n    parentNode.addChild(node);\n    node.parent = parentNode;\n    this.fastMap.set(scopeRef, node);\n    if (nodeToRestore) {\n      node.nodeToRestore = nodeToRestore;\n    }\n  }\n\n  addNode(node: TreeNode) {\n    this.fastMap.set(node.scopeRef, node);\n  }\n\n  removeTreeNode(scopeRef: ScopeRef) {\n    // never remove the root\n    if (scopeRef === null) {\n      return;\n    }\n    let node = this.fastMap.get(scopeRef);\n    if (!node) {\n      return;\n    }\n    let parentNode = node.parent;\n    // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n    // if we are, then replace the siblings restore with the restore from the scope we're removing\n    for (let current of this.traverse()) {\n      if (\n        current !== node &&\n        node.nodeToRestore &&\n        current.nodeToRestore &&\n        node.scopeRef &&\n        node.scopeRef.current &&\n        isElementInScope(current.nodeToRestore, node.scopeRef.current)\n      ) {\n        current.nodeToRestore = node.nodeToRestore;\n      }\n    }\n    let children = node.children;\n    if (parentNode) {\n      parentNode.removeChild(node);\n      if (children.size > 0) {\n        children.forEach(child => parentNode && parentNode.addChild(child));\n      }\n    }\n\n    this.fastMap.delete(node.scopeRef);\n  }\n\n  // Pre Order Depth First\n  *traverse(node: TreeNode = this.root): Generator<TreeNode> {\n    if (node.scopeRef != null) {\n      yield node;\n    }\n    if (node.children.size > 0) {\n      for (let child of node.children) {\n        yield* this.traverse(child);\n      }\n    }\n  }\n\n  clone(): Tree {\n    let newTree = new Tree();\n    for (let node of this.traverse()) {\n      newTree.addTreeNode(node.scopeRef, node.parent?.scopeRef ?? null, node.nodeToRestore);\n    }\n    return newTree;\n  }\n}\n\nclass TreeNode {\n  public scopeRef: ScopeRef;\n  public nodeToRestore?: FocusableElement;\n  public parent?: TreeNode;\n  public children: Set<TreeNode> = new Set();\n  public contain = false;\n\n  constructor(props: {scopeRef: ScopeRef}) {\n    this.scopeRef = props.scopeRef;\n  }\n  addChild(node: TreeNode) {\n    this.children.add(node);\n    node.parent = this;\n  }\n  removeChild(node: TreeNode) {\n    this.children.delete(node);\n    node.parent = undefined;\n  }\n}\n\nexport let focusScopeTree = new Tree();\n"], "mappings": ";;;;;AAAA;;;;;;;;;;;;AA6EA,MAAMA,kCAAA,gBAAe,IAAAC,YAAI,EAAEC,aAAa,CAAuB;AAC/D,MAAMC,yCAAA,GAAsB;AAE5B,IAAIC,iCAAA,GAAwB;AAYrB,SAASC,0CAAWC,KAAsB;EAC/C,IAAI;IAAAC,QAAA,EAACA,QAAQ;IAAAC,OAAA,EAAEA,OAAO;IAAAC,YAAA,EAAEA,YAAY;IAAAC,SAAA,EAAEA;EAAS,CAAC,GAAGJ,KAAA;EACnD,IAAIK,QAAA,GAAW,IAAAC,aAAK,EAAmB;EACvC,IAAIC,MAAA,GAAS,IAAAD,aAAK,EAAmB;EACrC,IAAIE,QAAA,GAAW,IAAAF,aAAK,EAAa,EAAE;EACnC,IAAI;IAAAG,UAAA,EAACA;EAAU,CAAC,GAAG,IAAAC,iBAAS,EAAEhB,kCAAA,KAAiB,CAAC;EAEhD;EACA,IAAIiB,IAAA,GAAO,IAAAC,cAAM,EAAE,MAAM,IAAIC,8BAAA,CAAS;cAACL;EAAQ,IAAI,CAACA,QAAA,CAAS;EAE7D,IAAAM,sBAAc,EAAE;IACd;IACA;IACA;IACA;IACA,IAAIC,MAAA,GAASN,UAAA,IAAcO,yCAAA,CAAeC,IAAI;IAC9C,IAAID,yCAAA,CAAeE,WAAW,CAACH,MAAA,CAAOP,QAAQ,KAAKV,iCAAA,IAAe,CAACqB,qCAAA,CAAgBrB,iCAAA,EAAaiB,MAAA,CAAOP,QAAQ,GAAG;MAChH,IAAIY,UAAA,GAAaJ,yCAAA,CAAeE,WAAW,CAACpB,iCAAA;MAC5C,IAAIsB,UAAA,EACFL,MAAA,GAASK,UAAA;IAEb;IAEA;IACAL,MAAA,CAAOM,QAAQ,CAACV,IAAA;IAChBK,yCAAA,CAAeM,OAAO,CAACX,IAAA;EACzB,GAAG,CAACA,IAAA,EAAMF,UAAA,CAAW;EAErB,IAAAK,sBAAc,EAAE;IACd,IAAIH,IAAA,GAAOK,yCAAA,CAAeE,WAAW,CAACV,QAAA;IACtC,IAAIG,IAAA,EACFA,IAAA,CAAKT,OAAO,GAAG,CAAC,CAACA,OAAA;EAErB,GAAG,CAACA,OAAA,CAAQ;EAEZ,IAAAY,sBAAc,EAAE;QAEHS,iBAAA;IADX;IACA,IAAIZ,IAAA,IAAOY,iBAAA,GAAAlB,QAAA,CAASmB,OAAO,cAAhBD,iBAAA,uBAAAA,iBAAA,CAAkBE,WAAW;IACxC,IAAIC,KAAA,GAAmB,EAAE;IACzB,IAAIC,eAAA,GAAkBC,CAAA,IAAKA,CAAA,CAAED,eAAe;IAC5C,OAAOhB,IAAA,IAAQA,IAAA,KAASJ,MAAA,CAAOiB,OAAO,EAAE;MACtCE,KAAA,CAAMG,IAAI,CAAClB,IAAA;MACX;MACAA,IAAA,CAAKmB,gBAAgB,CAACjC,yCAAA,EAAqB8B,eAAA;MAC3ChB,IAAA,GAAOA,IAAA,CAAKc,WAAW;IACzB;IAEAjB,QAAA,CAASgB,OAAO,GAAGE,KAAA;IAEnB,OAAO;MACL,KAAK,IAAIf,IAAA,IAAQe,KAAA,EACff,IAAA,CAAKoB,mBAAmB,CAAClC,yCAAA,EAAqB8B,eAAA;IAElD;EACF,GAAG,CAAC1B,QAAA,CAAS;EAEb+B,2CAAA,CAAsBxB,QAAA,EAAUL,YAAA,EAAcD,OAAA;EAC9C+B,yCAAA,CAAoBzB,QAAA,EAAUN,OAAA;EAC9BgC,qCAAA,CAAgB1B,QAAA,EAAUL,YAAA,EAAcD,OAAA;EACxCiC,kCAAA,CAAa3B,QAAA,EAAUJ,SAAA;EAEvB;EACA;EACA,IAAAgC,gBAAQ,EAAE;IACR,MAAMC,aAAA,GAAgB,IAAAC,uBAAe,EAAE,IAAAC,uBAAe,EAAE/B,QAAA,CAASgB,OAAO,GAAGhB,QAAA,CAASgB,OAAO,CAAC,EAAE,GAAGgB,SAAA;IACjG,IAAIC,KAAA,GAAyB;IAE7B,IAAIC,sCAAA,CAAiBL,aAAA,EAAe7B,QAAA,CAASgB,OAAO,GAAG;MACrD;MACA;MACA,KAAK,IAAIb,IAAA,IAAQK,yCAAA,CAAe2B,QAAQ,IACtC,IAAIhC,IAAA,CAAKH,QAAQ,IAAIkC,sCAAA,CAAiBL,aAAA,EAAe1B,IAAA,CAAKH,QAAQ,CAACgB,OAAO,GACxEiB,KAAA,GAAQ9B,IAAA;MAIZ,IAAI8B,KAAA,KAAUzB,yCAAA,CAAeE,WAAW,CAACV,QAAA,GACvCV,iCAAA,GAAc2C,KAAA,CAAMjC,QAAQ;IAEhC;EACF,GAAG,CAACA,QAAA,CAAS;EAEb;EACA;EACA,IAAAM,sBAAc,EAAE;IACd,OAAO;UAEa8B,kCAAA,EAAAC,2BAAA;UAAAC,2CAAA;MADlB;MACA,IAAIC,WAAA,GAAc,CAAAD,2CAAA,IAAAD,2BAAA,GAAA7B,yCAAA,CAAeE,WAAW,CAACV,QAAA,eAA3BqC,2BAAA,wBAAAD,kCAAA,GAAAC,2BAAA,CAAsC9B,MAAM,cAA5C6B,kCAAA,uBAAAA,kCAAA,CAA8CpC,QAAQ,cAAtDsC,2CAAA,cAAAA,2CAAA,GAA0D;MAE5E,IACE,CAACtC,QAAA,KAAaV,iCAAA,IAAeqB,qCAAA,CAAgBX,QAAA,EAAUV,iCAAA,CAAW,MACjE,CAACiD,WAAA,IAAe/B,yCAAA,CAAeE,WAAW,CAAC6B,WAAA,CAAW,GAEvDjD,iCAAA,GAAciD,WAAA;MAEhB/B,yCAAA,CAAegC,cAAc,CAACxC,QAAA;IAChC;EACF,GAAG,CAACA,QAAA,CAAS;EAEb,IAAIyC,YAAA,GAAe,IAAArC,cAAM,EAAE,MAAMsC,gDAAA,CAA2B1C,QAAA,GAAW,EAAE;EACzE,IAAI2C,KAAA,GAAQ,IAAAvC,cAAM,EAAE,OAAO;kBACzBqC,YAAA;IACAxC,UAAA,EAAYE;EACd,IAAI,CAACA,IAAA,EAAMsC,YAAA,CAAa;EAExB,oBACE,IAAAtD,YAAA,EAAAyD,aAAA,CAAC1D,kCAAA,CAAa2D,QAAQ;IAACF,KAAA,EAAOA;kBAC5B,IAAAxD,YAAA,EAAAyD,aAAA,CAAC;IAAK;IAAuBE,MAAA;IAAOC,GAAA,EAAKlD;MACxCJ,QAAA,eACD,IAAAN,YAAA,EAAAyD,aAAA,CAAC;IAAK;IAAqBE,MAAA;IAAOC,GAAA,EAAKhD;;AAG7C;AAOO,SAASiD,0CAAA;MACPC,WAAA;EAAP,QAAOA,WAAA,OAAA/C,iBAAS,EAAEhB,kCAAA,eAAX+D,WAAA,uBAAAA,WAAA,CAA0BR,YAAY;AAC/C;AAEA,SAASC,iDAA2B1C,QAA2C;EAC7E,OAAO;IACLkD,UAAUC,IAAA,GAA4B,CAAC,CAAC;MACtC,IAAIlB,KAAA,GAAQjC,QAAA,CAASgB,OAAO;MAC5B,IAAI;QAAAoC,IAAA,EAACA,IAAI;QAAAC,QAAA,EAAEA,QAAQ;QAAAC,IAAA,EAAEA,IAAI;QAAAC,MAAA,EAAEA;MAAM,CAAC,GAAGJ,IAAA;UACgBK,OAAA;MAArD,IAAIrD,IAAA,GAAOiD,IAAA,IAAQ,IAAAtB,uBAAe,EAAE,IAAAC,uBAAe,EAAE,CAAAyB,OAAA,GAAAvB,KAAK,CAAC,EAAE,cAARuB,OAAA,cAAAA,OAAA,GAAYxB,SAAA;MACjE,IAAIyB,QAAA,GAAWxB,KAAK,CAAC,EAAE,CAACyB,sBAAsB;MAC9C,IAAIC,SAAA,GAAYC,kCAAA,CAAa3B,KAAA;MAC7B,IAAI4B,MAAA,GAASC,yCAAA,CAAuBH,SAAA,EAAW;kBAACN,QAAA;gBAAUE;MAAM,GAAGtB,KAAA;MACnE4B,MAAA,CAAOE,WAAW,GAAG7B,sCAAA,CAAiB/B,IAAA,EAAM8B,KAAA,IAAS9B,IAAA,GAAOsD,QAAA;MAC5D,IAAIO,QAAA,GAAWH,MAAA,CAAOG,QAAQ;MAC9B,IAAI,CAACA,QAAA,IAAYV,IAAA,EAAM;QACrBO,MAAA,CAAOE,WAAW,GAAGN,QAAA;QACrBO,QAAA,GAAWH,MAAA,CAAOG,QAAQ;MAC5B;MACA,IAAIA,QAAA,EACFC,kCAAA,CAAaD,QAAA,EAAU;MAEzB,OAAOA,QAAA;IACT;IACAE,cAAcf,IAAA,GAA4B,CAAC,CAAC;MAC1C,IAAIlB,KAAA,GAAQjC,QAAA,CAASgB,OAAO;MAC5B,IAAI;QAAAoC,IAAA,EAACA,IAAI;QAAAC,QAAA,EAAEA,QAAQ;QAAAC,IAAA,EAAEA,IAAI;QAAAC,MAAA,EAAEA;MAAM,CAAC,GAAGJ,IAAA;UACgBK,OAAA;MAArD,IAAIrD,IAAA,GAAOiD,IAAA,IAAQ,IAAAtB,uBAAe,EAAE,IAAAC,uBAAe,EAAE,CAAAyB,OAAA,GAAAvB,KAAK,CAAC,EAAE,cAARuB,OAAA,cAAAA,OAAA,GAAYxB,SAAA;MACjE,IAAIyB,QAAA,GAAWxB,KAAK,CAACA,KAAA,CAAMkC,MAAM,GAAG,EAAE,CAACC,kBAAkB;MACzD,IAAIT,SAAA,GAAYC,kCAAA,CAAa3B,KAAA;MAC7B,IAAI4B,MAAA,GAASC,yCAAA,CAAuBH,SAAA,EAAW;kBAACN,QAAA;gBAAUE;MAAM,GAAGtB,KAAA;MACnE4B,MAAA,CAAOE,WAAW,GAAG7B,sCAAA,CAAiB/B,IAAA,EAAM8B,KAAA,IAAS9B,IAAA,GAAQsD,QAAA;MAC7D,IAAIY,YAAA,GAAeR,MAAA,CAAOQ,YAAY;MACtC,IAAI,CAACA,YAAA,IAAgBf,IAAA,EAAM;QACzBO,MAAA,CAAOE,WAAW,GAAGN,QAAA;QACrBY,YAAA,GAAeR,MAAA,CAAOQ,YAAY;MACpC;MACA,IAAIA,YAAA,EACFJ,kCAAA,CAAaI,YAAA,EAAc;MAE7B,OAAOA,YAAA;IACT;IACAC,WAAWnB,IAAA,GAAO,CAAC,CAAC;MAClB,IAAIlB,KAAA,GAAQjC,QAAA,CAASgB,OAAO;MAC5B,IAAI;QAAAqC,QAAA,EAACA,QAAQ;QAAAE,MAAA,EAAEA;MAAM,CAAC,GAAGJ,IAAA;MACzB,IAAIQ,SAAA,GAAYC,kCAAA,CAAa3B,KAAA;MAC7B,IAAI4B,MAAA,GAASC,yCAAA,CAAuBH,SAAA,EAAW;kBAACN,QAAA;gBAAUE;MAAM,GAAGtB,KAAA;MACnE4B,MAAA,CAAOE,WAAW,GAAG9B,KAAK,CAAC,EAAE,CAACyB,sBAAsB;MACpD,IAAIM,QAAA,GAAWH,MAAA,CAAOG,QAAQ;MAC9B,IAAIA,QAAA,EACFC,kCAAA,CAAaD,QAAA,EAAU;MAEzB,OAAOA,QAAA;IACT;IACAO,UAAUpB,IAAA,GAAO,CAAC,CAAC;MACjB,IAAIlB,KAAA,GAAQjC,QAAA,CAASgB,OAAO;MAC5B,IAAI;QAAAqC,QAAA,EAACA,QAAQ;QAAAE,MAAA,EAAEA;MAAM,CAAC,GAAGJ,IAAA;MACzB,IAAIQ,SAAA,GAAYC,kCAAA,CAAa3B,KAAA;MAC7B,IAAI4B,MAAA,GAASC,yCAAA,CAAuBH,SAAA,EAAW;kBAACN,QAAA;gBAAUE;MAAM,GAAGtB,KAAA;MACnE4B,MAAA,CAAOE,WAAW,GAAG9B,KAAK,CAACA,KAAA,CAAMkC,MAAM,GAAG,EAAE,CAACC,kBAAkB;MAC/D,IAAIC,YAAA,GAAeR,MAAA,CAAOQ,YAAY;MACtC,IAAIA,YAAA,EACFJ,kCAAA,CAAaI,YAAA,EAAc;MAE7B,OAAOA,YAAA;IACT;EACF;AACF;AAEA,SAAST,mCAAa3B,KAAgB;EACpC,OAAOA,KAAK,CAAC,EAAE,CAACuC,aAAa;AAC/B;AAEA,SAASC,yCAAmBzE,QAAkB;EAC5C,IAAIiC,KAAA,GAAQzB,yCAAA,CAAeE,WAAW,CAACpB,iCAAA;EACvC,OAAO2C,KAAA,IAASA,KAAA,CAAMjC,QAAQ,KAAKA,QAAA,EAAU;IAC3C,IAAIiC,KAAA,CAAMvC,OAAO,EACf,OAAO;IAGTuC,KAAA,GAAQA,KAAA,CAAM1B,MAAM;EACtB;EAEA,OAAO;AACT;AAEA,SAASkB,0CAAoBzB,QAAqC,EAAEN,OAAiB;EACnF,IAAIgF,WAAA,GAAc,IAAA5E,aAAK,EAAoBkC,SAAA;EAE3C,IAAI2C,GAAA,GAAM,IAAA7E,aAAK,EAA4CkC,SAAA;EAC3D,IAAA1B,sBAAc,EAAE;IACd,IAAI2B,KAAA,GAAQjC,QAAA,CAASgB,OAAO;IAC5B,IAAI,CAACtB,OAAA,EAAS;MACZ;MACA,IAAIiF,GAAA,CAAI3D,OAAO,EAAE;QACf4D,oBAAA,CAAqBD,GAAA,CAAI3D,OAAO;QAChC2D,GAAA,CAAI3D,OAAO,GAAGgB,SAAA;MAChB;MACA;IACF;IAEA,MAAM6C,aAAA,GAAgB,IAAA9C,uBAAe,EAAEE,KAAA,GAAQA,KAAK,CAAC,EAAE,GAAGD,SAAA;IAE1D;IACA,IAAI8C,SAAA,GAAa1D,CAAA;MACf,IAAIA,CAAA,CAAE2D,GAAG,KAAK,SAAS3D,CAAA,CAAE4D,MAAM,IAAI5D,CAAA,CAAE6D,OAAO,IAAI7D,CAAA,CAAE8D,OAAO,IAAI,CAACT,wCAAA,CAAmBzE,QAAA,KAAaoB,CAAA,CAAE+D,WAAW,EACzG;MAGF,IAAIC,cAAA,GAAiB,IAAAtD,uBAAe,EAAE+C,aAAA;MACtC,IAAI5C,KAAA,GAAQjC,QAAA,CAASgB,OAAO;MAC5B,IAAI,CAACiB,KAAA,IAAS,CAACC,sCAAA,CAAiBkD,cAAA,EAAgBnD,KAAA,GAC9C;MAGF,IAAI0B,SAAA,GAAYC,kCAAA,CAAa3B,KAAA;MAC7B,IAAI4B,MAAA,GAASC,yCAAA,CAAuBH,SAAA,EAAW;QAACN,QAAA,EAAU;MAAI,GAAGpB,KAAA;MACjE,IAAI,CAACmD,cAAA,EACH;MAEFvB,MAAA,CAAOE,WAAW,GAAGqB,cAAA;MACrB,IAAIC,WAAA,GAAejE,CAAA,CAAEkE,QAAQ,GAAGzB,MAAA,CAAOQ,YAAY,KAAKR,MAAA,CAAOG,QAAQ;MACvE,IAAI,CAACqB,WAAA,EAAa;QAChBxB,MAAA,CAAOE,WAAW,GAAG3C,CAAA,CAAEkE,QAAQ,GAAGrD,KAAK,CAACA,KAAA,CAAMkC,MAAM,GAAG,EAAE,CAACC,kBAAkB,GAAInC,KAAK,CAAC,EAAE,CAACyB,sBAAsB;QAC/G2B,WAAA,GAAejE,CAAA,CAAEkE,QAAQ,GAAGzB,MAAA,CAAOQ,YAAY,KAAKR,MAAA,CAAOG,QAAQ;MACrE;MAEA5C,CAAA,CAAEmE,cAAc;MAChB,IAAIF,WAAA,EACFpB,kCAAA,CAAaoB,WAAA,EAAa;IAE9B;IAEA,IAAIG,OAAA,GAA0BpE,CAAA;MAC5B;MACA;MACA,IAAI,CAAC,CAAC9B,iCAAA,IAAeqB,qCAAA,CAAgBrB,iCAAA,EAAaU,QAAA,CAAQ,KAAMkC,sCAAA,CAAiB,IAAAuD,qBAAa,EAAErE,CAAA,GAAepB,QAAA,CAASgB,OAAO,GAAG;QAChI1B,iCAAA,GAAcU,QAAA;QACd0E,WAAA,CAAY1D,OAAO,GAAG,IAAAyE,qBAAa,EAAErE,CAAA;MACvC,OAAO,IAAIqD,wCAAA,CAAmBzE,QAAA,KAAa,CAAC0F,2CAAA,CAAsB,IAAAD,qBAAa,EAAErE,CAAA,GAAepB,QAAA,GAAW;QACzG;QACA;QACA,IAAI0E,WAAA,CAAY1D,OAAO,EACrB0D,WAAA,CAAY1D,OAAO,CAAC2E,KAAK,QACpB,IAAIrG,iCAAA,IAAeA,iCAAA,CAAY0B,OAAO,EAC3C4E,uCAAA,CAAkBtG,iCAAA,CAAY0B,OAAO;MAEzC,OAAO,IAAIyD,wCAAA,CAAmBzE,QAAA,GAC5B0E,WAAA,CAAY1D,OAAO,GAAG,IAAAyE,qBAAa,EAAErE,CAAA;IAEzC;IAEA,IAAIyE,MAAA,GAAyBzE,CAAA;MAC3B;MACA,IAAIuD,GAAA,CAAI3D,OAAO,EACb4D,oBAAA,CAAqBD,GAAA,CAAI3D,OAAO;MAElC2D,GAAA,CAAI3D,OAAO,GAAG8E,qBAAA,CAAsB;QAClC;QACA;QACA;QACA,IAAIC,QAAA,GAAW,IAAAC,6BAAqB;QACpC,IAAIC,sBAAA,GAAyB,CAACF,QAAA,KAAa,aAAaA,QAAA,KAAa,IAAG,KAAM,IAAAG,gBAAQ,OAAO,IAAAC,eAAO;QAEpG;QACA,IAAItE,aAAA,GAAgB,IAAAC,uBAAe,EAAE+C,aAAA;QACrC,IAAI,CAACoB,sBAAA,IAA0BpE,aAAA,IAAiB4C,wCAAA,CAAmBzE,QAAA,KAAa,CAAC0F,2CAAA,CAAsB7D,aAAA,EAAe7B,QAAA,GAAW;UAC/HV,iCAAA,GAAcU,QAAA;UACd,IAAIoG,MAAA,GAAS,IAAAX,qBAAa,EAAErE,CAAA;UAC5B,IAAIgF,MAAA,IAAUA,MAAA,CAAOC,WAAW,EAAE;gBAEhCC,oBAAA;YADA5B,WAAA,CAAY1D,OAAO,GAAGoF,MAAA;aACtBE,oBAAA,GAAA5B,WAAA,CAAY1D,OAAO,cAAnBsF,oBAAA,uBAAAA,oBAAA,CAAqBX,KAAK;UAC5B,OAAO,IAAIrG,iCAAA,CAAY0B,OAAO,EAC5B4E,uCAAA,CAAkBtG,iCAAA,CAAY0B,OAAO;QAEzC;MACF;IACF;IAEA6D,aAAA,CAAcvD,gBAAgB,CAAC,WAAWwD,SAAA,EAAW;IACrDD,aAAA,CAAcvD,gBAAgB,CAAC,WAAWkE,OAAA,EAAS;IACnDvD,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOsE,OAAO,CAACC,OAAA,IAAWA,OAAA,CAAQlF,gBAAgB,CAAC,WAAWkE,OAAA,EAAS;IACvEvD,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOsE,OAAO,CAACC,OAAA,IAAWA,OAAA,CAAQlF,gBAAgB,CAAC,YAAYuE,MAAA,EAAQ;IACvE,OAAO;MACLhB,aAAA,CAActD,mBAAmB,CAAC,WAAWuD,SAAA,EAAW;MACxDD,aAAA,CAActD,mBAAmB,CAAC,WAAWiE,OAAA,EAAS;MACtDvD,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOsE,OAAO,CAACC,OAAA,IAAWA,OAAA,CAAQjF,mBAAmB,CAAC,WAAWiE,OAAA,EAAS;MAC1EvD,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOsE,OAAO,CAACC,OAAA,IAAWA,OAAA,CAAQjF,mBAAmB,CAAC,YAAYsE,MAAA,EAAQ;IAC5E;EACF,GAAG,CAAC7F,QAAA,EAAUN,OAAA,CAAQ;EAEtB;EAEA,IAAAY,sBAAc,EAAE;IACd,OAAO;MACL,IAAIqE,GAAA,CAAI3D,OAAO,EACb4D,oBAAA,CAAqBD,GAAA,CAAI3D,OAAO;IAEpC;EACF,GAAG,CAAC2D,GAAA,CAAI;AACV;AAEA,SAAS8B,0CAAoBD,OAAgB;EAC3C,OAAOd,2CAAA,CAAsBc,OAAA;AAC/B;AAEA,SAAStE,uCAAiBsE,OAAwB,EAAEvE,KAAwB;EAC1E,IAAI,CAACuE,OAAA,EACH,OAAO;EAET,IAAI,CAACvE,KAAA,EACH,OAAO;EAET,OAAOA,KAAA,CAAMyE,IAAI,CAACvG,IAAA,IAAQA,IAAA,CAAKwG,QAAQ,CAACH,OAAA;AAC1C;AAEA,SAASd,4CAAsBc,OAAgB,EAAEvE,KAAA,GAAkB,IAAI;EACrE;EACA,IAAIuE,OAAA,YAAmBI,OAAA,IAAWJ,OAAA,CAAQK,OAAO,CAAC,gCAChD,OAAO;EAGT;EACA;EACA,KAAK,IAAI;IAAC7G,QAAA,EAAU8G;EAAC,CAAC,IAAItG,yCAAA,CAAe2B,QAAQ,CAAC3B,yCAAA,CAAeE,WAAW,CAACuB,KAAA,IAAS;IACpF,IAAI6E,CAAA,IAAK5E,sCAAA,CAAiBsE,OAAA,EAASM,CAAA,CAAE9F,OAAO,GAC1C,OAAO;EAEX;EAEA,OAAO;AACT;AAGO,SAAS+F,0CAA8BP,OAAgB;EAC5D,OAAOd,2CAAA,CAAsBc,OAAA,EAASlH,iCAAA;AACxC;AAEA,SAASqB,sCAAgBqG,QAAkB,EAAE/E,KAAe;MAC7CI,2BAAA;EAAb,IAAI9B,MAAA,IAAS8B,2BAAA,GAAA7B,yCAAA,CAAeE,WAAW,CAACuB,KAAA,eAA3BI,2BAAA,uBAAAA,2BAAA,CAAmC9B,MAAM;EACtD,OAAOA,MAAA,EAAQ;IACb,IAAIA,MAAA,CAAOP,QAAQ,KAAKgH,QAAA,EACtB,OAAO;IAETzG,MAAA,GAASA,MAAA,CAAOA,MAAM;EACxB;EACA,OAAO;AACT;AAEA,SAAS0D,mCAAauC,OAAgC,EAAES,MAAA,GAAS,KAAK;EACpE,IAAIT,OAAA,IAAW,QAAQ,CAACS,MAAA,EACtB,IAAI;IACF,IAAAC,kBAAU,EAAEV,OAAA;EACd,EAAE,MAAM;IACN;EAAA,CACF,MACK,IAAIA,OAAA,IAAW,MACpB,IAAI;IACFA,OAAA,CAAQb,KAAK;EACf,EAAE,MAAM;IACN;EAAA;AAGN;AAEA,SAASwB,sCAAgBlF,KAAgB,EAAEoB,QAAA,GAAW,IAAI;EACxD,IAAII,QAAA,GAAWxB,KAAK,CAAC,EAAE,CAACyB,sBAAsB;EAC9C,IAAIC,SAAA,GAAYC,kCAAA,CAAa3B,KAAA;EAC7B,IAAI4B,MAAA,GAASC,yCAAA,CAAuBH,SAAA,EAAW;cAACN;EAAQ,GAAGpB,KAAA;EAC3D4B,MAAA,CAAOE,WAAW,GAAGN,QAAA;EACrB,IAAIO,QAAA,GAAWH,MAAA,CAAOG,QAAQ;EAE9B;EACA,IAAIX,QAAA,IAAY,CAACW,QAAA,EAAU;IACzBL,SAAA,GAAYC,kCAAA,CAAa3B,KAAA;IACzB4B,MAAA,GAASC,yCAAA,CAAuBH,SAAA,EAAW;MAACN,QAAA,EAAU;IAAK,GAAGpB,KAAA;IAC9D4B,MAAA,CAAOE,WAAW,GAAGN,QAAA;IACrBO,QAAA,GAAWH,MAAA,CAAOG,QAAQ;EAC5B;EAEA,OAAOA,QAAA;AACT;AAEA,SAAS4B,wCAAkB3D,KAAgB,EAAEoB,QAAA,GAAmB,IAAI;EAClEY,kCAAA,CAAakD,qCAAA,CAAgBlF,KAAA,EAAOoB,QAAA;AACtC;AAEA,SAAS1B,mCAAa3B,QAAqC,EAAEJ,SAAmB;EAC9E,MAAMwH,YAAA,GAAe,IAAAjI,YAAI,EAAEkI,MAAM,CAACzH,SAAA;EAClC,IAAAgC,gBAAQ,EAAE;IACR,IAAIwF,YAAA,CAAapG,OAAO,EAAE;MACxB1B,iCAAA,GAAcU,QAAA;MACd,MAAM6E,aAAA,GAAgB,IAAA9C,uBAAe,EAAE/B,QAAA,CAASgB,OAAO,GAAGhB,QAAA,CAASgB,OAAO,CAAC,EAAE,GAAGgB,SAAA;MAChF,IAAI,CAACE,sCAAA,CAAiB,IAAAJ,uBAAe,EAAE+C,aAAA,GAAgBvF,iCAAA,CAAY0B,OAAO,KAAKhB,QAAA,CAASgB,OAAO,EAC7F4E,uCAAA,CAAkB5F,QAAA,CAASgB,OAAO;IAEtC;IACAoG,YAAA,CAAapG,OAAO,GAAG;EACzB,GAAG,CAAChB,QAAA,CAAS;AACf;AAEA,SAASwB,4CAAsBxB,QAAqC,EAAEsH,OAAiB,EAAE5H,OAAiB;EACxG;EACA;EACA,IAAAY,sBAAc,EAAE;IACd,IAAIgH,OAAA,IAAW5H,OAAA,EACb;IAGF,IAAIuC,KAAA,GAAQjC,QAAA,CAASgB,OAAO;IAC5B,MAAM6D,aAAA,GAAgB,IAAA9C,uBAAe,EAAEE,KAAA,GAAQA,KAAK,CAAC,EAAE,GAAGD,SAAA;IAE1D,IAAIwD,OAAA,GAAWpE,CAAA;MACb,IAAIgF,MAAA,GAAS,IAAAX,qBAAa,EAAErE,CAAA;MAC5B,IAAIc,sCAAA,CAAiBkE,MAAA,EAAQpG,QAAA,CAASgB,OAAO,GAC3C1B,iCAAA,GAAcU,QAAA,MACT,IAAI,CAACyG,yCAAA,CAAoBL,MAAA,GAC9B9G,iCAAA,GAAc;IAElB;IAEAuF,aAAA,CAAcvD,gBAAgB,CAAC,WAAWkE,OAAA,EAAS;IACnDvD,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOsE,OAAO,CAACC,OAAA,IAAWA,OAAA,CAAQlF,gBAAgB,CAAC,WAAWkE,OAAA,EAAS;IACvE,OAAO;MACLX,aAAA,CAActD,mBAAmB,CAAC,WAAWiE,OAAA,EAAS;MACtDvD,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOsE,OAAO,CAACC,OAAA,IAAWA,OAAA,CAAQjF,mBAAmB,CAAC,WAAWiE,OAAA,EAAS;IAC5E;EACF,GAAG,CAACxF,QAAA,EAAUsH,OAAA,EAAS5H,OAAA,CAAQ;AACjC;AAEA,SAAS6H,yCAAmBvH,QAAkB;EAC5C,IAAIiC,KAAA,GAAQzB,yCAAA,CAAeE,WAAW,CAACpB,iCAAA;EACvC,OAAO2C,KAAA,IAASA,KAAA,CAAMjC,QAAQ,KAAKA,QAAA,EAAU;IAC3C,IAAIiC,KAAA,CAAMuF,aAAa,EACrB,OAAO;IAGTvF,KAAA,GAAQA,KAAA,CAAM1B,MAAM;EACtB;EAEA,OAAO,CAAA0B,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOjC,QAAQ,MAAKA,QAAA;AAC7B;AAEA,SAAS0B,sCAAgB1B,QAAqC,EAAEL,YAAsB,EAAED,OAAiB;EACvG;EACA;EACA,MAAM+H,gBAAA,GAAmB,IAAA3H,aAAK,EAAE,OAAO4H,QAAA,KAAa,cAAc,IAAA5F,uBAAe,EAAE,IAAAC,uBAAe,EAAE/B,QAAA,CAASgB,OAAO,GAAGhB,QAAA,CAASgB,OAAO,CAAC,EAAE,GAAGgB,SAAA,KAAkC;EAE/K;EACA;EACA,IAAA1B,sBAAc,EAAE;IACd,IAAI2B,KAAA,GAAQjC,QAAA,CAASgB,OAAO;IAC5B,MAAM6D,aAAA,GAAgB,IAAA9C,uBAAe,EAAEE,KAAA,GAAQA,KAAK,CAAC,EAAE,GAAGD,SAAA;IAC1D,IAAI,CAACrC,YAAA,IAAgBD,OAAA,EACnB;IAGF,IAAI8F,OAAA,GAAUA,CAAA;MACZ;MACA;MACA,IAAI,CAAC,CAAClG,iCAAA,IAAeqB,qCAAA,CAAgBrB,iCAAA,EAAaU,QAAA,CAAQ,KACxDkC,sCAAA,CAAiB,IAAAJ,uBAAe,EAAE+C,aAAA,GAAgB7E,QAAA,CAASgB,OAAO,GAElE1B,iCAAA,GAAcU,QAAA;IAElB;IAEA6E,aAAA,CAAcvD,gBAAgB,CAAC,WAAWkE,OAAA,EAAS;IACnDvD,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOsE,OAAO,CAACC,OAAA,IAAWA,OAAA,CAAQlF,gBAAgB,CAAC,WAAWkE,OAAA,EAAS;IACvE,OAAO;MACLX,aAAA,CAActD,mBAAmB,CAAC,WAAWiE,OAAA,EAAS;MACtDvD,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOsE,OAAO,CAACC,OAAA,IAAWA,OAAA,CAAQjF,mBAAmB,CAAC,WAAWiE,OAAA,EAAS;IAC5E;IACA;EACF,GAAG,CAACxF,QAAA,EAAUN,OAAA,CAAQ;EAEtB,IAAAY,sBAAc,EAAE;IACd,MAAMuE,aAAA,GAAgB,IAAA9C,uBAAe,EAAE/B,QAAA,CAASgB,OAAO,GAAGhB,QAAA,CAASgB,OAAO,CAAC,EAAE,GAAGgB,SAAA;IAEhF,IAAI,CAACrC,YAAA,EACH;IAGF;IACA;IACA;IACA;IACA,IAAImF,SAAA,GAAa1D,CAAA;MACf,IAAIA,CAAA,CAAE2D,GAAG,KAAK,SAAS3D,CAAA,CAAE4D,MAAM,IAAI5D,CAAA,CAAE6D,OAAO,IAAI7D,CAAA,CAAE8D,OAAO,IAAI,CAACT,wCAAA,CAAmBzE,QAAA,KAAaoB,CAAA,CAAE+D,WAAW,EACzG;MAGF,IAAIC,cAAA,GAAiBP,aAAA,CAAchD,aAAa;MAChD,IAAI,CAAC6D,2CAAA,CAAsBN,cAAA,EAAgBpF,QAAA,KAAa,CAACuH,wCAAA,CAAmBvH,QAAA,GAC1E;MAEF,IAAI2H,QAAA,GAAWnH,yCAAA,CAAeE,WAAW,CAACV,QAAA;MAC1C,IAAI,CAAC2H,QAAA,EACH;MAEF,IAAIH,aAAA,GAAgBG,QAAA,CAASH,aAAa;MAE1C;MACA,IAAI3D,MAAA,GAASC,yCAAA,CAAuBe,aAAA,CAAc+C,IAAI,EAAE;QAACvE,QAAA,EAAU;MAAI;MAEvE;MACAQ,MAAA,CAAOE,WAAW,GAAGqB,cAAA;MACrB,IAAIC,WAAA,GAAejE,CAAA,CAAEkE,QAAQ,GAAGzB,MAAA,CAAOQ,YAAY,KAAKR,MAAA,CAAOG,QAAQ;MAEvE,IAAI,CAACwD,aAAA,IAAiB,CAACA,aAAA,CAAcnB,WAAW,IAAImB,aAAA,KAAkB3C,aAAA,CAAc+C,IAAI,EAAE;QACxFJ,aAAA,GAAgBxF,SAAA;QAChB2F,QAAA,CAASH,aAAa,GAAGxF,SAAA;MAC3B;MAEA;MACA;MACA,IAAI,CAAC,CAACqD,WAAA,IAAe,CAACK,2CAAA,CAAsBL,WAAA,EAAarF,QAAA,CAAQ,KAAMwH,aAAA,EAAe;QACpF3D,MAAA,CAAOE,WAAW,GAAGyD,aAAA;QAErB;QACA,GACEnC,WAAA,GAAejE,CAAA,CAAEkE,QAAQ,GAAGzB,MAAA,CAAOQ,YAAY,KAAKR,MAAA,CAAOG,QAAQ,WAC5D0B,2CAAA,CAAsBL,WAAA,EAAarF,QAAA;QAE5CoB,CAAA,CAAEmE,cAAc;QAChBnE,CAAA,CAAED,eAAe;QACjB,IAAIkE,WAAA,EACFpB,kCAAA,CAAaoB,WAAA,EAAa;UAE1B;UACA;UACA;UACA,IAAI,CAACoB,yCAAA,CAAoBe,aAAA,GACvBpC,cAAA,CAAeyC,IAAI,QAEnB5D,kCAAA,CAAauD,aAAA,EAAe;MAGlC;IACF;IAEA,IAAI,CAAC9H,OAAA,EACHmF,aAAA,CAAcvD,gBAAgB,CAAC,WAAWwD,SAAA,EAA4B;IAGxE,OAAO;MACL,IAAI,CAACpF,OAAA,EACHmF,aAAA,CAActD,mBAAmB,CAAC,WAAWuD,SAAA,EAA4B;IAE7E;EACF,GAAG,CAAC9E,QAAA,EAAUL,YAAA,EAAcD,OAAA,CAAQ;EAEpC;EACA,IAAAY,sBAAc,EAAE;IACd,MAAMuE,aAAA,GAAgB,IAAA9C,uBAAe,EAAE/B,QAAA,CAASgB,OAAO,GAAGhB,QAAA,CAASgB,OAAO,CAAC,EAAE,GAAGgB,SAAA;IAEhF,IAAI,CAACrC,YAAA,EACH;IAGF,IAAIgI,QAAA,GAAWnH,yCAAA,CAAeE,WAAW,CAACV,QAAA;IAC1C,IAAI,CAAC2H,QAAA,EACH;QAEuBG,yBAAA;IAAzBH,QAAA,CAASH,aAAa,GAAG,CAAAM,yBAAA,GAAAL,gBAAA,CAAiBzG,OAAO,cAAxB8G,yBAAA,cAAAA,yBAAA,GAA4B9F,SAAA;IACrD,OAAO;MACL,IAAI2F,QAAA,GAAWnH,yCAAA,CAAeE,WAAW,CAACV,QAAA;MAC1C,IAAI,CAAC2H,QAAA,EACH;MAEF,IAAIH,aAAA,GAAgBG,QAAA,CAASH,aAAa;MAE1C;MACA,IAAI3F,aAAA,GAAgB,IAAAC,uBAAe,EAAE+C,aAAA;MACrC,IACElF,YAAA,IACG6H,aAAA,KAEA3F,aAAC,IAAiB6D,2CAAA,CAAsB7D,aAAA,EAAe7B,QAAA,KAAe6B,aAAA,KAAkBgD,aAAA,CAAc+C,IAAI,IAAIL,wCAAA,CAAmBvH,QAAA,CAAS,GAE7I;QACA;QACA,IAAI+H,UAAA,GAAavH,yCAAA,CAAewH,KAAK;QACrClC,qBAAA,CAAsB;UACpB;UACA,IAAIjB,aAAA,CAAchD,aAAa,KAAKgD,aAAA,CAAc+C,IAAI,EAAE;YACtD;YACA,IAAID,QAAA,GAAWI,UAAA,CAAWrH,WAAW,CAACV,QAAA;YACtC,OAAO2H,QAAA,EAAU;cACf,IAAIA,QAAA,CAASH,aAAa,IAAIG,QAAA,CAASH,aAAa,CAACnB,WAAW,EAAE;gBAChE4B,2CAAA,CAAsBN,QAAA,CAASH,aAAa;gBAC5C;cACF;cACAG,QAAA,GAAWA,QAAA,CAASpH,MAAM;YAC5B;YAEA;YACA;YACAoH,QAAA,GAAWI,UAAA,CAAWrH,WAAW,CAACV,QAAA;YAClC,OAAO2H,QAAA,EAAU;cACf,IAAIA,QAAA,CAAS3H,QAAQ,IAAI2H,QAAA,CAAS3H,QAAQ,CAACgB,OAAO,IAAIR,yCAAA,CAAeE,WAAW,CAACiH,QAAA,CAAS3H,QAAQ,GAAG;gBACnG,IAAIG,IAAA,GAAOgH,qCAAA,CAAgBQ,QAAA,CAAS3H,QAAQ,CAACgB,OAAO,EAAE;gBACtDiH,2CAAA,CAAsB9H,IAAA;gBACtB;cACF;cACAwH,QAAA,GAAWA,QAAA,CAASpH,MAAM;YAC5B;UACF;QACF;MACF;IACF;EACF,GAAG,CAACP,QAAA,EAAUL,YAAA,CAAa;AAC7B;AAEA,SAASsI,4CAAsB9H,IAAsB;EACnD;EACA;EACA;EACA,IAAIA,IAAA,CAAK+H,aAAa,CAAC,IAAIC,WAAA,CAAY9I,yCAAA,EAAqB;IAAC+I,OAAA,EAAS;IAAMC,UAAA,EAAY;EAAI,KAC1FpE,kCAAA,CAAa9D,IAAA;AAEjB;AAMO,SAAS2D,0CAAuBrD,IAAa,EAAE0C,IAA0B,EAAElB,KAAiB;EACjG,IAAIqG,MAAA,GAAS,CAAAnF,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAME,QAAQ,KAAG,GAAAkF,iBAAS,KAAI,GAAAC,kBAAU;EAErD;EACA,IAAIC,WAAA,GAAc,CAAAhI,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAMiI,QAAQ,MAAKC,IAAA,CAAKC,YAAY,GAAInI,IAAA,GAAmB;EAE7E;EACA,IAAIoI,GAAA,GAAM,IAAA9G,uBAAe,EAAE0G,WAAA;EAE3B;EACA,IAAI5E,MAAA,GAAS,IAAAiF,6BAAqB,EAChCD,GAAA,EACApI,IAAA,IAAQoI,GAAA,EACRE,UAAA,CAAWC,YAAY,EACvB;IACEC,WAAW9I,IAAI;UAET+I,UAAA;MADJ;MACA,IAAI/F,IAAA,aAAAA,IAAA,wBAAA+F,UAAA,GAAA/F,IAAA,CAAMC,IAAI,cAAV8F,UAAA,uBAAAA,UAAA,CAAYvC,QAAQ,CAACxG,IAAA,GACvB,OAAO4I,UAAA,CAAWI,aAAa;MAGjC,IAAIb,MAAA,CAAOnI,IAAA,KACN,IAAAiJ,yCAAe,EAAEjJ,IAAA,MAChB,CAAC8B,KAAA,IAASC,sCAAA,CAAiB/B,IAAA,EAAiB8B,KAAA,CAAK,MACjD,EAACkB,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAMI,MAAM,KAAIJ,IAAA,CAAKI,MAAM,CAACpD,IAAA,CAAe,GAEhD,OAAO4I,UAAA,CAAWM,aAAa;MAGjC,OAAON,UAAA,CAAWO,WAAW;IAC/B;EACF;EAGF,IAAInG,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAMC,IAAI,EACZS,MAAA,CAAOE,WAAW,GAAGZ,IAAA,CAAKC,IAAI;EAGhC,OAAOS,MAAA;AACT;AAKO,SAAS0F,yCAAmBxG,GAA8B,EAAEyG,cAAA,GAAsC,CAAC,CAAC;EACzG,OAAO;IACLtG,UAAUC,IAAA,GAA4B,CAAC,CAAC;MACtC,IAAI1C,IAAA,GAAOsC,GAAA,CAAI/B,OAAO;MACtB,IAAI,CAACP,IAAA,EACH,OAAO;MAET,IAAI;QAAA2C,IAAA,EAACA,IAAI;QAAEC,QAAA,GAAWmG,cAAA,CAAenG,QAAQ;QAAEC,IAAA,GAAOkG,cAAA,CAAelG,IAAI;QAAEC,MAAA,GAASiG,cAAA,CAAejG;MAAM,CAAC,GAAGJ,IAAA;MAC7G,IAAIhD,IAAA,GAAOiD,IAAA,IAAQ,IAAAtB,uBAAe,EAAE,IAAAC,uBAAe,EAAEtB,IAAA;MACrD,IAAIoD,MAAA,GAASC,yCAAA,CAAuBrD,IAAA,EAAM;kBAAC4C,QAAA;gBAAUE;MAAM;MAC3D,IAAI9C,IAAA,CAAKkG,QAAQ,CAACxG,IAAA,GAChB0D,MAAA,CAAOE,WAAW,GAAG5D,IAAA;MAEvB,IAAI6D,QAAA,GAAWH,MAAA,CAAOG,QAAQ;MAC9B,IAAI,CAACA,QAAA,IAAYV,IAAA,EAAM;QACrBO,MAAA,CAAOE,WAAW,GAAGtD,IAAA;QACrBuD,QAAA,GAAWH,MAAA,CAAOG,QAAQ;MAC5B;MACA,IAAIA,QAAA,EACFC,kCAAA,CAAaD,QAAA,EAAU;MAEzB,OAAOA,QAAA;IACT;IACAE,cAAcf,IAAA,GAA4BqG,cAAc;MACtD,IAAI/I,IAAA,GAAOsC,GAAA,CAAI/B,OAAO;MACtB,IAAI,CAACP,IAAA,EACH,OAAO;MAET,IAAI;QAAA2C,IAAA,EAACA,IAAI;QAAEC,QAAA,GAAWmG,cAAA,CAAenG,QAAQ;QAAEC,IAAA,GAAOkG,cAAA,CAAelG,IAAI;QAAEC,MAAA,GAASiG,cAAA,CAAejG;MAAM,CAAC,GAAGJ,IAAA;MAC7G,IAAIhD,IAAA,GAAOiD,IAAA,IAAQ,IAAAtB,uBAAe,EAAE,IAAAC,uBAAe,EAAEtB,IAAA;MACrD,IAAIoD,MAAA,GAASC,yCAAA,CAAuBrD,IAAA,EAAM;kBAAC4C,QAAA;gBAAUE;MAAM;MAC3D,IAAI9C,IAAA,CAAKkG,QAAQ,CAACxG,IAAA,GAChB0D,MAAA,CAAOE,WAAW,GAAG5D,IAAA,MAChB;QACL,IAAIsJ,IAAA,GAAOC,0BAAA,CAAK7F,MAAA;QAChB,IAAI4F,IAAA,EACFxF,kCAAA,CAAawF,IAAA,EAAM;QAErB,OAAOA,IAAA,aAAAA,IAAA,cAAAA,IAAA,GAAQ;MACjB;MACA,IAAIpF,YAAA,GAAeR,MAAA,CAAOQ,YAAY;MACtC,IAAI,CAACA,YAAA,IAAgBf,IAAA,EAAM;QACzBO,MAAA,CAAOE,WAAW,GAAGtD,IAAA;QACrB,IAAIkJ,QAAA,GAAWD,0BAAA,CAAK7F,MAAA;QACpB,IAAI,CAAC8F,QAAA;UACH;UACA,OAAO;QAETtF,YAAA,GAAesF,QAAA;MACjB;MACA,IAAItF,YAAA,EACFJ,kCAAA,CAAaI,YAAA,EAAc;MAE7B,OAAOA,YAAA,aAAAA,YAAA,cAAAA,YAAA,GAAgB;IACzB;IACAC,WAAWnB,IAAA,GAAOqG,cAAc;MAC9B,IAAI/I,IAAA,GAAOsC,GAAA,CAAI/B,OAAO;MACtB,IAAI,CAACP,IAAA,EACH,OAAO;MAET,IAAI;QAAC4C,QAAA,GAAWmG,cAAA,CAAenG,QAAQ;QAAEE,MAAA,GAASiG,cAAA,CAAejG;MAAM,CAAC,GAAGJ,IAAA;MAC3E,IAAIU,MAAA,GAASC,yCAAA,CAAuBrD,IAAA,EAAM;kBAAC4C,QAAA;gBAAUE;MAAM;MAC3D,IAAIS,QAAA,GAAWH,MAAA,CAAOG,QAAQ;MAC9B,IAAIA,QAAA,EACFC,kCAAA,CAAaD,QAAA,EAAU;MAEzB,OAAOA,QAAA;IACT;IACAO,UAAUpB,IAAA,GAAOqG,cAAc;MAC7B,IAAI/I,IAAA,GAAOsC,GAAA,CAAI/B,OAAO;MACtB,IAAI,CAACP,IAAA,EACH,OAAO;MAET,IAAI;QAAC4C,QAAA,GAAWmG,cAAA,CAAenG,QAAQ;QAAEE,MAAA,GAASiG,cAAA,CAAejG;MAAM,CAAC,GAAGJ,IAAA;MAC3E,IAAIU,MAAA,GAASC,yCAAA,CAAuBrD,IAAA,EAAM;kBAAC4C,QAAA;gBAAUE;MAAM;MAC3D,IAAIkG,IAAA,GAAOC,0BAAA,CAAK7F,MAAA;MAChB,IAAI4F,IAAA,EACFxF,kCAAA,CAAawF,IAAA,EAAM;MAErB,OAAOA,IAAA,aAAAA,IAAA,cAAAA,IAAA,GAAQ;IACjB;EACF;AACF;AAEA,SAASC,2BAAK7F,MAAqC;EACjD,IAAI4F,IAAA,GAAqCzH,SAAA;EACzC,IAAI4H,IAAA;EACJ,GAAG;IACDA,IAAA,GAAO/F,MAAA,CAAOgG,SAAS;IACvB,IAAID,IAAA,EACFH,IAAA,GAAOG,IAAA;EAEX,SAASA,IAAA;EACT,OAAOH,IAAA;AACT;AAGA,MAAMK,0BAAA;EASJ,IAAIC,KAAA,EAAO;IACT,OAAO,IAAI,CAACC,OAAO,CAACD,IAAI;EAC1B;EAEArJ,YAAYuJ,IAAc,EAAE;IAC1B,OAAO,IAAI,CAACD,OAAO,CAACE,GAAG,CAACD,IAAA;EAC1B;EAEAE,YAAYnK,QAAkB,EAAEO,MAAgB,EAAEiH,aAAgC,EAAE;IAClF,IAAIvH,UAAA,GAAa,IAAI,CAAC+J,OAAO,CAACE,GAAG,CAAC3J,MAAA,aAAAA,MAAA,cAAAA,MAAA,GAAU;IAC5C,IAAI,CAACN,UAAA,EACH;IAEF,IAAIE,IAAA,GAAO,IAAIE,8BAAA,CAAS;gBAACL;IAAQ;IACjCC,UAAA,CAAWY,QAAQ,CAACV,IAAA;IACpBA,IAAA,CAAKI,MAAM,GAAGN,UAAA;IACd,IAAI,CAAC+J,OAAO,CAACI,GAAG,CAACpK,QAAA,EAAUG,IAAA;IAC3B,IAAIqH,aAAA,EACFrH,IAAA,CAAKqH,aAAa,GAAGA,aAAA;EAEzB;EAEA1G,QAAQX,IAAc,EAAE;IACtB,IAAI,CAAC6J,OAAO,CAACI,GAAG,CAACjK,IAAA,CAAKH,QAAQ,EAAEG,IAAA;EAClC;EAEAqC,eAAexC,QAAkB,EAAE;IACjC;IACA,IAAIA,QAAA,KAAa,MACf;IAEF,IAAIG,IAAA,GAAO,IAAI,CAAC6J,OAAO,CAACE,GAAG,CAAClK,QAAA;IAC5B,IAAI,CAACG,IAAA,EACH;IAEF,IAAIF,UAAA,GAAaE,IAAA,CAAKI,MAAM;IAC5B;IACA;IACA,KAAK,IAAIS,OAAA,IAAW,IAAI,CAACmB,QAAQ,IAC/B,IACEnB,OAAA,KAAYb,IAAA,IACZA,IAAA,CAAKqH,aAAa,IAClBxG,OAAA,CAAQwG,aAAa,IACrBrH,IAAA,CAAKH,QAAQ,IACbG,IAAA,CAAKH,QAAQ,CAACgB,OAAO,IACrBkB,sCAAA,CAAiBlB,OAAA,CAAQwG,aAAa,EAAErH,IAAA,CAAKH,QAAQ,CAACgB,OAAO,GAE7DA,OAAA,CAAQwG,aAAa,GAAGrH,IAAA,CAAKqH,aAAa;IAG9C,IAAI/H,QAAA,GAAWU,IAAA,CAAKV,QAAQ;IAC5B,IAAIQ,UAAA,EAAY;MACdA,UAAA,CAAWoK,WAAW,CAAClK,IAAA;MACvB,IAAIV,QAAA,CAASsK,IAAI,GAAG,GAClBtK,QAAA,CAAS8G,OAAO,CAAC+D,KAAA,IAASrK,UAAA,IAAcA,UAAA,CAAWY,QAAQ,CAACyJ,KAAA;IAEhE;IAEA,IAAI,CAACN,OAAO,CAACO,MAAM,CAACpK,IAAA,CAAKH,QAAQ;EACnC;EAEA;EACA,CAACmC,SAAShC,IAAA,GAAiB,IAAI,CAACM,IAAI,EAAuB;IACzD,IAAIN,IAAA,CAAKH,QAAQ,IAAI,MACnB,MAAMG,IAAA;IAER,IAAIA,IAAA,CAAKV,QAAQ,CAACsK,IAAI,GAAG,GACvB,KAAK,IAAIO,KAAA,IAASnK,IAAA,CAAKV,QAAQ,EAC7B,OAAO,IAAI,CAAC0C,QAAQ,CAACmI,KAAA;EAG3B;EAEAtC,MAAA,EAAc;QAGyBwC,YAAA;IAFrC,IAAIC,OAAA,GAAU,IAAIX,0BAAA;QAEmBY,qBAAA;IADrC,KAAK,IAAIvK,IAAA,IAAQ,IAAI,CAACgC,QAAQ,IAC5BsI,OAAA,CAAQN,WAAW,CAAChK,IAAA,CAAKH,QAAQ,EAAE,CAAA0K,qBAAA,IAAAF,YAAA,GAAArK,IAAA,CAAKI,MAAM,cAAXiK,YAAA,uBAAAA,YAAA,CAAaxK,QAAQ,cAArB0K,qBAAA,cAAAA,qBAAA,GAAyB,MAAMvK,IAAA,CAAKqH,aAAa;IAEtF,OAAOiD,OAAA;EACT;EApFAE,YAAA,EAAc;SAFNX,OAAA,GAAU,IAAIY,GAAA;IAGpB,IAAI,CAACnK,IAAI,GAAG,IAAIJ,8BAAA,CAAS;MAACL,QAAA,EAAU;IAAI;IACxC,IAAI,CAACgK,OAAO,CAACI,GAAG,CAAC,MAAM,IAAI,CAAC3J,IAAI;EAClC;AAkFF;AAEA,MAAMJ,8BAAA;EAUJQ,SAASV,IAAc,EAAE;IACvB,IAAI,CAACV,QAAQ,CAACoL,GAAG,CAAC1K,IAAA;IAClBA,IAAA,CAAKI,MAAM,GAAG,IAAI;EACpB;EACA8J,YAAYlK,IAAc,EAAE;IAC1B,IAAI,CAACV,QAAQ,CAAC8K,MAAM,CAACpK,IAAA;IACrBA,IAAA,CAAKI,MAAM,GAAGyB,SAAA;EAChB;EAVA2I,YAAYnL,KAA2B,EAAE;SAHlCC,QAAA,GAA0B,IAAIqL,GAAA;SAC9BpL,OAAA,GAAU;IAGf,IAAI,CAACM,QAAQ,GAAGR,KAAA,CAAMQ,QAAQ;EAChC;AASF;AAEO,IAAIQ,yCAAA,GAAiB,IAAIsJ,0BAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}