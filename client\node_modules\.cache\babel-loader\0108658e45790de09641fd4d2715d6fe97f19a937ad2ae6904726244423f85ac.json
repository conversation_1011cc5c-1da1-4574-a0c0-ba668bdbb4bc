{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{Container,Typography,Box,Paper,CircularProgress,Alert,Dialog,DialogTitle,DialogContent,DialogActions,Button,Avatar,Chip,Grid,useTheme,IconButton,Tooltip}from'@mui/material';import{CalendarToday as CalendarIcon,AccessTime as TimeIcon,Person as PersonIcon,AttachMoney as MoneyIcon,ChevronLeft as ChevronLeftIcon,ChevronRight as ChevronRightIcon,VideoCall as VideoCallIcon,Cancel as CancelIcon,Close as CloseIcon}from'@mui/icons-material';import axios from'axios';import{format,startOfWeek,addDays,addWeeks,subWeeks}from'date-fns';import{ar,enUS}from'date-fns/locale';import{useAuth}from'../../contexts/AuthContext';import Layout from'../../components/Layout';import WeeklyBookingsTable from'../../components/WeeklyBookingsTable';import{convertFromDatabaseTime,formatDateInStudentTimezone,getCurrentTimeInTimezone,parseTimezoneOffset}from'../../utils/timezone';import moment from'moment-timezone';import VideoSDKMeeting from'../../components/meeting/VideoSDKMeeting';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TeacherBookings=()=>{const{t,i18n}=useTranslation();const{token}=useAuth();const theme=useTheme();const isRtl=i18n.language==='ar';const[bookings,setBookings]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[selectedBooking,setSelectedBooking]=useState(null);const[detailsDialogOpen,setDetailsDialogOpen]=useState(false);const[teacherProfile,setTeacherProfile]=useState(null);const[cancelDialogOpen,setCancelDialogOpen]=useState(false);const[cancellingBooking,setCancellingBooking]=useState(false);const[openMeeting,setOpenMeeting]=useState(false);const[currentMeeting,setCurrentMeeting]=useState(null);const[currentTime,setCurrentTime]=useState(new Date());const[availableHours,setAvailableHours]=useState(null);const[weeklyBreaks,setWeeklyBreaks]=useState([]);// Week navigation\nconst[currentWeekStart,setCurrentWeekStart]=useState(()=>{const today=new Date();return startOfWeek(today,{weekStartsOn:1});// Start from current week\n});// Days of the week (format expected by WeeklyBookingsTable)\nconst daysOfWeek=['monday','tuesday','wednesday','thursday','friday','saturday','sunday'];// Week navigation functions\nconst goToPreviousWeek=()=>{const previousWeek=subWeeks(currentWeekStart,1);setCurrentWeekStart(previousWeek);};const goToNextWeek=()=>{const nextWeek=addWeeks(currentWeekStart,1);const today=new Date();const oneYearAhead=addWeeks(today,52);// One year ahead from today\nconst maxWeek=startOfWeek(oneYearAhead,{weekStartsOn:1});// Don't allow going beyond one year ahead\nif(nextWeek<=maxWeek){setCurrentWeekStart(nextWeek);}};// Check if navigation buttons should be disabled\nconst isPreviousWeekDisabled=()=>false;const isNextWeekDisabled=()=>{const nextWeek=addWeeks(currentWeekStart,1);const today=new Date();const oneYearAhead=addWeeks(today,52);// One year ahead from today\nconst maxWeek=startOfWeek(oneYearAhead,{weekStartsOn:1});return nextWeek>maxWeek;};// Update current time every second\nuseEffect(()=>{const timeInterval=setInterval(()=>{setCurrentTime(new Date());},1000);return()=>clearInterval(timeInterval);},[]);// Fetch bookings\nuseEffect(()=>{const fetchBookings=async()=>{try{setLoading(true);const{data}=await axios.get('/api/bookings/teacher',{headers:{'Authorization':`Bearer ${token}`}});if(data.success){console.log('Teacher bookings data:',data.data);// Make sure all bookings have the correct data types\nconst processedBookings=data.data.map(booking=>({...booking,price_per_lesson:parseFloat(booking.price_per_lesson||0),duration:booking.duration?String(booking.duration):'50'}));console.log('Processed teacher bookings:',processedBookings);setBookings(processedBookings);// Set teacher profile with timezone information\nif(data.teacherTimezone){setTeacherProfile({timezone:data.teacherTimezone});}// Fetch teacher's available hours\nfetchAvailableHours();// Fetch weekly breaks\nfetchWeeklyBreaks();}else{setError(data.message||t('bookings.fetchError'));}}catch(error){var _error$response,_error$response$data;console.error('Error fetching teacher bookings:',error);setError(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||t('bookings.fetchError'));}finally{setLoading(false);}};if(token){fetchBookings();}},[token,t]);// Fetch weekly breaks when week changes\nuseEffect(()=>{if(token){fetchWeeklyBreaks();}},[currentWeekStart,token]);// Fetch available hours\nconst fetchAvailableHours=async()=>{try{const{data}=await axios.get('/api/teacher/profile',{headers:{'Authorization':`Bearer ${token}`}});if(data.success&&data.profile.available_hours){const parsedHours=typeof data.profile.available_hours==='string'?JSON.parse(data.profile.available_hours):data.profile.available_hours;console.log('Available hours loaded:',parsedHours);setAvailableHours(parsedHours);}}catch(error){console.error('Error fetching available hours:',error);}};// Fetch weekly breaks\nconst fetchWeeklyBreaks=async()=>{try{// Calculate start and end dates for the current week\nconst startDate=format(currentWeekStart,'yyyy-MM-dd');const endDate=format(addDays(currentWeekStart,6),'yyyy-MM-dd');const{data}=await axios.get(`/api/teacher/weekly-breaks?start_date=${startDate}&end_date=${endDate}`,{headers:{'Authorization':`Bearer ${token}`}});if(data.success){console.log('🔍 FRONTEND: Weekly breaks received from server:',data.data);console.log('🔍 FRONTEND: Setting weeklyBreaks state to:',data.data);setWeeklyBreaks(data.data);}}catch(error){console.error('Error fetching weekly breaks:',error);}};// Handle take break\nconst handleTakeBreak=async breakSlot=>{try{// Use teacher's timezone for date\nconst breakDate=breakSlot.date;const breakDateStr=`${breakDate.getFullYear()}-${String(breakDate.getMonth()+1).padStart(2,'0')}-${String(breakDate.getDate()).padStart(2,'0')}`;const breakData={date:breakDateStr,hour:breakSlot.hour,minute:breakSlot.minute};const{data}=await axios.post('/api/teacher/take-break',breakData,{headers:{'Authorization':`Bearer ${token}`}});if(data.success){// Refresh weekly breaks to get updated data\nfetchWeeklyBreaks();// Show success message\nalert(t('bookings.breakTakenSuccess','تم أخذ الراحة بنجاح'));}}catch(error){console.error('Error taking break:',error);alert(t('bookings.breakTakenError','خطأ في أخذ الراحة'));}};// Handle view details\nconst handleViewDetails=booking=>{setSelectedBooking(booking);setDetailsDialogOpen(true);};// Handle cancel booking\nconst handleCancelBookingClick=booking=>{setSelectedBooking(booking);setCancelDialogOpen(true);};// Handle join meeting\nconst handleJoinMeeting=async booking=>{try{// Check if room_name exists from the booking data\nif(!booking.room_name){console.error('No room_name found for booking:',booking);alert(t('meetings.noRoomError')||'Meeting room not found');return;}// Check if meeting_id exists\nif(!booking.meeting_id){console.error('No meeting_id found for booking:',booking);alert(t('meetings.noMeetingError')||'Meeting ID not found');return;}console.log('Joining meeting with data:',{room_name:booking.room_name,meeting_id:booking.meeting_id,datetime:booking.datetime,duration:booking.duration});// Validate room\nconst response=await axios.get(`/meetings/${booking.room_name}/validate`);setCurrentMeeting({...booking,room_name:booking.room_name});setOpenMeeting(true);}catch(error){console.error('Error joining meeting:',error);alert(t('meetings.joinError'));}};const handleCloseMeeting=()=>{setOpenMeeting(false);setCurrentMeeting(null);};// Get meeting status from database directly\nconst getMeetingStatus=booking=>{return booking.status||'scheduled';};// Check if user can join meeting\nconst canJoinMeeting=booking=>{if(!booking||!teacherProfile)return false;const currentStatus=getMeetingStatus(booking);if(currentStatus==='cancelled'||currentStatus==='completed'){return false;}const meetingStartTime=new Date(booking.datetime);const meetingEndTime=new Date(booking.datetime);meetingEndTime.setMinutes(meetingEndTime.getMinutes()+parseInt(booking.duration));const now=new Date();return now>=meetingStartTime&&now<meetingEndTime;};// Handle booking cancellation\nconst handleCancelBooking=async()=>{if(!selectedBooking)return;try{setCancellingBooking(true);const{data}=await axios.put(`/bookings/${selectedBooking.id}/cancel`,{},{headers:{'Authorization':`Bearer ${token}`}});if(data.success){// Update the booking status in the local state\nsetBookings(prevBookings=>prevBookings.map(booking=>booking.id===selectedBooking.id?{...booking,status:'cancelled'}:booking));alert(t('bookings.cancelSuccess'));}else{alert(data.message||t('bookings.cancelError'));}}catch(error){var _error$response2,_error$response2$data;console.error('Error cancelling booking:',error);alert(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||t('bookings.cancelError'));}finally{setCancellingBooking(false);setCancelDialogOpen(false);setDetailsDialogOpen(false);setSelectedBooking(null);}};// Get status chip color\nconst getStatusColor=status=>{switch(status){case'scheduled':return'primary';case'completed':return'success';case'cancelled':return'error';case'issue_reported':return'warning';case'ongoing':return'info';default:return'default';}};// Get translated status text\nconst getStatusText=status=>{return t(`bookings.statusValues.${status}`,{defaultValue:status.charAt(0).toUpperCase()+status.slice(1)});};// Format booking date in teacher's timezone\nconst formatBookingDate=datetime=>{if(!teacherProfile||!teacherProfile.timezone){return format(new Date(datetime),'PPP',{locale:isRtl?ar:enUS});}const formattedDate=formatDateInStudentTimezone(datetime,teacherProfile.timezone,'YYYY-MM-DD');return moment(formattedDate,'YYYY-MM-DD').format('MMMM D, YYYY');};// Format booking time in teacher's timezone\nconst formatBookingTime=datetime=>{if(!teacherProfile||!teacherProfile.timezone){return format(new Date(datetime),'p',{locale:isRtl?ar:enUS});}const formattedDateTime=formatDateInStudentTimezone(datetime,teacherProfile.timezone,'YYYY-MM-DD HH:mm:ss');return moment(formattedDateTime,'YYYY-MM-DD HH:mm:ss').format('h:mm A');};// Calculate lesson price based on duration\nconst calculateLessonPrice=(pricePerLesson,duration)=>{const durationNum=parseInt(duration,10);return durationNum===50?pricePerLesson:pricePerLesson/2;};// Render details dialog\nconst renderDetailsDialog=()=>{if(!selectedBooking)return null;const lessonPrice=calculateLessonPrice(selectedBooking.price_per_lesson,selectedBooking.duration);return/*#__PURE__*/_jsxs(Dialog,{open:detailsDialogOpen,onClose:()=>setDetailsDialogOpen(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{bgcolor:'primary.main',color:'white'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(CalendarIcon,{}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:t('bookings.bookingDetails')})]})}),/*#__PURE__*/_jsx(DialogContent,{sx:{mt:2},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Avatar,{src:selectedBooking.student_picture,alt:selectedBooking.student_name,sx:{mr:2,width:56,height:56}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:selectedBooking.student_name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:selectedBooking.student_email})]})]})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(CalendarIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.date')})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:formatBookingDate(selectedBooking.datetime)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(TimeIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.time')})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:formatBookingTime(selectedBooking.datetime)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(TimeIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.duration')})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",children:[selectedBooking.duration,\" \",t('bookings.minutes')]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(MoneyIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.price')})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",children:[\"$\",lessonPrice.toFixed(2)]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(PersonIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.status')})]}),/*#__PURE__*/_jsx(Chip,{label:getStatusText(selectedBooking.status),color:getStatusColor(selectedBooking.status),variant:\"filled\"})]})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailsDialogOpen(false),children:t('common.close')}),selectedBooking&&/*#__PURE__*/_jsx(Button,{onClick:()=>canJoinMeeting(selectedBooking)&&handleJoinMeeting(selectedBooking),color:canJoinMeeting(selectedBooking)?\"success\":\"inherit\",variant:canJoinMeeting(selectedBooking)?\"contained\":\"outlined\",startIcon:/*#__PURE__*/_jsx(VideoCallIcon,{}),disabled:!canJoinMeeting(selectedBooking),sx:{mr:1,...(canJoinMeeting(selectedBooking)?{}:{color:theme.palette.grey[500],borderColor:theme.palette.grey[300],backgroundColor:theme.palette.grey[100],'&:hover':{backgroundColor:theme.palette.grey[200]}})},children:canJoinMeeting(selectedBooking)?t('meetings.join'):t('meetings.notStarted')}),(selectedBooking===null||selectedBooking===void 0?void 0:selectedBooking.status)==='scheduled'&&/*#__PURE__*/_jsx(Button,{onClick:()=>{setDetailsDialogOpen(false);setCancelDialogOpen(true);},color:\"error\",variant:\"contained\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),children:t('bookings.cancel')})]})]});};// Cancel confirmation dialog\nconst renderCancelDialog=()=>/*#__PURE__*/_jsxs(Dialog,{open:cancelDialogOpen,onClose:()=>setCancelDialogOpen(false),children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[t('bookings.confirmCancel'),/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:()=>setCancelDialogOpen(false),sx:{position:'absolute',right:8,top:8},children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:t('bookings.cancelWarning')}),selectedBooking&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.student'),\":\"]}),\" \",selectedBooking.student_name]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.date'),\":\"]}),\" \",formatBookingDate(selectedBooking.datetime)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.time'),\":\"]}),\" \",formatBookingTime(selectedBooking.datetime)]})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setCancelDialogOpen(false),children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{onClick:handleCancelBooking,color:\"error\",variant:\"contained\",disabled:cancellingBooking,children:cancellingBooking?t('bookings.cancelling'):t('bookings.confirmCancelButton')})]})]});return/*#__PURE__*/_jsxs(Layout,{children:[/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",sx:{py:4},children:[/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:3,mb:4,bgcolor:'primary.main',color:'white'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,sx:{fontWeight:'bold'},children:t('teacher.weeklyBookings')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{opacity:0.9},children:t('teacher.weeklyBookingsDescription')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'right'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{opacity:0.8,mb:0.5},children:t('booking.weekNavigation')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:'bold'},children:[\"\\uD83D\\uDCC5 \",format(currentWeekStart,'MMM d',{locale:isRtl?ar:enUS}),\" - \",format(addDays(currentWeekStart,6),'MMM d, yyyy',{locale:isRtl?ar:enUS})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[/*#__PURE__*/_jsx(Tooltip,{title:t('booking.previousWeek'),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:goToPreviousWeek,disabled:isPreviousWeekDisabled(),sx:{color:'white',bgcolor:'rgba(255, 255, 255, 0.1)','&:hover':{bgcolor:'rgba(255, 255, 255, 0.2)'},'&:disabled':{color:'rgba(255, 255, 255, 0.3)',bgcolor:'rgba(255, 255, 255, 0.05)'}},children:/*#__PURE__*/_jsx(ChevronLeftIcon,{})})})}),/*#__PURE__*/_jsx(Tooltip,{title:t('booking.nextWeek'),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:goToNextWeek,disabled:isNextWeekDisabled(),sx:{color:'white',bgcolor:'rgba(255, 255, 255, 0.1)','&:hover':{bgcolor:'rgba(255, 255, 255, 0.2)'},'&:disabled':{color:'rgba(255, 255, 255, 0.3)',bgcolor:'rgba(255, 255, 255, 0.05)'}},children:/*#__PURE__*/_jsx(ChevronRightIcon,{})})})})]})]})]})}),/*#__PURE__*/_jsx(Paper,{elevation:2,sx:{p:2,mb:3,bgcolor:'background.paper'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Box,{sx:{bgcolor:'primary.main',color:'white',p:1,borderRadius:1,minWidth:40,textAlign:'center'},children:\"\\uD83D\\uDD50\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:0.5},children:t('bookings.currentTime')}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:'bold'},children:teacherProfile!==null&&teacherProfile!==void 0&&teacherProfile.timezone?moment().utc().add(parseTimezoneOffset(teacherProfile.timezone),'minutes').format('dddd, MMMM D, YYYY [at] h:mm A'):format(new Date(),'PPpp',{locale:isRtl?ar:enUS})}),(teacherProfile===null||teacherProfile===void 0?void 0:teacherProfile.timezone)&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{opacity:0.8},children:teacherProfile.timezone})]})]})}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',py:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}):error?/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:4},children:error}):/*#__PURE__*/_jsx(WeeklyBookingsTable,{bookings:bookings,loading:loading,currentWeekStart:currentWeekStart,daysOfWeek:daysOfWeek,onViewDetails:handleViewDetails,onCancelBooking:handleCancelBookingClick// Now teachers can cancel bookings\n,studentProfile:teacherProfile// Pass teacher profile for timezone conversion\n,formatBookingTime:formatBookingTime,getStatusColor:getStatusColor,getStatusText:getStatusText,isTeacherView:true// Add this prop to distinguish teacher view\n,availableHours:availableHours// Pass available hours to show available slots\n,onTakeBreak:handleTakeBreak// Pass take break handler\n,weeklyBreaks:weeklyBreaks// Pass weekly breaks\n}),renderDetailsDialog(),renderCancelDialog()]}),/*#__PURE__*/_jsx(Dialog,{fullScreen:true,open:openMeeting,onClose:handleCloseMeeting,children:currentMeeting&&/*#__PURE__*/_jsx(VideoSDKMeeting,{roomId:currentMeeting.room_name,meetingId:currentMeeting.meeting_id,meetingData:currentMeeting,onClose:handleCloseMeeting})})]});};export default TeacherBookings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Typography", "Box", "Paper", "CircularProgress", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Avatar", "Chip", "Grid", "useTheme", "IconButton", "<PERSON><PERSON><PERSON>", "CalendarToday", "CalendarIcon", "AccessTime", "TimeIcon", "Person", "PersonIcon", "AttachMoney", "MoneyIcon", "ChevronLeft", "ChevronLeftIcon", "ChevronRight", "ChevronRightIcon", "VideoCall", "VideoCallIcon", "Cancel", "CancelIcon", "Close", "CloseIcon", "axios", "format", "startOfWeek", "addDays", "addWeeks", "subWeeks", "ar", "enUS", "useAuth", "Layout", "WeeklyBookingsTable", "convertFromDatabaseTime", "formatDateInStudentTimezone", "getCurrentTimeInTimezone", "parseTimezoneOffset", "moment", "VideoSDKMeeting", "jsx", "_jsx", "jsxs", "_jsxs", "TeacherBookings", "t", "i18n", "token", "theme", "isRtl", "language", "bookings", "setBookings", "loading", "setLoading", "error", "setError", "selectedBooking", "setSelectedBooking", "detailsDialogOpen", "setDetailsDialogOpen", "teacher<PERSON><PERSON><PERSON><PERSON>", "setTeacherProfile", "cancelDialogOpen", "setCancelDialogOpen", "cancellingBooking", "setCancellingBooking", "openMeeting", "setOpenMeeting", "currentMeeting", "setCurrentMeeting", "currentTime", "setCurrentTime", "Date", "availableHours", "setAvailableHours", "weeklyBreaks", "setWeeklyBreaks", "currentWeekStart", "setCurrentWeekStart", "today", "weekStartsOn", "daysOfWeek", "goToPreviousWeek", "previousWeek", "goToNextWeek", "nextWeek", "oneYearAhead", "maxWeek", "isPreviousWeekDisabled", "isNextWeekDisabled", "timeInterval", "setInterval", "clearInterval", "fetchBookings", "data", "get", "headers", "success", "console", "log", "processedBookings", "map", "booking", "price_per_lesson", "parseFloat", "duration", "String", "teacherTimezone", "timezone", "fetchAvailableHours", "fetchWeeklyBreaks", "message", "_error$response", "_error$response$data", "response", "profile", "available_hours", "parsedHours", "JSON", "parse", "startDate", "endDate", "handleTakeBreak", "breakSlot", "breakDate", "date", "breakDateStr", "getFullYear", "getMonth", "padStart", "getDate", "breakData", "hour", "minute", "post", "alert", "handleViewDetails", "handleCancelBookingClick", "handleJoinMeeting", "room_name", "meeting_id", "datetime", "handleCloseMeeting", "getMeetingStatus", "status", "canJoinMeeting", "currentStatus", "meetingStartTime", "meetingEndTime", "setMinutes", "getMinutes", "parseInt", "now", "handleCancelBooking", "put", "id", "prevBookings", "_error$response2", "_error$response2$data", "getStatusColor", "getStatusText", "defaultValue", "char<PERSON>t", "toUpperCase", "slice", "formatBookingDate", "locale", "formattedDate", "formatBookingTime", "formattedDateTime", "calculateLessonPrice", "pricePer<PERSON><PERSON>on", "durationNum", "renderDetailsDialog", "lessonPrice", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "sx", "bgcolor", "color", "display", "alignItems", "gap", "variant", "mt", "container", "spacing", "item", "xs", "mb", "src", "student_picture", "alt", "student_name", "mr", "width", "height", "student_email", "sm", "toFixed", "label", "onClick", "startIcon", "disabled", "palette", "grey", "borderColor", "backgroundColor", "renderCancelDialog", "position", "right", "top", "gutterBottom", "py", "elevation", "p", "justifyContent", "flexWrap", "fontWeight", "opacity", "textAlign", "title", "borderRadius", "min<PERSON><PERSON><PERSON>", "utc", "add", "severity", "onViewDetails", "onCancelBooking", "studentProfile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onTakeBreak", "fullScreen", "roomId", "meetingId", "meetingData"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/teacher/Bookings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  CircularProgress,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Avatar,\n  Chip,\n  Grid,\n  useTheme,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport {\n  CalendarToday as CalendarIcon,\n  AccessTime as TimeIcon,\n  Person as PersonIcon,\n  AttachMoney as MoneyIcon,\n  ChevronLeft as ChevronLeftIcon,\n  ChevronRight as ChevronRightIcon,\n  VideoCall as VideoCallIcon,\n  Cancel as CancelIcon,\n  Close as CloseIcon\n} from '@mui/icons-material';\nimport axios from 'axios';\nimport { format, startOfWeek, addDays, addWeeks, subWeeks } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport WeeklyBookingsTable from '../../components/WeeklyBookingsTable';\nimport { convertFromDatabaseTime, formatDateInStudentTimezone, getCurrentTimeInTimezone, parseTimezoneOffset } from '../../utils/timezone';\nimport moment from 'moment-timezone';\nimport VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';\n\nconst TeacherBookings = () => {\n  const { t, i18n } = useTranslation();\n  const { token } = useAuth();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  const [bookings, setBookings] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedBooking, setSelectedBooking] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n  const [teacherProfile, setTeacherProfile] = useState(null);\n  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);\n  const [cancellingBooking, setCancellingBooking] = useState(false);\n  const [openMeeting, setOpenMeeting] = useState(false);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [availableHours, setAvailableHours] = useState(null);\n  const [weeklyBreaks, setWeeklyBreaks] = useState([]);\n\n  // Week navigation\n  const [currentWeekStart, setCurrentWeekStart] = useState(() => {\n    const today = new Date();\n    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week\n  });\n\n  // Days of the week (format expected by WeeklyBookingsTable)\n  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n\n  // Week navigation functions\n  const goToPreviousWeek = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    setCurrentWeekStart(previousWeek);\n  };\n\n  const goToNextWeek = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n\n    // Don't allow going beyond one year ahead\n    if (nextWeek <= maxWeek) {\n      setCurrentWeekStart(nextWeek);\n    }\n  };\n\n  // Check if navigation buttons should be disabled\n  const isPreviousWeekDisabled = () => false;\n\n  const isNextWeekDisabled = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n    return nextWeek > maxWeek;\n  };\n\n  // Update current time every second\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Fetch bookings\n  useEffect(() => {\n    const fetchBookings = async () => {\n      try {\n        setLoading(true);\n        const { data } = await axios.get('/api/bookings/teacher', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success) {\n          console.log('Teacher bookings data:', data.data);\n\n          // Make sure all bookings have the correct data types\n          const processedBookings = data.data.map(booking => ({\n            ...booking,\n            price_per_lesson: parseFloat(booking.price_per_lesson || 0),\n            duration: booking.duration ? String(booking.duration) : '50'\n          }));\n\n          console.log('Processed teacher bookings:', processedBookings);\n          setBookings(processedBookings);\n\n          // Set teacher profile with timezone information\n          if (data.teacherTimezone) {\n            setTeacherProfile({ timezone: data.teacherTimezone });\n          }\n\n          // Fetch teacher's available hours\n          fetchAvailableHours();\n\n          // Fetch weekly breaks\n          fetchWeeklyBreaks();\n        } else {\n          setError(data.message || t('bookings.fetchError'));\n        }\n      } catch (error) {\n        console.error('Error fetching teacher bookings:', error);\n        setError(error.response?.data?.message || t('bookings.fetchError'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (token) {\n      fetchBookings();\n    }\n  }, [token, t]);\n\n  // Fetch weekly breaks when week changes\n  useEffect(() => {\n    if (token) {\n      fetchWeeklyBreaks();\n    }\n  }, [currentWeekStart, token]);\n\n  // Fetch available hours\n  const fetchAvailableHours = async () => {\n    try {\n      const { data } = await axios.get('/api/teacher/profile', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success && data.profile.available_hours) {\n        const parsedHours = typeof data.profile.available_hours === 'string'\n          ? JSON.parse(data.profile.available_hours)\n          : data.profile.available_hours;\n        console.log('Available hours loaded:', parsedHours);\n        setAvailableHours(parsedHours);\n      }\n    } catch (error) {\n      console.error('Error fetching available hours:', error);\n    }\n  };\n\n  // Fetch weekly breaks\n  const fetchWeeklyBreaks = async () => {\n    try {\n      // Calculate start and end dates for the current week\n      const startDate = format(currentWeekStart, 'yyyy-MM-dd');\n      const endDate = format(addDays(currentWeekStart, 6), 'yyyy-MM-dd');\n\n      const { data } = await axios.get(`/api/teacher/weekly-breaks?start_date=${startDate}&end_date=${endDate}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        console.log('🔍 FRONTEND: Weekly breaks received from server:', data.data);\n        console.log('🔍 FRONTEND: Setting weeklyBreaks state to:', data.data);\n        setWeeklyBreaks(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching weekly breaks:', error);\n    }\n  };\n\n  // Handle take break\n  const handleTakeBreak = async (breakSlot) => {\n    try {\n      // Use teacher's timezone for date\n      const breakDate = breakSlot.date;\n      const breakDateStr = `${breakDate.getFullYear()}-${String(breakDate.getMonth() + 1).padStart(2, '0')}-${String(breakDate.getDate()).padStart(2, '0')}`;\n\n      const breakData = {\n        date: breakDateStr,\n        hour: breakSlot.hour,\n        minute: breakSlot.minute\n      };\n\n      const { data } = await axios.post('/api/teacher/take-break', breakData, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Refresh weekly breaks to get updated data\n        fetchWeeklyBreaks();\n\n        // Show success message\n        alert(t('bookings.breakTakenSuccess', 'تم أخذ الراحة بنجاح'));\n      }\n    } catch (error) {\n      console.error('Error taking break:', error);\n      alert(t('bookings.breakTakenError', 'خطأ في أخذ الراحة'));\n    }\n  };\n\n  // Handle view details\n  const handleViewDetails = (booking) => {\n    setSelectedBooking(booking);\n    setDetailsDialogOpen(true);\n  };\n\n  // Handle cancel booking\n  const handleCancelBookingClick = (booking) => {\n    setSelectedBooking(booking);\n    setCancelDialogOpen(true);\n  };\n\n  // Handle join meeting\n  const handleJoinMeeting = async (booking) => {\n    try {\n      // Check if room_name exists from the booking data\n      if (!booking.room_name) {\n        console.error('No room_name found for booking:', booking);\n        alert(t('meetings.noRoomError') || 'Meeting room not found');\n        return;\n      }\n\n      // Check if meeting_id exists\n      if (!booking.meeting_id) {\n        console.error('No meeting_id found for booking:', booking);\n        alert(t('meetings.noMeetingError') || 'Meeting ID not found');\n        return;\n      }\n\n      console.log('Joining meeting with data:', {\n        room_name: booking.room_name,\n        meeting_id: booking.meeting_id,\n        datetime: booking.datetime,\n        duration: booking.duration\n      });\n\n      // Validate room\n      const response = await axios.get(`/meetings/${booking.room_name}/validate`);\n      setCurrentMeeting({ ...booking, room_name: booking.room_name });\n      setOpenMeeting(true);\n    } catch (error) {\n      console.error('Error joining meeting:', error);\n      alert(t('meetings.joinError'));\n    }\n  };\n\n  const handleCloseMeeting = () => {\n    setOpenMeeting(false);\n    setCurrentMeeting(null);\n  };\n\n  // Get meeting status from database directly\n  const getMeetingStatus = (booking) => {\n    return booking.status || 'scheduled';\n  };\n\n  // Check if user can join meeting\n  const canJoinMeeting = (booking) => {\n    if (!booking || !teacherProfile) return false;\n\n    const currentStatus = getMeetingStatus(booking);\n    if (currentStatus === 'cancelled' || currentStatus === 'completed') {\n      return false;\n    }\n\n    const meetingStartTime = new Date(booking.datetime);\n    const meetingEndTime = new Date(booking.datetime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));\n    const now = new Date();\n\n    return now >= meetingStartTime && now < meetingEndTime;\n  };\n\n  // Handle booking cancellation\n  const handleCancelBooking = async () => {\n    if (!selectedBooking) return;\n\n    try {\n      setCancellingBooking(true);\n      const { data } = await axios.put(`/bookings/${selectedBooking.id}/cancel`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Update the booking status in the local state\n        setBookings(prevBookings =>\n          prevBookings.map(booking =>\n            booking.id === selectedBooking.id\n              ? { ...booking, status: 'cancelled' }\n              : booking\n          )\n        );\n        alert(t('bookings.cancelSuccess'));\n      } else {\n        alert(data.message || t('bookings.cancelError'));\n      }\n    } catch (error) {\n      console.error('Error cancelling booking:', error);\n      alert(error.response?.data?.message || t('bookings.cancelError'));\n    } finally {\n      setCancellingBooking(false);\n      setCancelDialogOpen(false);\n      setDetailsDialogOpen(false);\n      setSelectedBooking(null);\n    }\n  };\n\n  // Get status chip color\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'scheduled':\n        return 'primary';\n      case 'completed':\n        return 'success';\n      case 'cancelled':\n        return 'error';\n      case 'issue_reported':\n        return 'warning';\n      case 'ongoing':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n\n  // Get translated status text\n  const getStatusText = (status) => {\n    return t(`bookings.statusValues.${status}`, { \n      defaultValue: status.charAt(0).toUpperCase() + status.slice(1) \n    });\n  };\n\n  // Format booking date in teacher's timezone\n  const formatBookingDate = (datetime) => {\n    if (!teacherProfile || !teacherProfile.timezone) {\n      return format(new Date(datetime), 'PPP', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDate = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD');\n    return moment(formattedDate, 'YYYY-MM-DD').format('MMMM D, YYYY');\n  };\n\n  // Format booking time in teacher's timezone\n  const formatBookingTime = (datetime) => {\n    if (!teacherProfile || !teacherProfile.timezone) {\n      return format(new Date(datetime), 'p', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDateTime = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    return moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').format('h:mm A');\n  };\n\n  // Calculate lesson price based on duration\n  const calculateLessonPrice = (pricePerLesson, duration) => {\n    const durationNum = parseInt(duration, 10);\n    return durationNum === 50 ? pricePerLesson : pricePerLesson / 2;\n  };\n\n  // Render details dialog\n  const renderDetailsDialog = () => {\n    if (!selectedBooking) return null;\n\n    const lessonPrice = calculateLessonPrice(selectedBooking.price_per_lesson, selectedBooking.duration);\n\n    return (\n      <Dialog\n        open={detailsDialogOpen}\n        onClose={() => setDetailsDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <CalendarIcon />\n            <Typography variant=\"h6\">\n              {t('bookings.bookingDetails')}\n            </Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent sx={{ mt: 2 }}>\n          <Grid container spacing={3}>\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <Avatar\n                  src={selectedBooking.student_picture}\n                  alt={selectedBooking.student_name}\n                  sx={{ mr: 2, width: 56, height: 56 }}\n                />\n                <Box>\n                  <Typography variant=\"h6\">\n                    {selectedBooking.student_name}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {selectedBooking.student_email}\n                  </Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.date')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {formatBookingDate(selectedBooking.datetime)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.time')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {formatBookingTime(selectedBooking.datetime)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.duration')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {selectedBooking.duration} {t('bookings.minutes')}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <MoneyIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.price')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                ${lessonPrice.toFixed(2)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.status')}\n                </Typography>\n              </Box>\n              <Chip\n                label={getStatusText(selectedBooking.status)}\n                color={getStatusColor(selectedBooking.status)}\n                variant=\"filled\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDetailsDialogOpen(false)}>\n            {t('common.close')}\n          </Button>\n\n          {/* Join Meeting Button */}\n          {selectedBooking && (\n            <Button\n              onClick={() => canJoinMeeting(selectedBooking) && handleJoinMeeting(selectedBooking)}\n              color={canJoinMeeting(selectedBooking) ? \"success\" : \"inherit\"}\n              variant={canJoinMeeting(selectedBooking) ? \"contained\" : \"outlined\"}\n              startIcon={<VideoCallIcon />}\n              disabled={!canJoinMeeting(selectedBooking)}\n              sx={{\n                mr: 1,\n                ...(canJoinMeeting(selectedBooking) ? {} : {\n                  color: theme.palette.grey[500],\n                  borderColor: theme.palette.grey[300],\n                  backgroundColor: theme.palette.grey[100],\n                  '&:hover': {\n                    backgroundColor: theme.palette.grey[200],\n                  }\n                })\n              }}\n            >\n              {canJoinMeeting(selectedBooking) ? t('meetings.join') : t('meetings.notStarted')}\n            </Button>\n          )}\n\n          {selectedBooking?.status === 'scheduled' && (\n            <Button\n              onClick={() => {\n                setDetailsDialogOpen(false);\n                setCancelDialogOpen(true);\n              }}\n              color=\"error\"\n              variant=\"contained\"\n              startIcon={<CancelIcon />}\n            >\n              {t('bookings.cancel')}\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Cancel confirmation dialog\n  const renderCancelDialog = () => (\n    <Dialog open={cancelDialogOpen} onClose={() => setCancelDialogOpen(false)}>\n      <DialogTitle>\n        {t('bookings.confirmCancel')}\n        <IconButton\n          aria-label=\"close\"\n          onClick={() => setCancelDialogOpen(false)}\n          sx={{ position: 'absolute', right: 8, top: 8 }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n      <DialogContent>\n        <Typography variant=\"body1\">\n          {t('bookings.cancelWarning')}\n        </Typography>\n        {selectedBooking && (\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.student')}:</strong> {selectedBooking.student_name}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.date')}:</strong> {formatBookingDate(selectedBooking.datetime)}\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>{t('bookings.time')}:</strong> {formatBookingTime(selectedBooking.datetime)}\n            </Typography>\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={() => setCancelDialogOpen(false)}>\n          {t('common.cancel')}\n        </Button>\n        <Button\n          onClick={handleCancelBooking}\n          color=\"error\"\n          variant=\"contained\"\n          disabled={cancellingBooking}\n        >\n          {cancellingBooking ? t('bookings.cancelling') : t('bookings.confirmCancelButton')}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Layout>\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>\n            <Box>\n              <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                {t('teacher.weeklyBookings')}\n              </Typography>\n              <Typography variant=\"body1\" sx={{ opacity: 0.9 }}>\n                {t('teacher.weeklyBookingsDescription')}\n              </Typography>\n            </Box>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <Box sx={{ textAlign: 'right' }}>\n                <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 0.5 }}>\n                  {t('booking.weekNavigation')}\n                </Typography>\n                <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                  📅 {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                </Typography>\n              </Box>\n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Tooltip title={t('booking.previousWeek')}>\n                  <span>\n                    <IconButton\n                      onClick={goToPreviousWeek}\n                      disabled={isPreviousWeekDisabled()}\n                      sx={{\n                        color: 'white',\n                        bgcolor: 'rgba(255, 255, 255, 0.1)',\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 255, 255, 0.2)',\n                        },\n                        '&:disabled': {\n                          color: 'rgba(255, 255, 255, 0.3)',\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        }\n                      }}\n                    >\n                      <ChevronLeftIcon />\n                    </IconButton>\n                  </span>\n                </Tooltip>\n                <Tooltip title={t('booking.nextWeek')}>\n                  <span>\n                    <IconButton\n                      onClick={goToNextWeek}\n                      disabled={isNextWeekDisabled()}\n                      sx={{\n                        color: 'white',\n                        bgcolor: 'rgba(255, 255, 255, 0.1)',\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 255, 255, 0.2)',\n                        },\n                        '&:disabled': {\n                          color: 'rgba(255, 255, 255, 0.3)',\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        }\n                      }}\n                    >\n                      <ChevronRightIcon />\n                    </IconButton>\n                  </span>\n                </Tooltip>\n              </Box>\n            </Box>\n          </Box>\n        </Paper>\n\n        {/* Current Time Display */}\n        <Paper elevation={2} sx={{ p: 2, mb: 3, bgcolor: 'background.paper' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <Box sx={{\n              bgcolor: 'primary.main',\n              color: 'white',\n              p: 1,\n              borderRadius: 1,\n              minWidth: 40,\n              textAlign: 'center'\n            }}>\n              🕐\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\n                {t('bookings.currentTime')}\n              </Typography>\n              <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                {teacherProfile?.timezone ? (\n                  moment().utc().add(parseTimezoneOffset(teacherProfile.timezone), 'minutes').format('dddd, MMMM D, YYYY [at] h:mm A')\n                ) : (\n                  format(new Date(), 'PPpp', {\n                    locale: isRtl ? ar : enUS\n                  })\n                )}\n              </Typography>\n              {teacherProfile?.timezone && (\n                <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                  {teacherProfile.timezone}\n                </Typography>\n              )}\n            </Box>\n          </Box>\n        </Paper>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>\n            <CircularProgress />\n          </Box>\n        ) : error ? (\n          <Alert severity=\"error\" sx={{ mb: 4 }}>\n            {error}\n          </Alert>\n        ) : (\n          <WeeklyBookingsTable\n            bookings={bookings}\n            loading={loading}\n            currentWeekStart={currentWeekStart}\n            daysOfWeek={daysOfWeek}\n            onViewDetails={handleViewDetails}\n            onCancelBooking={handleCancelBookingClick} // Now teachers can cancel bookings\n            studentProfile={teacherProfile} // Pass teacher profile for timezone conversion\n            formatBookingTime={formatBookingTime}\n            getStatusColor={getStatusColor}\n            getStatusText={getStatusText}\n            isTeacherView={true} // Add this prop to distinguish teacher view\n            availableHours={availableHours} // Pass available hours to show available slots\n            onTakeBreak={handleTakeBreak} // Pass take break handler\n            weeklyBreaks={weeklyBreaks} // Pass weekly breaks\n          />\n        )}\n\n        {renderDetailsDialog()}\n        {renderCancelDialog()}\n      </Container>\n\n      {/* Meeting Dialog */}\n      <Dialog\n        fullScreen\n        open={openMeeting}\n        onClose={handleCloseMeeting}\n      >\n        {currentMeeting && (\n          <VideoSDKMeeting\n            roomId={currentMeeting.room_name}\n            meetingId={currentMeeting.meeting_id}\n            meetingData={currentMeeting}\n            onClose={handleCloseMeeting}\n          />\n        )}\n      </Dialog>\n    </Layout>\n  );\n};\n\nexport default TeacherBookings;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,gBAAgB,CAChBC,KAAK,CACLC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,MAAM,CACNC,MAAM,CACNC,IAAI,CACJC,IAAI,CACJC,QAAQ,CACRC,UAAU,CACVC,OAAO,KACF,eAAe,CACtB,OACEC,aAAa,GAAI,CAAAC,YAAY,CAC7BC,UAAU,GAAI,CAAAC,QAAQ,CACtBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,WAAW,GAAI,CAAAC,SAAS,CACxBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,YAAY,GAAI,CAAAC,gBAAgB,CAChCC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,KAAK,GAAI,CAAAC,SAAS,KACb,qBAAqB,CAC5B,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,WAAW,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,QAAQ,KAAQ,UAAU,CAC3E,OAASC,EAAE,CAAEC,IAAI,KAAQ,iBAAiB,CAC1C,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,mBAAmB,KAAM,sCAAsC,CACtE,OAASC,uBAAuB,CAAEC,2BAA2B,CAAEC,wBAAwB,CAAEC,mBAAmB,KAAQ,sBAAsB,CAC1I,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CACpC,MAAO,CAAAC,eAAe,KAAM,0CAA0C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvE,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG3D,cAAc,CAAC,CAAC,CACpC,KAAM,CAAE4D,KAAM,CAAC,CAAGhB,OAAO,CAAC,CAAC,CAC3B,KAAM,CAAAiB,KAAK,CAAG9C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA+C,KAAK,CAAGH,IAAI,CAACI,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACoE,OAAO,CAAEC,UAAU,CAAC,CAAGrE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsE,KAAK,CAAEC,QAAQ,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACwE,eAAe,CAAEC,kBAAkB,CAAC,CAAGzE,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAAC0E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3E,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC4E,cAAc,CAAEC,iBAAiB,CAAC,CAAG7E,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC8E,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/E,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACgF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjF,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACkF,WAAW,CAAEC,cAAc,CAAC,CAAGnF,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACoF,cAAc,CAAEC,iBAAiB,CAAC,CAAGrF,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACsF,WAAW,CAAEC,cAAc,CAAC,CAAGvF,QAAQ,CAAC,GAAI,CAAAwF,IAAI,CAAC,CAAC,CAAC,CAC1D,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1F,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC2F,YAAY,CAAEC,eAAe,CAAC,CAAG5F,QAAQ,CAAC,EAAE,CAAC,CAEpD;AACA,KAAM,CAAC6F,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9F,QAAQ,CAAC,IAAM,CAC7D,KAAM,CAAA+F,KAAK,CAAG,GAAI,CAAAP,IAAI,CAAC,CAAC,CACxB,MAAO,CAAAhD,WAAW,CAACuD,KAAK,CAAE,CAAEC,YAAY,CAAE,CAAE,CAAC,CAAC,CAAE;AAClD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAG,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CAEjG;AACA,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,YAAY,CAAGxD,QAAQ,CAACkD,gBAAgB,CAAE,CAAC,CAAC,CAClDC,mBAAmB,CAACK,YAAY,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,QAAQ,CAAG3D,QAAQ,CAACmD,gBAAgB,CAAE,CAAC,CAAC,CAC9C,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAAP,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAc,YAAY,CAAG5D,QAAQ,CAACqD,KAAK,CAAE,EAAE,CAAC,CAAE;AAC1C,KAAM,CAAAQ,OAAO,CAAG/D,WAAW,CAAC8D,YAAY,CAAE,CAAEN,YAAY,CAAE,CAAE,CAAC,CAAC,CAE9D;AACA,GAAIK,QAAQ,EAAIE,OAAO,CAAE,CACvBT,mBAAmB,CAACO,QAAQ,CAAC,CAC/B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,sBAAsB,CAAGA,CAAA,GAAM,KAAK,CAE1C,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAJ,QAAQ,CAAG3D,QAAQ,CAACmD,gBAAgB,CAAE,CAAC,CAAC,CAC9C,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAAP,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAc,YAAY,CAAG5D,QAAQ,CAACqD,KAAK,CAAE,EAAE,CAAC,CAAE;AAC1C,KAAM,CAAAQ,OAAO,CAAG/D,WAAW,CAAC8D,YAAY,CAAE,CAAEN,YAAY,CAAE,CAAE,CAAC,CAAC,CAC9D,MAAO,CAAAK,QAAQ,CAAGE,OAAO,CAC3B,CAAC,CAED;AACAtG,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyG,YAAY,CAAGC,WAAW,CAAC,IAAM,CACrCpB,cAAc,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,CAC5B,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMoB,aAAa,CAACF,YAAY,CAAC,CAC1C,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzG,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4G,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFxC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEyC,IAAK,CAAC,CAAG,KAAM,CAAAxE,KAAK,CAACyE,GAAG,CAAC,uBAAuB,CAAE,CACxDC,OAAO,CAAE,CACP,eAAe,CAAE,UAAUlD,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIgD,IAAI,CAACG,OAAO,CAAE,CAChBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEL,IAAI,CAACA,IAAI,CAAC,CAEhD;AACA,KAAM,CAAAM,iBAAiB,CAAGN,IAAI,CAACA,IAAI,CAACO,GAAG,CAACC,OAAO,GAAK,CAClD,GAAGA,OAAO,CACVC,gBAAgB,CAAEC,UAAU,CAACF,OAAO,CAACC,gBAAgB,EAAI,CAAC,CAAC,CAC3DE,QAAQ,CAAEH,OAAO,CAACG,QAAQ,CAAGC,MAAM,CAACJ,OAAO,CAACG,QAAQ,CAAC,CAAG,IAC1D,CAAC,CAAC,CAAC,CAEHP,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEC,iBAAiB,CAAC,CAC7DjD,WAAW,CAACiD,iBAAiB,CAAC,CAE9B;AACA,GAAIN,IAAI,CAACa,eAAe,CAAE,CACxB9C,iBAAiB,CAAC,CAAE+C,QAAQ,CAAEd,IAAI,CAACa,eAAgB,CAAC,CAAC,CACvD,CAEA;AACAE,mBAAmB,CAAC,CAAC,CAErB;AACAC,iBAAiB,CAAC,CAAC,CACrB,CAAC,IAAM,CACLvD,QAAQ,CAACuC,IAAI,CAACiB,OAAO,EAAInE,CAAC,CAAC,qBAAqB,CAAC,CAAC,CACpD,CACF,CAAE,MAAOU,KAAK,CAAE,KAAA0D,eAAA,CAAAC,oBAAA,CACdf,OAAO,CAAC5C,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxDC,QAAQ,CAAC,EAAAyD,eAAA,CAAA1D,KAAK,CAAC4D,QAAQ,UAAAF,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBlB,IAAI,UAAAmB,oBAAA,iBAApBA,oBAAA,CAAsBF,OAAO,GAAInE,CAAC,CAAC,qBAAqB,CAAC,CAAC,CACrE,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIP,KAAK,CAAE,CACT+C,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAAE,CAAC/C,KAAK,CAAEF,CAAC,CAAC,CAAC,CAEd;AACA3D,SAAS,CAAC,IAAM,CACd,GAAI6D,KAAK,CAAE,CACTgE,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAAE,CAACjC,gBAAgB,CAAE/B,KAAK,CAAC,CAAC,CAE7B;AACA,KAAM,CAAA+D,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACF,KAAM,CAAEf,IAAK,CAAC,CAAG,KAAM,CAAAxE,KAAK,CAACyE,GAAG,CAAC,sBAAsB,CAAE,CACvDC,OAAO,CAAE,CACP,eAAe,CAAE,UAAUlD,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIgD,IAAI,CAACG,OAAO,EAAIH,IAAI,CAACqB,OAAO,CAACC,eAAe,CAAE,CAChD,KAAM,CAAAC,WAAW,CAAG,MAAO,CAAAvB,IAAI,CAACqB,OAAO,CAACC,eAAe,GAAK,QAAQ,CAChEE,IAAI,CAACC,KAAK,CAACzB,IAAI,CAACqB,OAAO,CAACC,eAAe,CAAC,CACxCtB,IAAI,CAACqB,OAAO,CAACC,eAAe,CAChClB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEkB,WAAW,CAAC,CACnD3C,iBAAiB,CAAC2C,WAAW,CAAC,CAChC,CACF,CAAE,MAAO/D,KAAK,CAAE,CACd4C,OAAO,CAAC5C,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACzD,CACF,CAAC,CAED;AACA,KAAM,CAAAwD,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF;AACA,KAAM,CAAAU,SAAS,CAAGjG,MAAM,CAACsD,gBAAgB,CAAE,YAAY,CAAC,CACxD,KAAM,CAAA4C,OAAO,CAAGlG,MAAM,CAACE,OAAO,CAACoD,gBAAgB,CAAE,CAAC,CAAC,CAAE,YAAY,CAAC,CAElE,KAAM,CAAEiB,IAAK,CAAC,CAAG,KAAM,CAAAxE,KAAK,CAACyE,GAAG,CAAC,yCAAyCyB,SAAS,aAAaC,OAAO,EAAE,CAAE,CACzGzB,OAAO,CAAE,CACP,eAAe,CAAE,UAAUlD,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIgD,IAAI,CAACG,OAAO,CAAE,CAChBC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAEL,IAAI,CAACA,IAAI,CAAC,CAC1EI,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAEL,IAAI,CAACA,IAAI,CAAC,CACrElB,eAAe,CAACkB,IAAI,CAACA,IAAI,CAAC,CAC5B,CACF,CAAE,MAAOxC,KAAK,CAAE,CACd4C,OAAO,CAAC5C,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CAAC,CAED;AACA,KAAM,CAAAoE,eAAe,CAAG,KAAO,CAAAC,SAAS,EAAK,CAC3C,GAAI,CACF;AACA,KAAM,CAAAC,SAAS,CAAGD,SAAS,CAACE,IAAI,CAChC,KAAM,CAAAC,YAAY,CAAG,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC,IAAIrB,MAAM,CAACkB,SAAS,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIvB,MAAM,CAACkB,SAAS,CAACM,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAEtJ,KAAM,CAAAE,SAAS,CAAG,CAChBN,IAAI,CAAEC,YAAY,CAClBM,IAAI,CAAET,SAAS,CAACS,IAAI,CACpBC,MAAM,CAAEV,SAAS,CAACU,MACpB,CAAC,CAED,KAAM,CAAEvC,IAAK,CAAC,CAAG,KAAM,CAAAxE,KAAK,CAACgH,IAAI,CAAC,yBAAyB,CAAEH,SAAS,CAAE,CACtEnC,OAAO,CAAE,CACP,eAAe,CAAE,UAAUlD,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIgD,IAAI,CAACG,OAAO,CAAE,CAChB;AACAa,iBAAiB,CAAC,CAAC,CAEnB;AACAyB,KAAK,CAAC3F,CAAC,CAAC,4BAA4B,CAAE,qBAAqB,CAAC,CAAC,CAC/D,CACF,CAAE,MAAOU,KAAK,CAAE,CACd4C,OAAO,CAAC5C,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CiF,KAAK,CAAC3F,CAAC,CAAC,0BAA0B,CAAE,mBAAmB,CAAC,CAAC,CAC3D,CACF,CAAC,CAED;AACA,KAAM,CAAA4F,iBAAiB,CAAIlC,OAAO,EAAK,CACrC7C,kBAAkB,CAAC6C,OAAO,CAAC,CAC3B3C,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAA8E,wBAAwB,CAAInC,OAAO,EAAK,CAC5C7C,kBAAkB,CAAC6C,OAAO,CAAC,CAC3BvC,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAA2E,iBAAiB,CAAG,KAAO,CAAApC,OAAO,EAAK,CAC3C,GAAI,CACF;AACA,GAAI,CAACA,OAAO,CAACqC,SAAS,CAAE,CACtBzC,OAAO,CAAC5C,KAAK,CAAC,iCAAiC,CAAEgD,OAAO,CAAC,CACzDiC,KAAK,CAAC3F,CAAC,CAAC,sBAAsB,CAAC,EAAI,wBAAwB,CAAC,CAC5D,OACF,CAEA;AACA,GAAI,CAAC0D,OAAO,CAACsC,UAAU,CAAE,CACvB1C,OAAO,CAAC5C,KAAK,CAAC,kCAAkC,CAAEgD,OAAO,CAAC,CAC1DiC,KAAK,CAAC3F,CAAC,CAAC,yBAAyB,CAAC,EAAI,sBAAsB,CAAC,CAC7D,OACF,CAEAsD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAE,CACxCwC,SAAS,CAAErC,OAAO,CAACqC,SAAS,CAC5BC,UAAU,CAAEtC,OAAO,CAACsC,UAAU,CAC9BC,QAAQ,CAAEvC,OAAO,CAACuC,QAAQ,CAC1BpC,QAAQ,CAAEH,OAAO,CAACG,QACpB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAA5F,KAAK,CAACyE,GAAG,CAAC,aAAaO,OAAO,CAACqC,SAAS,WAAW,CAAC,CAC3EtE,iBAAiB,CAAC,CAAE,GAAGiC,OAAO,CAAEqC,SAAS,CAAErC,OAAO,CAACqC,SAAU,CAAC,CAAC,CAC/DxE,cAAc,CAAC,IAAI,CAAC,CACtB,CAAE,MAAOb,KAAK,CAAE,CACd4C,OAAO,CAAC5C,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CiF,KAAK,CAAC3F,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAkG,kBAAkB,CAAGA,CAAA,GAAM,CAC/B3E,cAAc,CAAC,KAAK,CAAC,CACrBE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAA0E,gBAAgB,CAAIzC,OAAO,EAAK,CACpC,MAAO,CAAAA,OAAO,CAAC0C,MAAM,EAAI,WAAW,CACtC,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAI3C,OAAO,EAAK,CAClC,GAAI,CAACA,OAAO,EAAI,CAAC1C,cAAc,CAAE,MAAO,MAAK,CAE7C,KAAM,CAAAsF,aAAa,CAAGH,gBAAgB,CAACzC,OAAO,CAAC,CAC/C,GAAI4C,aAAa,GAAK,WAAW,EAAIA,aAAa,GAAK,WAAW,CAAE,CAClE,MAAO,MAAK,CACd,CAEA,KAAM,CAAAC,gBAAgB,CAAG,GAAI,CAAA3E,IAAI,CAAC8B,OAAO,CAACuC,QAAQ,CAAC,CACnD,KAAM,CAAAO,cAAc,CAAG,GAAI,CAAA5E,IAAI,CAAC8B,OAAO,CAACuC,QAAQ,CAAC,CACjDO,cAAc,CAACC,UAAU,CAACD,cAAc,CAACE,UAAU,CAAC,CAAC,CAAGC,QAAQ,CAACjD,OAAO,CAACG,QAAQ,CAAC,CAAC,CACnF,KAAM,CAAA+C,GAAG,CAAG,GAAI,CAAAhF,IAAI,CAAC,CAAC,CAEtB,MAAO,CAAAgF,GAAG,EAAIL,gBAAgB,EAAIK,GAAG,CAAGJ,cAAc,CACxD,CAAC,CAED;AACA,KAAM,CAAAK,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAACjG,eAAe,CAAE,OAEtB,GAAI,CACFS,oBAAoB,CAAC,IAAI,CAAC,CAC1B,KAAM,CAAE6B,IAAK,CAAC,CAAG,KAAM,CAAAxE,KAAK,CAACoI,GAAG,CAAC,aAAalG,eAAe,CAACmG,EAAE,SAAS,CAAE,CAAC,CAAC,CAAE,CAC7E3D,OAAO,CAAE,CACP,eAAe,CAAE,UAAUlD,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAIgD,IAAI,CAACG,OAAO,CAAE,CAChB;AACA9C,WAAW,CAACyG,YAAY,EACtBA,YAAY,CAACvD,GAAG,CAACC,OAAO,EACtBA,OAAO,CAACqD,EAAE,GAAKnG,eAAe,CAACmG,EAAE,CAC7B,CAAE,GAAGrD,OAAO,CAAE0C,MAAM,CAAE,WAAY,CAAC,CACnC1C,OACN,CACF,CAAC,CACDiC,KAAK,CAAC3F,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACpC,CAAC,IAAM,CACL2F,KAAK,CAACzC,IAAI,CAACiB,OAAO,EAAInE,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAClD,CACF,CAAE,MAAOU,KAAK,CAAE,KAAAuG,gBAAA,CAAAC,qBAAA,CACd5D,OAAO,CAAC5C,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDiF,KAAK,CAAC,EAAAsB,gBAAA,CAAAvG,KAAK,CAAC4D,QAAQ,UAAA2C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB/D,IAAI,UAAAgE,qBAAA,iBAApBA,qBAAA,CAAsB/C,OAAO,GAAInE,CAAC,CAAC,sBAAsB,CAAC,CAAC,CACnE,CAAC,OAAS,CACRqB,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,mBAAmB,CAAC,KAAK,CAAC,CAC1BJ,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAAsG,cAAc,CAAIf,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,OAAO,CAChB,IAAK,gBAAgB,CACnB,MAAO,SAAS,CAClB,IAAK,SAAS,CACZ,MAAO,MAAM,CACf,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAAgB,aAAa,CAAIhB,MAAM,EAAK,CAChC,MAAO,CAAApG,CAAC,CAAC,yBAAyBoG,MAAM,EAAE,CAAE,CAC1CiB,YAAY,CAAEjB,MAAM,CAACkB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGnB,MAAM,CAACoB,KAAK,CAAC,CAAC,CAC/D,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIxB,QAAQ,EAAK,CACtC,GAAI,CAACjF,cAAc,EAAI,CAACA,cAAc,CAACgD,QAAQ,CAAE,CAC/C,MAAO,CAAArF,MAAM,CAAC,GAAI,CAAAiD,IAAI,CAACqE,QAAQ,CAAC,CAAE,KAAK,CAAE,CAAEyB,MAAM,CAAEtH,KAAK,CAAGpB,EAAE,CAAGC,IAAK,CAAC,CAAC,CACzE,CAEA,KAAM,CAAA0I,aAAa,CAAGrI,2BAA2B,CAAC2G,QAAQ,CAAEjF,cAAc,CAACgD,QAAQ,CAAE,YAAY,CAAC,CAClG,MAAO,CAAAvE,MAAM,CAACkI,aAAa,CAAE,YAAY,CAAC,CAAChJ,MAAM,CAAC,cAAc,CAAC,CACnE,CAAC,CAED;AACA,KAAM,CAAAiJ,iBAAiB,CAAI3B,QAAQ,EAAK,CACtC,GAAI,CAACjF,cAAc,EAAI,CAACA,cAAc,CAACgD,QAAQ,CAAE,CAC/C,MAAO,CAAArF,MAAM,CAAC,GAAI,CAAAiD,IAAI,CAACqE,QAAQ,CAAC,CAAE,GAAG,CAAE,CAAEyB,MAAM,CAAEtH,KAAK,CAAGpB,EAAE,CAAGC,IAAK,CAAC,CAAC,CACvE,CAEA,KAAM,CAAA4I,iBAAiB,CAAGvI,2BAA2B,CAAC2G,QAAQ,CAAEjF,cAAc,CAACgD,QAAQ,CAAE,qBAAqB,CAAC,CAC/G,MAAO,CAAAvE,MAAM,CAACoI,iBAAiB,CAAE,qBAAqB,CAAC,CAAClJ,MAAM,CAAC,QAAQ,CAAC,CAC1E,CAAC,CAED;AACA,KAAM,CAAAmJ,oBAAoB,CAAGA,CAACC,cAAc,CAAElE,QAAQ,GAAK,CACzD,KAAM,CAAAmE,WAAW,CAAGrB,QAAQ,CAAC9C,QAAQ,CAAE,EAAE,CAAC,CAC1C,MAAO,CAAAmE,WAAW,GAAK,EAAE,CAAGD,cAAc,CAAGA,cAAc,CAAG,CAAC,CACjE,CAAC,CAED;AACA,KAAM,CAAAE,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI,CAACrH,eAAe,CAAE,MAAO,KAAI,CAEjC,KAAM,CAAAsH,WAAW,CAAGJ,oBAAoB,CAAClH,eAAe,CAAC+C,gBAAgB,CAAE/C,eAAe,CAACiD,QAAQ,CAAC,CAEpG,mBACE/D,KAAA,CAACjD,MAAM,EACLsL,IAAI,CAAErH,iBAAkB,CACxBsH,OAAO,CAAEA,CAAA,GAAMrH,oBAAoB,CAAC,KAAK,CAAE,CAC3CsH,QAAQ,CAAC,IAAI,CACbC,SAAS,MAAAC,QAAA,eAET3I,IAAA,CAAC9C,WAAW,EAAC0L,EAAE,CAAE,CAAEC,OAAO,CAAE,cAAc,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAH,QAAA,cAC3DzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eACzD3I,IAAA,CAACnC,YAAY,GAAE,CAAC,cAChBmC,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,IAAI,CAAAP,QAAA,CACrBvI,CAAC,CAAC,yBAAyB,CAAC,CACnB,CAAC,EACV,CAAC,CACK,CAAC,cACdJ,IAAA,CAAC7C,aAAa,EAACyL,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,cAC3BzI,KAAA,CAAC1C,IAAI,EAAC4L,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAV,QAAA,eACzB3I,IAAA,CAACxC,IAAI,EAAC8L,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAZ,QAAA,cAChBzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxD3I,IAAA,CAAC1C,MAAM,EACLmM,GAAG,CAAEzI,eAAe,CAAC0I,eAAgB,CACrCC,GAAG,CAAE3I,eAAe,CAAC4I,YAAa,CAClChB,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAG,CAAE,CACtC,CAAC,cACF7J,KAAA,CAACrD,GAAG,EAAA8L,QAAA,eACF3I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,IAAI,CAAAP,QAAA,CACrB3H,eAAe,CAAC4I,YAAY,CACnB,CAAC,cACb5J,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CAC/C3H,eAAe,CAACgJ,aAAa,CACpB,CAAC,EACV,CAAC,EACH,CAAC,CACF,CAAC,cAEP9J,KAAA,CAAC1C,IAAI,EAAC8L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACU,EAAE,CAAE,CAAE,CAAAtB,QAAA,eACvBzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxD3I,IAAA,CAACnC,YAAY,EAAC+K,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACtD9I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnDvI,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,EACV,CAAC,cACNJ,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAAAP,QAAA,CACxBd,iBAAiB,CAAC7G,eAAe,CAACqF,QAAQ,CAAC,CAClC,CAAC,EACT,CAAC,cAEPnG,KAAA,CAAC1C,IAAI,EAAC8L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACU,EAAE,CAAE,CAAE,CAAAtB,QAAA,eACvBzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxD3I,IAAA,CAACjC,QAAQ,EAAC6K,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cAClD9I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnDvI,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,EACV,CAAC,cACNJ,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAAAP,QAAA,CACxBX,iBAAiB,CAAChH,eAAe,CAACqF,QAAQ,CAAC,CAClC,CAAC,EACT,CAAC,cAEPnG,KAAA,CAAC1C,IAAI,EAAC8L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACU,EAAE,CAAE,CAAE,CAAAtB,QAAA,eACvBzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxD3I,IAAA,CAACjC,QAAQ,EAAC6K,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cAClD9I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnDvI,CAAC,CAAC,mBAAmB,CAAC,CACb,CAAC,EACV,CAAC,cACNF,KAAA,CAACtD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAAAP,QAAA,EACxB3H,eAAe,CAACiD,QAAQ,CAAC,GAAC,CAAC7D,CAAC,CAAC,kBAAkB,CAAC,EACvC,CAAC,EACT,CAAC,cAEPF,KAAA,CAAC1C,IAAI,EAAC8L,IAAI,MAACC,EAAE,CAAE,EAAG,CAACU,EAAE,CAAE,CAAE,CAAAtB,QAAA,eACvBzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxD3I,IAAA,CAAC7B,SAAS,EAACyK,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACnD9I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnDvI,CAAC,CAAC,gBAAgB,CAAC,CACV,CAAC,EACV,CAAC,cACNF,KAAA,CAACtD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAAAP,QAAA,EAAC,GACzB,CAACL,WAAW,CAAC4B,OAAO,CAAC,CAAC,CAAC,EACd,CAAC,EACT,CAAC,cAEPhK,KAAA,CAAC1C,IAAI,EAAC8L,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAZ,QAAA,eAChBzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxD3I,IAAA,CAAC/B,UAAU,EAAC2K,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACpD9I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnDvI,CAAC,CAAC,iBAAiB,CAAC,CACX,CAAC,EACV,CAAC,cACNJ,IAAA,CAACzC,IAAI,EACH4M,KAAK,CAAE3C,aAAa,CAACxG,eAAe,CAACwF,MAAM,CAAE,CAC7CsC,KAAK,CAAEvB,cAAc,CAACvG,eAAe,CAACwF,MAAM,CAAE,CAC9C0C,OAAO,CAAC,QAAQ,CACjB,CAAC,EACE,CAAC,EACH,CAAC,CACM,CAAC,cAChBhJ,KAAA,CAAC9C,aAAa,EAAAuL,QAAA,eACZ3I,IAAA,CAAC3C,MAAM,EAAC+M,OAAO,CAAEA,CAAA,GAAMjJ,oBAAoB,CAAC,KAAK,CAAE,CAAAwH,QAAA,CAChDvI,CAAC,CAAC,cAAc,CAAC,CACZ,CAAC,CAGRY,eAAe,eACdhB,IAAA,CAAC3C,MAAM,EACL+M,OAAO,CAAEA,CAAA,GAAM3D,cAAc,CAACzF,eAAe,CAAC,EAAIkF,iBAAiB,CAAClF,eAAe,CAAE,CACrF8H,KAAK,CAAErC,cAAc,CAACzF,eAAe,CAAC,CAAG,SAAS,CAAG,SAAU,CAC/DkI,OAAO,CAAEzC,cAAc,CAACzF,eAAe,CAAC,CAAG,WAAW,CAAG,UAAW,CACpEqJ,SAAS,cAAErK,IAAA,CAACvB,aAAa,GAAE,CAAE,CAC7B6L,QAAQ,CAAE,CAAC7D,cAAc,CAACzF,eAAe,CAAE,CAC3C4H,EAAE,CAAE,CACFiB,EAAE,CAAE,CAAC,CACL,IAAIpD,cAAc,CAACzF,eAAe,CAAC,CAAG,CAAC,CAAC,CAAG,CACzC8H,KAAK,CAAEvI,KAAK,CAACgK,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,CAC9BC,WAAW,CAAElK,KAAK,CAACgK,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,CACpCE,eAAe,CAAEnK,KAAK,CAACgK,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,CACxC,SAAS,CAAE,CACTE,eAAe,CAAEnK,KAAK,CAACgK,OAAO,CAACC,IAAI,CAAC,GAAG,CACzC,CACF,CAAC,CACH,CAAE,CAAA7B,QAAA,CAEDlC,cAAc,CAACzF,eAAe,CAAC,CAAGZ,CAAC,CAAC,eAAe,CAAC,CAAGA,CAAC,CAAC,qBAAqB,CAAC,CAC1E,CACT,CAEA,CAAAY,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwF,MAAM,IAAK,WAAW,eACtCxG,IAAA,CAAC3C,MAAM,EACL+M,OAAO,CAAEA,CAAA,GAAM,CACbjJ,oBAAoB,CAAC,KAAK,CAAC,CAC3BI,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CACFuH,KAAK,CAAC,OAAO,CACbI,OAAO,CAAC,WAAW,CACnBmB,SAAS,cAAErK,IAAA,CAACrB,UAAU,GAAE,CAAE,CAAAgK,QAAA,CAEzBvI,CAAC,CAAC,iBAAiB,CAAC,CACf,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAAC,CAED;AACA,KAAM,CAAAuK,kBAAkB,CAAGA,CAAA,gBACzBzK,KAAA,CAACjD,MAAM,EAACsL,IAAI,CAAEjH,gBAAiB,CAACkH,OAAO,CAAEA,CAAA,GAAMjH,mBAAmB,CAAC,KAAK,CAAE,CAAAoH,QAAA,eACxEzI,KAAA,CAAChD,WAAW,EAAAyL,QAAA,EACTvI,CAAC,CAAC,wBAAwB,CAAC,cAC5BJ,IAAA,CAACtC,UAAU,EACT,aAAW,OAAO,CAClB0M,OAAO,CAAEA,CAAA,GAAM7I,mBAAmB,CAAC,KAAK,CAAE,CAC1CqH,EAAE,CAAE,CAAEgC,QAAQ,CAAE,UAAU,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAnC,QAAA,cAE/C3I,IAAA,CAACnB,SAAS,GAAE,CAAC,CACH,CAAC,EACF,CAAC,cACdqB,KAAA,CAAC/C,aAAa,EAAAwL,QAAA,eACZ3I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAAAP,QAAA,CACxBvI,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,CACZY,eAAe,eACdd,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACjBzI,KAAA,CAACtD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAAC6B,YAAY,MAAApC,QAAA,eACtCzI,KAAA,WAAAyI,QAAA,EAASvI,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACY,eAAe,CAAC4I,YAAY,EAC7D,CAAC,cACb1J,KAAA,CAACtD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAAC6B,YAAY,MAAApC,QAAA,eACtCzI,KAAA,WAAAyI,QAAA,EAASvI,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACyH,iBAAiB,CAAC7G,eAAe,CAACqF,QAAQ,CAAC,EACzE,CAAC,cACbnG,KAAA,CAACtD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAAAP,QAAA,eACzBzI,KAAA,WAAAyI,QAAA,EAASvI,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC4H,iBAAiB,CAAChH,eAAe,CAACqF,QAAQ,CAAC,EACzE,CAAC,EACV,CACN,EACY,CAAC,cAChBnG,KAAA,CAAC9C,aAAa,EAAAuL,QAAA,eACZ3I,IAAA,CAAC3C,MAAM,EAAC+M,OAAO,CAAEA,CAAA,GAAM7I,mBAAmB,CAAC,KAAK,CAAE,CAAAoH,QAAA,CAC/CvI,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTJ,IAAA,CAAC3C,MAAM,EACL+M,OAAO,CAAEnD,mBAAoB,CAC7B6B,KAAK,CAAC,OAAO,CACbI,OAAO,CAAC,WAAW,CACnBoB,QAAQ,CAAE9I,iBAAkB,CAAAmH,QAAA,CAE3BnH,iBAAiB,CAAGpB,CAAC,CAAC,qBAAqB,CAAC,CAAGA,CAAC,CAAC,8BAA8B,CAAC,CAC3E,CAAC,EACI,CAAC,EACV,CACT,CAED,mBACEF,KAAA,CAACX,MAAM,EAAAoJ,QAAA,eACLzI,KAAA,CAACvD,SAAS,EAAC8L,QAAQ,CAAC,IAAI,CAACG,EAAE,CAAE,CAAEoC,EAAE,CAAE,CAAE,CAAE,CAAArC,QAAA,eACrC3I,IAAA,CAAClD,KAAK,EAACmO,SAAS,CAAE,CAAE,CAACrC,EAAE,CAAE,CAAEsC,CAAC,CAAE,CAAC,CAAE1B,EAAE,CAAE,CAAC,CAAEX,OAAO,CAAE,cAAc,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAH,QAAA,cAChFzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEoC,cAAc,CAAE,eAAe,CAAEnC,UAAU,CAAE,QAAQ,CAAEoC,QAAQ,CAAE,MAAM,CAAEnC,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eAC5GzI,KAAA,CAACrD,GAAG,EAAA8L,QAAA,eACF3I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,IAAI,CAAC6B,YAAY,MAACnC,EAAE,CAAE,CAAEyC,UAAU,CAAE,MAAO,CAAE,CAAA1C,QAAA,CAC9DvI,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cACbJ,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAACN,EAAE,CAAE,CAAE0C,OAAO,CAAE,GAAI,CAAE,CAAA3C,QAAA,CAC9CvI,CAAC,CAAC,mCAAmC,CAAC,CAC7B,CAAC,EACV,CAAC,cACNF,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eACzDzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAE2C,SAAS,CAAE,OAAQ,CAAE,CAAA5C,QAAA,eAC9B3I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAACN,EAAE,CAAE,CAAE0C,OAAO,CAAE,GAAG,CAAE9B,EAAE,CAAE,GAAI,CAAE,CAAAb,QAAA,CACvDvI,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cACbF,KAAA,CAACtD,UAAU,EAACsM,OAAO,CAAC,IAAI,CAACN,EAAE,CAAE,CAAEyC,UAAU,CAAE,MAAO,CAAE,CAAA1C,QAAA,EAAC,eAChD,CAAC5J,MAAM,CAACsD,gBAAgB,CAAE,OAAO,CAAE,CAAEyF,MAAM,CAAEtH,KAAK,CAAGpB,EAAE,CAAGC,IAAK,CAAC,CAAC,CAAC,KAAG,CAACN,MAAM,CAACE,OAAO,CAACoD,gBAAgB,CAAE,CAAC,CAAC,CAAE,aAAa,CAAE,CAAEyF,MAAM,CAAEtH,KAAK,CAAGpB,EAAE,CAAGC,IAAK,CAAC,CAAC,EACjJ,CAAC,EACV,CAAC,cACNa,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eACnC3I,IAAA,CAACrC,OAAO,EAAC6N,KAAK,CAAEpL,CAAC,CAAC,sBAAsB,CAAE,CAAAuI,QAAA,cACxC3I,IAAA,SAAA2I,QAAA,cACE3I,IAAA,CAACtC,UAAU,EACT0M,OAAO,CAAE1H,gBAAiB,CAC1B4H,QAAQ,CAAEtH,sBAAsB,CAAC,CAAE,CACnC4F,EAAE,CAAE,CACFE,KAAK,CAAE,OAAO,CACdD,OAAO,CAAE,0BAA0B,CACnC,SAAS,CAAE,CACTA,OAAO,CAAE,0BACX,CAAC,CACD,YAAY,CAAE,CACZC,KAAK,CAAE,0BAA0B,CACjCD,OAAO,CAAE,2BACX,CACF,CAAE,CAAAF,QAAA,cAEF3I,IAAA,CAAC3B,eAAe,GAAE,CAAC,CACT,CAAC,CACT,CAAC,CACA,CAAC,cACV2B,IAAA,CAACrC,OAAO,EAAC6N,KAAK,CAAEpL,CAAC,CAAC,kBAAkB,CAAE,CAAAuI,QAAA,cACpC3I,IAAA,SAAA2I,QAAA,cACE3I,IAAA,CAACtC,UAAU,EACT0M,OAAO,CAAExH,YAAa,CACtB0H,QAAQ,CAAErH,kBAAkB,CAAC,CAAE,CAC/B2F,EAAE,CAAE,CACFE,KAAK,CAAE,OAAO,CACdD,OAAO,CAAE,0BAA0B,CACnC,SAAS,CAAE,CACTA,OAAO,CAAE,0BACX,CAAC,CACD,YAAY,CAAE,CACZC,KAAK,CAAE,0BAA0B,CACjCD,OAAO,CAAE,2BACX,CACF,CAAE,CAAAF,QAAA,cAEF3I,IAAA,CAACzB,gBAAgB,GAAE,CAAC,CACV,CAAC,CACT,CAAC,CACA,CAAC,EACP,CAAC,EACH,CAAC,EACH,CAAC,CACD,CAAC,cAGRyB,IAAA,CAAClD,KAAK,EAACmO,SAAS,CAAE,CAAE,CAACrC,EAAE,CAAE,CAAEsC,CAAC,CAAE,CAAC,CAAE1B,EAAE,CAAE,CAAC,CAAEX,OAAO,CAAE,kBAAmB,CAAE,CAAAF,QAAA,cACpEzI,KAAA,CAACrD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eACzD3I,IAAA,CAACnD,GAAG,EAAC+L,EAAE,CAAE,CACPC,OAAO,CAAE,cAAc,CACvBC,KAAK,CAAE,OAAO,CACdoC,CAAC,CAAE,CAAC,CACJO,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAE,EAAE,CACZH,SAAS,CAAE,QACb,CAAE,CAAA5C,QAAA,CAAC,cAEH,CAAK,CAAC,cACNzI,KAAA,CAACrD,GAAG,EAAA8L,QAAA,eACF3I,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAACF,EAAE,CAAE,CAAEY,EAAE,CAAE,GAAI,CAAE,CAAAb,QAAA,CAChEvI,CAAC,CAAC,sBAAsB,CAAC,CAChB,CAAC,cACbJ,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,IAAI,CAACN,EAAE,CAAE,CAAEyC,UAAU,CAAE,MAAO,CAAE,CAAA1C,QAAA,CACjDvH,cAAc,SAAdA,cAAc,WAAdA,cAAc,CAAEgD,QAAQ,CACvBvE,MAAM,CAAC,CAAC,CAAC8L,GAAG,CAAC,CAAC,CAACC,GAAG,CAAChM,mBAAmB,CAACwB,cAAc,CAACgD,QAAQ,CAAC,CAAE,SAAS,CAAC,CAACrF,MAAM,CAAC,gCAAgC,CAAC,CAEpHA,MAAM,CAAC,GAAI,CAAAiD,IAAI,CAAC,CAAC,CAAE,MAAM,CAAE,CACzB8F,MAAM,CAAEtH,KAAK,CAAGpB,EAAE,CAAGC,IACvB,CAAC,CACF,CACS,CAAC,CACZ,CAAA+B,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEgD,QAAQ,gBACvBpE,IAAA,CAACpD,UAAU,EAACsM,OAAO,CAAC,SAAS,CAACN,EAAE,CAAE,CAAE0C,OAAO,CAAE,GAAI,CAAE,CAAA3C,QAAA,CAChDvH,cAAc,CAACgD,QAAQ,CACd,CACb,EACE,CAAC,EACH,CAAC,CACD,CAAC,CAEPxD,OAAO,cACNZ,IAAA,CAACnD,GAAG,EAAC+L,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEoC,cAAc,CAAE,QAAQ,CAAEH,EAAE,CAAE,CAAE,CAAE,CAAArC,QAAA,cAC5D3I,IAAA,CAACjD,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACJ+D,KAAK,cACPd,IAAA,CAAChD,KAAK,EAAC6O,QAAQ,CAAC,OAAO,CAACjD,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACnC7H,KAAK,CACD,CAAC,cAERd,IAAA,CAACR,mBAAmB,EAClBkB,QAAQ,CAAEA,QAAS,CACnBE,OAAO,CAAEA,OAAQ,CACjByB,gBAAgB,CAAEA,gBAAiB,CACnCI,UAAU,CAAEA,UAAW,CACvBqJ,aAAa,CAAE9F,iBAAkB,CACjC+F,eAAe,CAAE9F,wBAA0B;AAAA,CAC3C+F,cAAc,CAAE5K,cAAgB;AAAA,CAChC4G,iBAAiB,CAAEA,iBAAkB,CACrCT,cAAc,CAAEA,cAAe,CAC/BC,aAAa,CAAEA,aAAc,CAC7ByE,aAAa,CAAE,IAAM;AAAA,CACrBhK,cAAc,CAAEA,cAAgB;AAAA,CAChCiK,WAAW,CAAEhH,eAAiB;AAAA,CAC9B/C,YAAY,CAAEA,YAAc;AAAA,CAC7B,CACF,CAEAkG,mBAAmB,CAAC,CAAC,CACrBsC,kBAAkB,CAAC,CAAC,EACZ,CAAC,cAGZ3K,IAAA,CAAC/C,MAAM,EACLkP,UAAU,MACV5D,IAAI,CAAE7G,WAAY,CAClB8G,OAAO,CAAElC,kBAAmB,CAAAqC,QAAA,CAE3B/G,cAAc,eACb5B,IAAA,CAACF,eAAe,EACdsM,MAAM,CAAExK,cAAc,CAACuE,SAAU,CACjCkG,SAAS,CAAEzK,cAAc,CAACwE,UAAW,CACrCkG,WAAW,CAAE1K,cAAe,CAC5B4G,OAAO,CAAElC,kBAAmB,CAC7B,CACF,CACK,CAAC,EACH,CAAC,CAEb,CAAC,CAED,cAAe,CAAAnG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}