{"ast": null, "code": "import { useState as $fuDHA$useState, useEffect as $fuDHA$useEffect } from \"react\";\nimport { useIsSSR as $fuDHA$useIsSSR } from \"@react-aria/ssr\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet $5df64b3807dc15ee$var$visualViewport = typeof document !== 'undefined' && window.visualViewport;\nfunction $5df64b3807dc15ee$export$d699905dd57c73ca() {\n  let isSSR = (0, $fuDHA$useIsSSR)();\n  let [size, setSize] = (0, $fuDHA$useState)(() => isSSR ? {\n    width: 0,\n    height: 0\n  } : $5df64b3807dc15ee$var$getViewportSize());\n  (0, $fuDHA$useEffect)(() => {\n    // Use visualViewport api to track available height even on iOS virtual keyboard opening\n    let onResize = () => {\n      setSize(size => {\n        let newSize = $5df64b3807dc15ee$var$getViewportSize();\n        if (newSize.width === size.width && newSize.height === size.height) return size;\n        return newSize;\n      });\n    };\n    if (!$5df64b3807dc15ee$var$visualViewport) window.addEventListener('resize', onResize);else $5df64b3807dc15ee$var$visualViewport.addEventListener('resize', onResize);\n    return () => {\n      if (!$5df64b3807dc15ee$var$visualViewport) window.removeEventListener('resize', onResize);else $5df64b3807dc15ee$var$visualViewport.removeEventListener('resize', onResize);\n    };\n  }, []);\n  return size;\n}\nfunction $5df64b3807dc15ee$var$getViewportSize() {\n  return {\n    width: $5df64b3807dc15ee$var$visualViewport && ($5df64b3807dc15ee$var$visualViewport === null || $5df64b3807dc15ee$var$visualViewport === void 0 ? void 0 : $5df64b3807dc15ee$var$visualViewport.width) || window.innerWidth,\n    height: $5df64b3807dc15ee$var$visualViewport && ($5df64b3807dc15ee$var$visualViewport === null || $5df64b3807dc15ee$var$visualViewport === void 0 ? void 0 : $5df64b3807dc15ee$var$visualViewport.height) || window.innerHeight\n  };\n}\nexport { $5df64b3807dc15ee$export$d699905dd57c73ca as useViewportSize };", "map": {"version": 3, "names": ["$5df64b3807dc15ee$var$visualViewport", "document", "window", "visualViewport", "$5df64b3807dc15ee$export$d699905dd57c73ca", "isSSR", "$fuDHA$useIsSSR", "size", "setSize", "$fuDHA$useState", "width", "height", "$5df64b3807dc15ee$var$getViewportSize", "$fuDHA$useEffect", "onResize", "newSize", "addEventListener", "removeEventListener", "innerWidth", "innerHeight"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useViewportSize.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\ninterface ViewportSize {\n  width: number,\n  height: number\n}\n\nlet visualViewport = typeof document !== 'undefined' && window.visualViewport;\n\nexport function useViewportSize(): ViewportSize {\n  let isSSR = useIsSSR();\n  let [size, setSize] = useState(() => isSSR ? {width: 0, height: 0} : getViewportSize());\n\n  useEffect(() => {\n    // Use visualViewport api to track available height even on iOS virtual keyboard opening\n    let onResize = () => {\n      setSize(size => {\n        let newSize = getViewportSize();\n        if (newSize.width === size.width && newSize.height === size.height) {\n          return size;\n        }\n        return newSize;\n      });\n    };\n\n    if (!visualViewport) {\n      window.addEventListener('resize', onResize);\n    } else {\n      visualViewport.addEventListener('resize', onResize);\n    }\n\n    return () => {\n      if (!visualViewport) {\n        window.removeEventListener('resize', onResize);\n      } else {\n        visualViewport.removeEventListener('resize', onResize);\n      }\n    };\n  }, []);\n\n  return size;\n}\n\nfunction getViewportSize(): ViewportSize {\n  return {\n    width: (visualViewport && visualViewport?.width) || window.innerWidth,\n    height: (visualViewport && visualViewport?.height) || window.innerHeight\n  };\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAoBA,IAAIA,oCAAA,GAAiB,OAAOC,QAAA,KAAa,eAAeC,MAAA,CAAOC,cAAc;AAEtE,SAASC,0CAAA;EACd,IAAIC,KAAA,GAAQ,IAAAC,eAAO;EACnB,IAAI,CAACC,IAAA,EAAMC,OAAA,CAAQ,GAAG,IAAAC,eAAO,EAAE,MAAMJ,KAAA,GAAQ;IAACK,KAAA,EAAO;IAAGC,MAAA,EAAQ;EAAC,IAAIC,qCAAA;EAErE,IAAAC,gBAAQ,EAAE;IACR;IACA,IAAIC,QAAA,GAAWA,CAAA;MACbN,OAAA,CAAQD,IAAA;QACN,IAAIQ,OAAA,GAAUH,qCAAA;QACd,IAAIG,OAAA,CAAQL,KAAK,KAAKH,IAAA,CAAKG,KAAK,IAAIK,OAAA,CAAQJ,MAAM,KAAKJ,IAAA,CAAKI,MAAM,EAChE,OAAOJ,IAAA;QAET,OAAOQ,OAAA;MACT;IACF;IAEA,IAAI,CAACf,oCAAA,EACHE,MAAA,CAAOc,gBAAgB,CAAC,UAAUF,QAAA,OAElCd,oCAAA,CAAegB,gBAAgB,CAAC,UAAUF,QAAA;IAG5C,OAAO;MACL,IAAI,CAACd,oCAAA,EACHE,MAAA,CAAOe,mBAAmB,CAAC,UAAUH,QAAA,OAErCd,oCAAA,CAAeiB,mBAAmB,CAAC,UAAUH,QAAA;IAEjD;EACF,GAAG,EAAE;EAEL,OAAOP,IAAA;AACT;AAEA,SAASK,sCAAA;EACP,OAAO;IACLF,KAAA,EAAOV,oCAAC,KAAkBA,oCAAA,aAAAA,oCAAA,uBAAAA,oCAAA,CAAgBU,KAAK,KAAKR,MAAA,CAAOgB,UAAU;IACrEP,MAAA,EAAQX,oCAAC,KAAkBA,oCAAA,aAAAA,oCAAA,uBAAAA,oCAAA,CAAgBW,MAAM,KAAKT,MAAA,CAAOiB;EAC/D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}