[{"D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js": "1", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js": "2", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js": "3", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js": "4", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js": "5", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js": "6", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js": "7", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js": "8", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js": "9", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js": "10", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js": "11", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js": "12", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js": "13", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js": "14", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js": "15", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js": "16", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js": "17", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js": "18", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js": "19", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js": "20", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js": "21", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js": "22", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js": "23", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js": "24", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js": "25", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js": "26", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js": "27", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js": "28", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js": "29", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js": "30", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js": "31", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js": "32", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js": "33", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js": "34", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js": "35", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js": "36", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js": "37", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js": "38", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js": "39", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js": "40", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js": "41", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js": "42", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js": "43", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js": "44", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js": "45", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js": "46", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js": "47", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js": "48", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js": "49", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js": "50", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js": "51", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js": "52", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js": "53", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js": "54", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js": "55", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js": "56", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js": "57", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js": "58", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js": "59", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js": "60", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js": "61", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js": "62", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js": "63", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js": "64", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js": "65", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js": "66", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js": "67", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js": "68", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js": "69", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js": "70", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js": "71", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js": "72", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js": "73", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js": "74", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js": "75", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js": "76", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js": "77", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js": "78", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js": "79", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js": "80", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js": "81", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js": "82", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js": "83", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js": "84", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js": "85", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js": "86", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js": "87", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js": "88", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js": "89", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js": "90", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js": "91", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js": "92", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js": "93", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js": "94", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js": "95", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js": "96", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js": "97", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js": "98", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js": "99", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js": "100", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js": "101", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js": "102", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js": "103", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js": "104", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js": "105", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js": "106", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js": "107", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js": "108", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js": "109", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js": "110", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js": "111", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js": "112", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js": "113", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx": "114", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx": "115", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js": "116", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js": "117", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js": "118", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js": "119", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js": "120", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js": "121", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js": "122", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js": "123", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js": "124", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js": "125", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js": "126", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js": "127", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js": "128", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx": "129", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx": "130", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx": "131", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx": "132", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx": "133", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx": "134", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx": "135", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx": "136", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx": "137", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx": "138", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx": "139", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx": "140", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js": "141", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js": "142", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js": "143", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js": "144", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js": "145", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js": "146", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js": "147", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js": "148", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js": "149", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js": "150", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js": "151", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js": "152", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js": "153", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js": "154"}, {"size": 641, "mtime": 1742576862818, "results": "155", "hashOfConfig": "156"}, {"size": 23869, "mtime": 1752698169985, "results": "157", "hashOfConfig": "156"}, {"size": 1456, "mtime": 1746732811228, "results": "158", "hashOfConfig": "156"}, {"size": 214602, "mtime": 1753029780857, "results": "159", "hashOfConfig": "156"}, {"size": 362, "mtime": 1742419325522, "results": "160", "hashOfConfig": "156"}, {"size": 1029, "mtime": 1744808128594, "results": "161", "hashOfConfig": "156"}, {"size": 325, "mtime": 1743221258725, "results": "162", "hashOfConfig": "156"}, {"size": 8026, "mtime": 1749482015181, "results": "163", "hashOfConfig": "156"}, {"size": 7654, "mtime": 1751398254796, "results": "164", "hashOfConfig": "156"}, {"size": 3432, "mtime": 1751285065551, "results": "165", "hashOfConfig": "156"}, {"size": 3326, "mtime": 1751040721930, "results": "166", "hashOfConfig": "156"}, {"size": 943, "mtime": 1749309630963, "results": "167", "hashOfConfig": "156"}, {"size": 24114, "mtime": 1751284599336, "results": "168", "hashOfConfig": "156"}, {"size": 36616, "mtime": 1753024457732, "results": "169", "hashOfConfig": "156"}, {"size": 4768, "mtime": 1751278844207, "results": "170", "hashOfConfig": "156"}, {"size": 10446, "mtime": 1751293932223, "results": "171", "hashOfConfig": "156"}, {"size": 22367, "mtime": 1749628749611, "results": "172", "hashOfConfig": "156"}, {"size": 30616, "mtime": 1751485922771, "results": "173", "hashOfConfig": "156"}, {"size": 15736, "mtime": 1751486831358, "results": "174", "hashOfConfig": "156"}, {"size": 15894, "mtime": 1749479492637, "results": "175", "hashOfConfig": "156"}, {"size": 15959, "mtime": 1749479507046, "results": "176", "hashOfConfig": "156"}, {"size": 12132, "mtime": 1751303763974, "results": "177", "hashOfConfig": "156"}, {"size": 7902, "mtime": 1747334952040, "results": "178", "hashOfConfig": "156"}, {"size": 10135, "mtime": 1747335101462, "results": "179", "hashOfConfig": "156"}, {"size": 7229, "mtime": 1747334912778, "results": "180", "hashOfConfig": "156"}, {"size": 9247, "mtime": 1742785011813, "results": "181", "hashOfConfig": "156"}, {"size": 13648, "mtime": 1751303763967, "results": "182", "hashOfConfig": "156"}, {"size": 29493, "mtime": 1744817248860, "results": "183", "hashOfConfig": "156"}, {"size": 13701, "mtime": 1744022069977, "results": "184", "hashOfConfig": "156"}, {"size": 12085, "mtime": 1746733346280, "results": "185", "hashOfConfig": "156"}, {"size": 11112, "mtime": 1744799843410, "results": "186", "hashOfConfig": "156"}, {"size": 30331, "mtime": 1751480317262, "results": "187", "hashOfConfig": "156"}, {"size": 6641, "mtime": 1742964048040, "results": "188", "hashOfConfig": "156"}, {"size": 6618, "mtime": 1744022276817, "results": "189", "hashOfConfig": "156"}, {"size": 22625, "mtime": 1751041436226, "results": "190", "hashOfConfig": "156"}, {"size": 11838, "mtime": 1749581734964, "results": "191", "hashOfConfig": "156"}, {"size": 7554, "mtime": 1744093753745, "results": "192", "hashOfConfig": "156"}, {"size": 19961, "mtime": 1746026434563, "results": "193", "hashOfConfig": "156"}, {"size": 10379, "mtime": 1749855699542, "results": "194", "hashOfConfig": "156"}, {"size": 7520, "mtime": 1742769302670, "results": "195", "hashOfConfig": "156"}, {"size": 12485, "mtime": 1749828882192, "results": "196", "hashOfConfig": "156"}, {"size": 12535, "mtime": 1749506609302, "results": "197", "hashOfConfig": "156"}, {"size": 24173, "mtime": 1750508377674, "results": "198", "hashOfConfig": "156"}, {"size": 6673, "mtime": 1749331131513, "results": "199", "hashOfConfig": "156"}, {"size": 35597, "mtime": 1749498641458, "results": "200", "hashOfConfig": "156"}, {"size": 32081, "mtime": 1749321424781, "results": "201", "hashOfConfig": "156"}, {"size": 10975, "mtime": 1749337833332, "results": "202", "hashOfConfig": "156"}, {"size": 33527, "mtime": 1749322312984, "results": "203", "hashOfConfig": "156"}, {"size": 15465, "mtime": 1750848677426, "results": "204", "hashOfConfig": "156"}, {"size": 25465, "mtime": 1753137739320, "results": "205", "hashOfConfig": "156"}, {"size": 15303, "mtime": 1750870211486, "results": "206", "hashOfConfig": "156"}, {"size": 48702, "mtime": 1751484405507, "results": "207", "hashOfConfig": "156"}, {"size": 12480, "mtime": 1752698192448, "results": "208", "hashOfConfig": "156"}, {"size": 8011, "mtime": 1744093709651, "results": "209", "hashOfConfig": "156"}, {"size": 4974, "mtime": 1744028374118, "results": "210", "hashOfConfig": "156"}, {"size": 17010, "mtime": 1749916559907, "results": "211", "hashOfConfig": "156"}, {"size": 10864, "mtime": 1746026292144, "results": "212", "hashOfConfig": "156"}, {"size": 21665, "mtime": 1750041191553, "results": "213", "hashOfConfig": "156"}, {"size": 25751, "mtime": 1749491932637, "results": "214", "hashOfConfig": "156"}, {"size": 4214, "mtime": 1742913454633, "results": "215", "hashOfConfig": "156"}, {"size": 8524, "mtime": 1749491950143, "results": "216", "hashOfConfig": "156"}, {"size": 34718, "mtime": 1751485938458, "results": "217", "hashOfConfig": "156"}, {"size": 32352, "mtime": 1753138555976, "results": "218", "hashOfConfig": "156"}, {"size": 5814, "mtime": 1744024131309, "results": "219", "hashOfConfig": "156"}, {"size": 15814, "mtime": 1749589859006, "results": "220", "hashOfConfig": "156"}, {"size": 5024, "mtime": 1746026315740, "results": "221", "hashOfConfig": "156"}, {"size": 11083, "mtime": 1746026269030, "results": "222", "hashOfConfig": "156"}, {"size": 2721, "mtime": 1750156638150, "results": "223", "hashOfConfig": "156"}, {"size": 7189, "mtime": 1750568564445, "results": "224", "hashOfConfig": "156"}, {"size": 13724, "mtime": 1751567205888, "results": "225", "hashOfConfig": "156"}, {"size": 14721, "mtime": 1749819465295, "results": "226", "hashOfConfig": "156"}, {"size": 37517, "mtime": 1753033568413, "results": "227", "hashOfConfig": "156"}, {"size": 34394, "mtime": 1752956873141, "results": "228", "hashOfConfig": "156"}, {"size": 57051, "mtime": 1746744695128, "results": "229", "hashOfConfig": "156"}, {"size": 14257, "mtime": 1749585929387, "results": "230", "hashOfConfig": "156"}, {"size": 24555, "mtime": 1752956536040, "results": "231", "hashOfConfig": "156"}, {"size": 3262, "mtime": 1753034610621, "results": "232", "hashOfConfig": "156"}, {"size": 7726, "mtime": 1753129936379, "results": "233", "hashOfConfig": "156"}, {"size": 48719, "mtime": 1753137707882, "results": "234", "hashOfConfig": "156"}, {"size": 5549, "mtime": 1749490160444, "results": "235", "hashOfConfig": "156"}, {"size": 25593, "mtime": 1751470234559, "results": "236", "hashOfConfig": "156"}, {"size": 3697, "mtime": 1749774472728, "results": "237", "hashOfConfig": "156"}, {"size": 13801, "mtime": 1743218310347, "results": "238", "hashOfConfig": "156"}, {"size": 7971, "mtime": 1746023858919, "results": "239", "hashOfConfig": "156"}, {"size": 2877, "mtime": 1747400985303, "results": "240", "hashOfConfig": "156"}, {"size": 2530, "mtime": 1750507091156, "results": "241", "hashOfConfig": "156"}, {"size": 1488, "mtime": 1742858201413, "results": "242", "hashOfConfig": "156"}, {"size": 40979, "mtime": 1751474409941, "results": "243", "hashOfConfig": "156"}, {"size": 153, "mtime": 1742445554097, "results": "244", "hashOfConfig": "156"}, {"size": 7365, "mtime": 1750226849238, "results": "245", "hashOfConfig": "156"}, {"size": 3181, "mtime": 1744798236742, "results": "246", "hashOfConfig": "156"}, {"size": 2614, "mtime": 1750185563183, "results": "247", "hashOfConfig": "156"}, {"size": 3068, "mtime": 1751036509706, "results": "248", "hashOfConfig": "156"}, {"size": 11091, "mtime": 1752283251033, "results": "249", "hashOfConfig": "156"}, {"size": 20466, "mtime": 1750170525751, "results": "250", "hashOfConfig": "156"}, {"size": 2219, "mtime": 1750163211180, "results": "251", "hashOfConfig": "156"}, {"size": 836, "mtime": 1750184890252, "results": "252", "hashOfConfig": "156"}, {"size": 4725, "mtime": 1750162079006, "results": "253", "hashOfConfig": "156"}, {"size": 11306, "mtime": 1750569740310, "results": "254", "hashOfConfig": "156"}, {"size": 18054, "mtime": 1750163343664, "results": "255", "hashOfConfig": "156"}, {"size": 2950, "mtime": 1750162078997, "results": "256", "hashOfConfig": "156"}, {"size": 938, "mtime": 1750162079095, "results": "257", "hashOfConfig": "156"}, {"size": 211, "mtime": 1750162079017, "results": "258", "hashOfConfig": "156"}, {"size": 6402, "mtime": 1751036509336, "results": "259", "hashOfConfig": "156"}, {"size": 225, "mtime": 1750162079019, "results": "260", "hashOfConfig": "156"}, {"size": 4612, "mtime": 1750486746007, "results": "261", "hashOfConfig": "156"}, {"size": 831, "mtime": 1750162079019, "results": "262", "hashOfConfig": "156"}, {"size": 4793, "mtime": 1750163289799, "results": "263", "hashOfConfig": "156"}, {"size": 12075, "mtime": 1750163272446, "results": "264", "hashOfConfig": "156"}, {"size": 6897, "mtime": 1750163306463, "results": "265", "hashOfConfig": "156"}, {"size": 3348, "mtime": 1750162079002, "results": "266", "hashOfConfig": "156"}, {"size": 1056, "mtime": 1750162079048, "results": "267", "hashOfConfig": "156"}, {"size": 1842, "mtime": 1750162079037, "results": "268", "hashOfConfig": "156"}, {"size": 3656, "mtime": 1750162079039, "results": "269", "hashOfConfig": "156"}, {"size": 4224, "mtime": 1750162079030, "results": "270", "hashOfConfig": "156"}, {"size": 1616, "mtime": 1750162079025, "results": "271", "hashOfConfig": "156"}, {"size": 937, "mtime": 1750162079029, "results": "272", "hashOfConfig": "156"}, {"size": 1738, "mtime": 1750162079038, "results": "273", "hashOfConfig": "156"}, {"size": 662, "mtime": 1750162079047, "results": "274", "hashOfConfig": "156"}, {"size": 550, "mtime": 1750162079020, "results": "275", "hashOfConfig": "156"}, {"size": 519, "mtime": 1750162079047, "results": "276", "hashOfConfig": "156"}, {"size": 1921, "mtime": 1750265054387, "results": "277", "hashOfConfig": "156"}, {"size": 577, "mtime": 1750162079039, "results": "278", "hashOfConfig": "156"}, {"size": 1773, "mtime": 1750162079025, "results": "279", "hashOfConfig": "156"}, {"size": 503, "mtime": 1750523557957, "results": "280", "hashOfConfig": "156"}, {"size": 6750, "mtime": 1750251645533, "results": "281", "hashOfConfig": "156"}, {"size": 4365, "mtime": 1750434036699, "results": "282", "hashOfConfig": "156"}, {"size": 3765, "mtime": 1750163361559, "results": "283", "hashOfConfig": "156"}, {"size": 615, "mtime": 1750162079034, "results": "284", "hashOfConfig": "156"}, {"size": 853, "mtime": 1750162079036, "results": "285", "hashOfConfig": "156"}, {"size": 857, "mtime": 1750162079033, "results": "286", "hashOfConfig": "156"}, {"size": 564, "mtime": 1750162079037, "results": "287", "hashOfConfig": "156"}, {"size": 594, "mtime": 1750162079035, "results": "288", "hashOfConfig": "156"}, {"size": 750, "mtime": 1750162079035, "results": "289", "hashOfConfig": "156"}, {"size": 589, "mtime": 1750162079034, "results": "290", "hashOfConfig": "156"}, {"size": 492, "mtime": 1750162079042, "results": "291", "hashOfConfig": "156"}, {"size": 810, "mtime": 1750162079041, "results": "292", "hashOfConfig": "156"}, {"size": 490, "mtime": 1750162079042, "results": "293", "hashOfConfig": "156"}, {"size": 492, "mtime": 1750162079041, "results": "294", "hashOfConfig": "156"}, {"size": 840, "mtime": 1750162079043, "results": "295", "hashOfConfig": "156"}, {"size": 1776, "mtime": 1750162079045, "results": "296", "hashOfConfig": "156"}, {"size": 896, "mtime": 1750162079044, "results": "297", "hashOfConfig": "156"}, {"size": 548, "mtime": 1750162079044, "results": "298", "hashOfConfig": "156"}, {"size": 825, "mtime": 1750162079046, "results": "299", "hashOfConfig": "156"}, {"size": 1014, "mtime": 1750162079045, "results": "300", "hashOfConfig": "156"}, {"size": 5744, "mtime": 1751280328258, "results": "301", "hashOfConfig": "156"}, {"size": 8245, "mtime": 1751279903554, "results": "302", "hashOfConfig": "156"}, {"size": 6823, "mtime": 1751280498212, "results": "303", "hashOfConfig": "156"}, {"size": 4794, "mtime": 1751272383355, "results": "304", "hashOfConfig": "156"}, {"size": 3832, "mtime": 1751274488484, "results": "305", "hashOfConfig": "156"}, {"size": 6866, "mtime": 1751269538235, "results": "306", "hashOfConfig": "156"}, {"size": 2091, "mtime": 1751304469748, "results": "307", "hashOfConfig": "156"}, {"size": 4523, "mtime": 1752327988910, "results": "308", "hashOfConfig": "156"}, {"size": 21251, "mtime": 1752953246619, "results": "309", "hashOfConfig": "156"}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kyl3u4", {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 39, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js", ["772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js", ["811"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js", ["812"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js", ["813"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js", ["814", "815", "816"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js", ["817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js", ["831", "832", "833", "834", "835", "836"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js", ["837", "838"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js", ["839", "840", "841", "842", "843", "844", "845"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js", ["846", "847", "848", "849"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js", ["850"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js", ["851"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js", ["852", "853"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js", ["854"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js", ["855", "856"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js", ["857", "858", "859"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js", ["860", "861"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js", ["862"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js", ["863", "864", "865", "866", "867"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js", ["868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js", ["880"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js", ["881"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js", ["882", "883"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js", ["884", "885"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js", ["886"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js", ["887", "888"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js", ["889"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js", ["890"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js", ["891"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js", ["892", "893", "894", "895", "896"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js", ["897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js", ["915", "916", "917", "918", "919", "920", "921", "922"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js", ["923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js", ["940", "941", "942", "943", "944", "945"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js", ["946", "947", "948", "949"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js", ["950", "951", "952", "953", "954", "955", "956", "957"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js", ["958"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js", ["959", "960"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js", ["961"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js", ["962"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js", ["963", "964", "965", "966", "967", "968", "969", "970", "971"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js", ["972"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js", ["973"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js", ["974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js", ["990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js", ["1008", "1009"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js", ["1010", "1011", "1012"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js", ["1013"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js", ["1014"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js", ["1015", "1016", "1017"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js", ["1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js", ["1027"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js", ["1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js", ["1043", "1044", "1045", "1046", "1047"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js", ["1048", "1049", "1050", "1051"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js", ["1052", "1053", "1054", "1055", "1056"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js", ["1057", "1058", "1059", "1060"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js", ["1061", "1062", "1063", "1064", "1065"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js", ["1066", "1067", "1068"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js", ["1069", "1070"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js", ["1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js", ["1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js", ["1090"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js", ["1091"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js", ["1092", "1093", "1094", "1095", "1096"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js", ["1097"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js", ["1098"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js", ["1099"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js", ["1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js", ["1108"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js", ["1109"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js", ["1110", "1111", "1112", "1113", "1114", "1115"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js", ["1116", "1117", "1118"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js", ["1119", "1120", "1121", "1122", "1123", "1124"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js", ["1125"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js", ["1126"], [], {"ruleId": "1127", "severity": 1, "message": "1128", "line": 3, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1131", "line": 4, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 23}, {"ruleId": "1132", "severity": 1, "message": "1133", "line": 63, "column": 24, "nodeType": "1134", "messageId": "1135", "endLine": 63, "endColumn": 66}, {"ruleId": "1136", "severity": 1, "message": "1137", "line": 299, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 299, "endColumn": 16}, {"ruleId": "1136", "severity": 1, "message": "1140", "line": 325, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 325, "endColumn": 17}, {"ruleId": "1136", "severity": 1, "message": "1141", "line": 567, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 567, "endColumn": 24}, {"ruleId": "1136", "severity": 1, "message": "1142", "line": 575, "column": 7, "nodeType": "1138", "messageId": "1139", "endLine": 575, "endColumn": 14}, {"ruleId": "1132", "severity": 1, "message": "1133", "line": 607, "column": 30, "nodeType": "1134", "messageId": "1135", "endLine": 607, "endColumn": 107}, {"ruleId": "1136", "severity": 1, "message": "1143", "line": 626, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 626, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1144", "line": 771, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 771, "endColumn": 20}, {"ruleId": "1136", "severity": 1, "message": "1145", "line": 772, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 772, "endColumn": 27}, {"ruleId": "1136", "severity": 1, "message": "1146", "line": 859, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 859, "endColumn": 22}, {"ruleId": "1136", "severity": 1, "message": "1147", "line": 1010, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 1010, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1148", "line": 1011, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 1011, "endColumn": 26}, {"ruleId": "1136", "severity": 1, "message": "1149", "line": 1013, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 1013, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1150", "line": 1072, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 1072, "endColumn": 22}, {"ruleId": "1136", "severity": 1, "message": "1151", "line": 1079, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 1079, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1152", "line": 1080, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 1080, "endColumn": 20}, {"ruleId": "1136", "severity": 1, "message": "1153", "line": 1205, "column": 7, "nodeType": "1138", "messageId": "1139", "endLine": 1205, "endColumn": 12}, {"ruleId": "1136", "severity": 1, "message": "1154", "line": 1901, "column": 7, "nodeType": "1138", "messageId": "1139", "endLine": 1901, "endColumn": 12}, {"ruleId": "1132", "severity": 1, "message": "1133", "line": 1983, "column": 24, "nodeType": "1134", "messageId": "1135", "endLine": 1983, "endColumn": 58}, {"ruleId": "1136", "severity": 1, "message": "1155", "line": 2001, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2001, "endColumn": 24}, {"ruleId": "1136", "severity": 1, "message": "1143", "line": 2091, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2091, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1137", "line": 2228, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2228, "endColumn": 16}, {"ruleId": "1136", "severity": 1, "message": "1141", "line": 2494, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2494, "endColumn": 24}, {"ruleId": "1136", "severity": 1, "message": "1142", "line": 2502, "column": 7, "nodeType": "1138", "messageId": "1139", "endLine": 2502, "endColumn": 14}, {"ruleId": "1136", "severity": 1, "message": "1143", "line": 2554, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2554, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1144", "line": 2703, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2703, "endColumn": 20}, {"ruleId": "1136", "severity": 1, "message": "1145", "line": 2704, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2704, "endColumn": 27}, {"ruleId": "1136", "severity": 1, "message": "1146", "line": 2787, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2787, "endColumn": 22}, {"ruleId": "1136", "severity": 1, "message": "1147", "line": 2938, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2938, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1148", "line": 2939, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2939, "endColumn": 26}, {"ruleId": "1136", "severity": 1, "message": "1149", "line": 2941, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 2941, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1150", "line": 3009, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 3009, "endColumn": 22}, {"ruleId": "1136", "severity": 1, "message": "1151", "line": 3027, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 3027, "endColumn": 23}, {"ruleId": "1136", "severity": 1, "message": "1152", "line": 3028, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 3028, "endColumn": 20}, {"ruleId": "1136", "severity": 1, "message": "1153", "line": 3136, "column": 7, "nodeType": "1138", "messageId": "1139", "endLine": 3136, "endColumn": 12}, {"ruleId": "1136", "severity": 1, "message": "1154", "line": 3653, "column": 7, "nodeType": "1138", "messageId": "1139", "endLine": 3653, "endColumn": 12}, {"ruleId": "1136", "severity": 1, "message": "1156", "line": 4138, "column": 9, "nodeType": "1138", "messageId": "1139", "endLine": 4138, "endColumn": 17}, {"ruleId": "1157", "severity": 1, "message": "1158", "line": 55, "column": 6, "nodeType": "1159", "endLine": 55, "endColumn": 8, "suggestions": "1160"}, {"ruleId": "1157", "severity": 1, "message": "1161", "line": 97, "column": 6, "nodeType": "1159", "endLine": 97, "endColumn": 43, "suggestions": "1162"}, {"ruleId": "1157", "severity": 1, "message": "1163", "line": 101, "column": 6, "nodeType": "1159", "endLine": 101, "endColumn": 40, "suggestions": "1164"}, {"ruleId": "1136", "severity": 1, "message": "1165", "line": 431, "column": 19, "nodeType": "1138", "messageId": "1139", "endLine": 431, "endColumn": 22}, {"ruleId": "1136", "severity": 1, "message": "1165", "line": 453, "column": 19, "nodeType": "1138", "messageId": "1139", "endLine": 453, "endColumn": 22}, {"ruleId": "1136", "severity": 1, "message": "1165", "line": 519, "column": 21, "nodeType": "1138", "messageId": "1139", "endLine": 519, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1166", "line": 13, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 13, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1167", "line": 14, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1168", "line": 19, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 19, "endColumn": 8}, {"ruleId": "1127", "severity": 1, "message": "1169", "line": 23, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 23, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1170", "line": 28, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 28, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1171", "line": 33, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 33, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1172", "line": 34, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 34, "endColumn": 11}, {"ruleId": "1127", "severity": 1, "message": "1173", "line": 35, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 35, "endColumn": 15}, {"ruleId": "1127", "severity": 1, "message": "1174", "line": 38, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 38, "endColumn": 11}, {"ruleId": "1127", "severity": 1, "message": "1175", "line": 40, "column": 16, "nodeType": "1129", "messageId": "1130", "endLine": 40, "endColumn": 29}, {"ruleId": "1127", "severity": 1, "message": "1176", "line": 42, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 42, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1177", "line": 327, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 327, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1178", "line": 368, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 368, "endColumn": 29}, {"ruleId": "1179", "severity": 1, "message": "1180", "line": 371, "column": 5, "nodeType": "1129", "messageId": "1181", "endLine": 371, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1182", "line": 6, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 6, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1183", "line": 7, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 7, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1170", "line": 17, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1184", "line": 18, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1185", "line": 20, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 20, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1186", "line": 21, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 21, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1187", "line": 14, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 15}, {"ruleId": "1127", "severity": 1, "message": "1188", "line": 39, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 39, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1189", "line": 9, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 9, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1190", "line": 26, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 26, "endColumn": 9}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 31, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 31, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1192", "line": 34, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 34, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1193", "line": 40, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 40, "endColumn": 19}, {"ruleId": "1157", "severity": 1, "message": "1194", "line": 310, "column": 6, "nodeType": "1159", "endLine": 310, "endColumn": 54, "suggestions": "1195"}, {"ruleId": "1127", "severity": 1, "message": "1196", "line": 367, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 367, "endColumn": 26}, {"ruleId": "1127", "severity": 1, "message": "1197", "line": 40, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 40, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 93, "column": 66, "nodeType": "1134", "messageId": "1200", "endLine": 93, "endColumn": 67, "suggestions": "1201"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 93, "column": 75, "nodeType": "1134", "messageId": "1200", "endLine": 93, "endColumn": 76, "suggestions": "1202"}, {"ruleId": "1198", "severity": 1, "message": "1203", "line": 93, "column": 77, "nodeType": "1134", "messageId": "1200", "endLine": 93, "endColumn": 78, "suggestions": "1204"}, {"ruleId": "1127", "severity": 1, "message": "1205", "line": 37, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 37, "endColumn": 15}, {"ruleId": "1127", "severity": 1, "message": "1205", "line": 37, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 37, "endColumn": 15}, {"ruleId": "1127", "severity": 1, "message": "1206", "line": 1, "column": 27, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 33}, {"ruleId": "1127", "severity": 1, "message": "1207", "line": 29, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 29, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1208", "line": 2, "column": 23, "nodeType": "1129", "messageId": "1130", "endLine": 2, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1209", "line": 23, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 23, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1205", "line": 40, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 40, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1210", "line": 32, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 32, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1211", "line": 36, "column": 18, "nodeType": "1129", "messageId": "1130", "endLine": 36, "endColumn": 33}, {"ruleId": "1127", "severity": 1, "message": "1212", "line": 37, "column": 20, "nodeType": "1129", "messageId": "1130", "endLine": 37, "endColumn": 37}, {"ruleId": "1127", "severity": 1, "message": "1213", "line": 27, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 27, "endColumn": 8}, {"ruleId": "1157", "severity": 1, "message": "1214", "line": 82, "column": 6, "nodeType": "1159", "endLine": 82, "endColumn": 41, "suggestions": "1215"}, {"ruleId": "1127", "severity": 1, "message": "1216", "line": 15, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 15, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1217", "line": 26, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 26, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1218", "line": 27, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 27, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1219", "line": 28, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 28, "endColumn": 9}, {"ruleId": "1127", "severity": 1, "message": "1220", "line": 29, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 29, "endColumn": 11}, {"ruleId": "1157", "severity": 1, "message": "1221", "line": 84, "column": 6, "nodeType": "1159", "endLine": 84, "endColumn": 41, "suggestions": "1222"}, {"ruleId": "1127", "severity": 1, "message": "1182", "line": 21, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 21, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1183", "line": 22, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 32, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 32, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1223", "line": 39, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 39, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1224", "line": 58, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 58, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1225", "line": 70, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 70, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1226", "line": 190, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 190, "endColumn": 30}, {"ruleId": "1127", "severity": 1, "message": "1227", "line": 194, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 194, "endColumn": 31}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 203, "column": 66, "nodeType": "1134", "messageId": "1200", "endLine": 203, "endColumn": 67, "suggestions": "1228"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 203, "column": 75, "nodeType": "1134", "messageId": "1200", "endLine": 203, "endColumn": 76, "suggestions": "1229"}, {"ruleId": "1198", "severity": 1, "message": "1203", "line": 203, "column": 77, "nodeType": "1134", "messageId": "1200", "endLine": 203, "endColumn": 78, "suggestions": "1230"}, {"ruleId": "1127", "severity": 1, "message": "1231", "line": 253, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 253, "endColumn": 22}, {"ruleId": "1157", "severity": 1, "message": "1232", "line": 43, "column": 6, "nodeType": "1159", "endLine": 43, "endColumn": 8, "suggestions": "1233"}, {"ruleId": "1157", "severity": 1, "message": "1234", "line": 43, "column": 6, "nodeType": "1159", "endLine": 43, "endColumn": 8, "suggestions": "1235"}, {"ruleId": "1127", "severity": 1, "message": "1236", "line": 39, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 39, "endColumn": 12}, {"ruleId": "1157", "severity": 1, "message": "1237", "line": 88, "column": 6, "nodeType": "1159", "endLine": 88, "endColumn": 39, "suggestions": "1238"}, {"ruleId": "1127", "severity": 1, "message": "1239", "line": 40, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 40, "endColumn": 24}, {"ruleId": "1157", "severity": 1, "message": "1240", "line": 107, "column": 6, "nodeType": "1159", "endLine": 107, "endColumn": 15, "suggestions": "1241"}, {"ruleId": "1157", "severity": 1, "message": "1242", "line": 45, "column": 6, "nodeType": "1159", "endLine": 45, "endColumn": 45, "suggestions": "1243"}, {"ruleId": "1127", "severity": 1, "message": "1244", "line": 33, "column": 12, "nodeType": "1129", "messageId": "1130", "endLine": 33, "endColumn": 21}, {"ruleId": "1157", "severity": 1, "message": "1245", "line": 103, "column": 6, "nodeType": "1159", "endLine": 103, "endColumn": 42, "suggestions": "1246"}, {"ruleId": "1157", "severity": 1, "message": "1247", "line": 50, "column": 6, "nodeType": "1159", "endLine": 50, "endColumn": 25, "suggestions": "1248"}, {"ruleId": "1127", "severity": 1, "message": "1249", "line": 54, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 54, "endColumn": 17}, {"ruleId": "1157", "severity": 1, "message": "1250", "line": 60, "column": 6, "nodeType": "1159", "endLine": 60, "endColumn": 39, "suggestions": "1251"}, {"ruleId": "1127", "severity": 1, "message": "1252", "line": 31, "column": 24, "nodeType": "1129", "messageId": "1130", "endLine": 31, "endColumn": 34}, {"ruleId": "1157", "severity": 1, "message": "1253", "line": 110, "column": 6, "nodeType": "1159", "endLine": 110, "endColumn": 22, "suggestions": "1254"}, {"ruleId": "1157", "severity": 1, "message": "1253", "line": 133, "column": 6, "nodeType": "1159", "endLine": 133, "endColumn": 19, "suggestions": "1255"}, {"ruleId": "1157", "severity": 1, "message": "1253", "line": 145, "column": 6, "nodeType": "1159", "endLine": 145, "endColumn": 19, "suggestions": "1256"}, {"ruleId": "1157", "severity": 1, "message": "1253", "line": 162, "column": 6, "nodeType": "1159", "endLine": 162, "endColumn": 19, "suggestions": "1257"}, {"ruleId": "1127", "severity": 1, "message": "1258", "line": 10, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 10, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1259", "line": 13, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 13, "endColumn": 8}, {"ruleId": "1127", "severity": 1, "message": "1260", "line": 14, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1261", "line": 15, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 15, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1262", "line": 16, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 16, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1263", "line": 17, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1264", "line": 18, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 11}, {"ruleId": "1127", "severity": 1, "message": "1167", "line": 20, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 20, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 22, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1265", "line": 31, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 31, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1266", "line": 52, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 52, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1267", "line": 52, "column": 23, "nodeType": "1129", "messageId": "1130", "endLine": 52, "endColumn": 37}, {"ruleId": "1127", "severity": 1, "message": "1268", "line": 53, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 53, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1269", "line": 53, "column": 24, "nodeType": "1129", "messageId": "1130", "endLine": 53, "endColumn": 39}, {"ruleId": "1127", "severity": 1, "message": "1270", "line": 227, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 227, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1271", "line": 236, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 236, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1272", "line": 245, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 245, "endColumn": 29}, {"ruleId": "1127", "severity": 1, "message": "1273", "line": 258, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 258, "endColumn": 28}, {"ruleId": "1127", "severity": 1, "message": "1274", "line": 2, "column": 23, "nodeType": "1129", "messageId": "1130", "endLine": 2, "endColumn": 34}, {"ruleId": "1127", "severity": 1, "message": "1275", "line": 4, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1167", "line": 13, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 13, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 15, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 15, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1266", "line": 45, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 45, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1267", "line": 45, "column": 23, "nodeType": "1129", "messageId": "1130", "endLine": 45, "endColumn": 37}, {"ruleId": "1127", "severity": 1, "message": "1268", "line": 46, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 46, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1269", "line": 46, "column": 24, "nodeType": "1129", "messageId": "1130", "endLine": 46, "endColumn": 39}, {"ruleId": "1127", "severity": 1, "message": "1276", "line": 5, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 5, "endColumn": 9}, {"ruleId": "1127", "severity": 1, "message": "1189", "line": 7, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 7, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1182", "line": 9, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 9, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1183", "line": 10, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 10, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1258", "line": 11, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 11, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1217", "line": 16, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 16, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1218", "line": 17, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1219", "line": 18, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 9}, {"ruleId": "1127", "severity": 1, "message": "1220", "line": 19, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 19, "endColumn": 11}, {"ruleId": "1127", "severity": 1, "message": "1277", "line": 33, "column": 18, "nodeType": "1129", "messageId": "1130", "endLine": 33, "endColumn": 33}, {"ruleId": "1127", "severity": 1, "message": "1278", "line": 46, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 46, "endColumn": 33}, {"ruleId": "1127", "severity": 1, "message": "1279", "line": 46, "column": 35, "nodeType": "1129", "messageId": "1130", "endLine": 46, "endColumn": 59}, {"ruleId": "1127", "severity": 1, "message": "1280", "line": 51, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 51, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1281", "line": 58, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 58, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1282", "line": 110, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 110, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1283", "line": 154, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 154, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1284", "line": 215, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 215, "endColumn": 26}, {"ruleId": "1127", "severity": 1, "message": "1278", "line": 39, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 39, "endColumn": 33}, {"ruleId": "1127", "severity": 1, "message": "1279", "line": 39, "column": 64, "nodeType": "1129", "messageId": "1130", "endLine": 39, "endColumn": 88}, {"ruleId": "1127", "severity": 1, "message": "1281", "line": 59, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 59, "endColumn": 21}, {"ruleId": "1157", "severity": 1, "message": "1285", "line": 155, "column": 6, "nodeType": "1159", "endLine": 155, "endColumn": 16, "suggestions": "1286"}, {"ruleId": "1157", "severity": 1, "message": "1287", "line": 162, "column": 6, "nodeType": "1159", "endLine": 162, "endColumn": 31, "suggestions": "1288"}, {"ruleId": "1127", "severity": 1, "message": "1289", "line": 269, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 269, "endColumn": 21}, {"ruleId": "1157", "severity": 1, "message": "1194", "line": 180, "column": 6, "nodeType": "1159", "endLine": 180, "endColumn": 28, "suggestions": "1290"}, {"ruleId": "1157", "severity": 1, "message": "1291", "line": 217, "column": 6, "nodeType": "1159", "endLine": 217, "endColumn": 28, "suggestions": "1292"}, {"ruleId": "1157", "severity": 1, "message": "1293", "line": 257, "column": 6, "nodeType": "1159", "endLine": 257, "endColumn": 14, "suggestions": "1294"}, {"ruleId": "1157", "severity": 1, "message": "1295", "line": 324, "column": 6, "nodeType": "1159", "endLine": 324, "endColumn": 53, "suggestions": "1296"}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 14, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1170", "line": 30, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 30, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 70, "column": 64, "nodeType": "1134", "messageId": "1200", "endLine": 70, "endColumn": 65, "suggestions": "1297"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 70, "column": 73, "nodeType": "1134", "messageId": "1200", "endLine": 70, "endColumn": 74, "suggestions": "1298"}, {"ruleId": "1198", "severity": 1, "message": "1203", "line": 70, "column": 75, "nodeType": "1134", "messageId": "1200", "endLine": 70, "endColumn": 76, "suggestions": "1299"}, {"ruleId": "1127", "severity": 1, "message": "1252", "line": 91, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 91, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1205", "line": 92, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 92, "endColumn": 15}, {"ruleId": "1127", "severity": 1, "message": "1300", "line": 92, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 92, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1301", "line": 25, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 25, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1211", "line": 22, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 23}, {"ruleId": "1157", "severity": 1, "message": "1242", "line": 46, "column": 6, "nodeType": "1159", "endLine": 46, "endColumn": 45, "suggestions": "1302"}, {"ruleId": "1157", "severity": 1, "message": "1303", "line": 65, "column": 6, "nodeType": "1159", "endLine": 65, "endColumn": 45, "suggestions": "1304"}, {"ruleId": "1157", "severity": 1, "message": "1245", "line": 83, "column": 6, "nodeType": "1159", "endLine": 83, "endColumn": 32, "suggestions": "1305"}, {"ruleId": "1127", "severity": 1, "message": "1306", "line": 17, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1307", "line": 29, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 29, "endColumn": 11}, {"ruleId": "1127", "severity": 1, "message": "1193", "line": 32, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 32, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1308", "line": 33, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 33, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1309", "line": 40, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 40, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1310", "line": 41, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 41, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1205", "line": 49, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 49, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1311", "line": 68, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 68, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1312", "line": 289, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 289, "endColumn": 29}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 4, "column": 29, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 36}, {"ruleId": "1127", "severity": 1, "message": "1280", "line": 28, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 28, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1189", "line": 14, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1190", "line": 31, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 31, "endColumn": 9}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 36, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 36, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1192", "line": 39, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 39, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1313", "line": 42, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 42, "endColumn": 9}, {"ruleId": "1127", "severity": 1, "message": "1314", "line": 43, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 43, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1315", "line": 44, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 44, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1316", "line": 45, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 45, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1193", "line": 51, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 51, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1249", "line": 174, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 174, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1317", "line": 203, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 203, "endColumn": 19}, {"ruleId": "1157", "severity": 1, "message": "1221", "line": 298, "column": 6, "nodeType": "1159", "endLine": 298, "endColumn": 48, "suggestions": "1318"}, {"ruleId": "1127", "severity": 1, "message": "1196", "line": 337, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 337, "endColumn": 26}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 422, "column": 68, "nodeType": "1134", "messageId": "1200", "endLine": 422, "endColumn": 69, "suggestions": "1319"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 422, "column": 77, "nodeType": "1134", "messageId": "1200", "endLine": 422, "endColumn": 78, "suggestions": "1320"}, {"ruleId": "1198", "severity": 1, "message": "1203", "line": 422, "column": 79, "nodeType": "1134", "messageId": "1200", "endLine": 422, "endColumn": 80, "suggestions": "1321"}, {"ruleId": "1127", "severity": 1, "message": "1182", "line": 15, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 15, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1183", "line": 16, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 16, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1316", "line": 30, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 30, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1322", "line": 31, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 31, "endColumn": 30}, {"ruleId": "1127", "severity": 1, "message": "1177", "line": 63, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 63, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1323", "line": 71, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 71, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1324", "line": 72, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 72, "endColumn": 22}, {"ruleId": "1325", "severity": 2, "message": "1326", "line": 109, "column": 13, "nodeType": "1129", "messageId": "1327", "endLine": 109, "endColumn": 27}, {"ruleId": "1325", "severity": 2, "message": "1326", "line": 110, "column": 58, "nodeType": "1129", "messageId": "1327", "endLine": 110, "endColumn": 72}, {"ruleId": "1157", "severity": 1, "message": "1328", "line": 134, "column": 6, "nodeType": "1159", "endLine": 134, "endColumn": 46, "suggestions": "1329"}, {"ruleId": "1325", "severity": 2, "message": "1326", "line": 134, "column": 21, "nodeType": "1129", "messageId": "1327", "endLine": 134, "endColumn": 35}, {"ruleId": "1157", "severity": 1, "message": "1330", "line": 212, "column": 6, "nodeType": "1159", "endLine": 212, "endColumn": 39, "suggestions": "1331"}, {"ruleId": "1127", "severity": 1, "message": "1332", "line": 294, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 294, "endColumn": 23}, {"ruleId": "1325", "severity": 2, "message": "1326", "line": 334, "column": 11, "nodeType": "1129", "messageId": "1327", "endLine": 334, "endColumn": 25}, {"ruleId": "1325", "severity": 2, "message": "1326", "line": 335, "column": 56, "nodeType": "1129", "messageId": "1327", "endLine": 335, "endColumn": 70}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 384, "column": 68, "nodeType": "1134", "messageId": "1200", "endLine": 384, "endColumn": 69, "suggestions": "1333"}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 384, "column": 77, "nodeType": "1134", "messageId": "1200", "endLine": 384, "endColumn": 78, "suggestions": "1334"}, {"ruleId": "1198", "severity": 1, "message": "1203", "line": 384, "column": 79, "nodeType": "1134", "messageId": "1200", "endLine": 384, "endColumn": 80, "suggestions": "1335"}, {"ruleId": "1127", "severity": 1, "message": "1336", "line": 7, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 7, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1337", "line": 22, "column": 19, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 30}, {"ruleId": "1157", "severity": 1, "message": "1194", "line": 196, "column": 6, "nodeType": "1159", "endLine": 196, "endColumn": 28, "suggestions": "1338"}, {"ruleId": "1157", "severity": 1, "message": "1291", "line": 233, "column": 6, "nodeType": "1159", "endLine": 233, "endColumn": 28, "suggestions": "1339"}, {"ruleId": "1157", "severity": 1, "message": "1293", "line": 273, "column": 6, "nodeType": "1159", "endLine": 273, "endColumn": 14, "suggestions": "1340"}, {"ruleId": "1127", "severity": 1, "message": "1341", "line": 24, "column": 24, "nodeType": "1129", "messageId": "1130", "endLine": 24, "endColumn": 29}, {"ruleId": "1157", "severity": 1, "message": "1245", "line": 84, "column": 6, "nodeType": "1159", "endLine": 84, "endColumn": 32, "suggestions": "1342"}, {"ruleId": "1127", "severity": 1, "message": "1258", "line": 5, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 5, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1182", "line": 6, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 6, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1208", "line": 20, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 20, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1343", "line": 27, "column": 18, "nodeType": "1129", "messageId": "1130", "endLine": 27, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1344", "line": 29, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 29, "endColumn": 15}, {"ruleId": "1127", "severity": 1, "message": "1281", "line": 44, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 44, "endColumn": 21}, {"ruleId": "1157", "severity": 1, "message": "1345", "line": 66, "column": 6, "nodeType": "1159", "endLine": 66, "endColumn": 19, "suggestions": "1346"}, {"ruleId": "1127", "severity": 1, "message": "1347", "line": 127, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 127, "endColumn": 40}, {"ruleId": "1127", "severity": 1, "message": "1348", "line": 135, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 135, "endColumn": 43}, {"ruleId": "1127", "severity": 1, "message": "1349", "line": 147, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 147, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1350", "line": 156, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 156, "endColumn": 40}, {"ruleId": "1127", "severity": 1, "message": "1351", "line": 184, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 184, "endColumn": 23}, {"ruleId": "1157", "severity": 1, "message": "1242", "line": 74, "column": 6, "nodeType": "1159", "endLine": 74, "endColumn": 45, "suggestions": "1352"}, {"ruleId": "1127", "severity": 1, "message": "1306", "line": 13, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 13, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1353", "line": 16, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 16, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 17, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1354", "line": 18, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 9}, {"ruleId": "1127", "severity": 1, "message": "1355", "line": 48, "column": 40, "nodeType": "1129", "messageId": "1130", "endLine": 48, "endColumn": 49}, {"ruleId": "1127", "severity": 1, "message": "1356", "line": 50, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 50, "endColumn": 32}, {"ruleId": "1127", "severity": 1, "message": "1279", "line": 50, "column": 34, "nodeType": "1129", "messageId": "1130", "endLine": 50, "endColumn": 58}, {"ruleId": "1127", "severity": 1, "message": "1278", "line": 50, "column": 89, "nodeType": "1129", "messageId": "1130", "endLine": 50, "endColumn": 112}, {"ruleId": "1127", "severity": 1, "message": "1249", "line": 58, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 58, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1177", "line": 60, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 60, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1350", "line": 197, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 197, "endColumn": 40}, {"ruleId": "1127", "severity": 1, "message": "1357", "line": 328, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 328, "endColumn": 28}, {"ruleId": "1127", "severity": 1, "message": "1358", "line": 347, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 347, "endColumn": 35}, {"ruleId": "1127", "severity": 1, "message": "1359", "line": 370, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 370, "endColumn": 36}, {"ruleId": "1127", "severity": 1, "message": "1360", "line": 631, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 631, "endColumn": 29}, {"ruleId": "1127", "severity": 1, "message": "1361", "line": 28, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 28, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1289", "line": 267, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 267, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1362", "line": 464, "column": 12, "nodeType": "1129", "messageId": "1130", "endLine": 464, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1289", "line": 485, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 485, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1363", "line": 508, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 508, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1193", "line": 27, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 27, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1249", "line": 36, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 36, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1205", "line": 37, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 37, "endColumn": 14}, {"ruleId": "1157", "severity": 1, "message": "1364", "line": 467, "column": 6, "nodeType": "1159", "endLine": 467, "endColumn": 32, "suggestions": "1365"}, {"ruleId": "1127", "severity": 1, "message": "1366", "line": 18, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1223", "line": 22, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1208", "line": 34, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 34, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1367", "line": 49, "column": 20, "nodeType": "1129", "messageId": "1130", "endLine": 49, "endColumn": 37}, {"ruleId": "1157", "severity": 1, "message": "1368", "line": 110, "column": 6, "nodeType": "1159", "endLine": 110, "endColumn": 38, "suggestions": "1369"}, {"ruleId": "1127", "severity": 1, "message": "1306", "line": 8, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 8, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1370", "line": 22, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1371", "line": 29, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 29, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1372", "line": 142, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 142, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1373", "line": 18, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1374", "line": 148, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 148, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1375", "line": 158, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 158, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1376", "line": 177, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 177, "endColumn": 29}, {"ruleId": "1127", "severity": 1, "message": "1377", "line": 306, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 306, "endColumn": 30}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 11, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 11, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1378", "line": 26, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 26, "endColumn": 29}, {"ruleId": "1127", "severity": 1, "message": "1379", "line": 27, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 27, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1378", "line": 21, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 21, "endColumn": 29}, {"ruleId": "1127", "severity": 1, "message": "1379", "line": 22, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1380", "line": 18, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 12}, {"ruleId": "1127", "severity": 1, "message": "1381", "line": 19, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 19, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1382", "line": 20, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 20, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1192", "line": 21, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 21, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1383", "line": 22, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 11}, {"ruleId": "1127", "severity": 1, "message": "1384", "line": 24, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 24, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 34, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 34, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1309", "line": 43, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 43, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1385", "line": 44, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 44, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1386", "line": 45, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 45, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1387", "line": 127, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 127, "endColumn": 18}, {"ruleId": "1127", "severity": 1, "message": "1206", "line": 1, "column": 38, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 44}, {"ruleId": "1127", "severity": 1, "message": "1388", "line": 1, "column": 46, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 53}, {"ruleId": "1127", "severity": 1, "message": "1389", "line": 4, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 18}, {"ruleId": "1127", "severity": 1, "message": "1390", "line": 5, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 5, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1391", "line": 6, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 6, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1392", "line": 9, "column": 46, "nodeType": "1129", "messageId": "1130", "endLine": 9, "endColumn": 71}, {"ruleId": "1127", "severity": 1, "message": "1393", "line": 9, "column": 73, "nodeType": "1129", "messageId": "1130", "endLine": 9, "endColumn": 88}, {"ruleId": "1127", "severity": 1, "message": "1394", "line": 14, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 27}, {"ruleId": "1157", "severity": 1, "message": "1395", "line": 49, "column": 6, "nodeType": "1159", "endLine": 49, "endColumn": 22, "suggestions": "1396"}, {"ruleId": "1157", "severity": 1, "message": "1397", "line": 40, "column": 8, "nodeType": "1159", "endLine": 40, "endColumn": 33, "suggestions": "1398"}, {"ruleId": "1157", "severity": 1, "message": "1399", "line": 128, "column": 6, "nodeType": "1159", "endLine": 128, "endColumn": 33, "suggestions": "1400"}, {"ruleId": "1157", "severity": 1, "message": "1401", "line": 132, "column": 6, "nodeType": "1159", "endLine": 132, "endColumn": 37, "suggestions": "1402"}, {"ruleId": "1157", "severity": 1, "message": "1403", "line": 137, "column": 6, "nodeType": "1159", "endLine": 137, "endColumn": 8, "suggestions": "1404"}, {"ruleId": "1157", "severity": 1, "message": "1405", "line": 300, "column": 6, "nodeType": "1159", "endLine": 300, "endColumn": 8, "suggestions": "1406"}, {"ruleId": "1157", "severity": 1, "message": "1401", "line": 403, "column": 6, "nodeType": "1159", "endLine": 403, "endColumn": 8, "suggestions": "1407"}, {"ruleId": "1157", "severity": 1, "message": "1408", "line": 28, "column": 6, "nodeType": "1159", "endLine": 28, "endColumn": 8, "suggestions": "1409"}, {"ruleId": "1157", "severity": 1, "message": "1410", "line": 230, "column": 6, "nodeType": "1159", "endLine": 230, "endColumn": 34, "suggestions": "1411"}, {"ruleId": "1157", "severity": 1, "message": "1412", "line": 220, "column": 6, "nodeType": "1159", "endLine": 220, "endColumn": 50, "suggestions": "1413"}, {"ruleId": "1157", "severity": 1, "message": "1414", "line": 59, "column": 6, "nodeType": "1159", "endLine": 59, "endColumn": 23, "suggestions": "1415"}, {"ruleId": "1416", "severity": 1, "message": "1417", "line": 148, "column": 37, "nodeType": "1418", "messageId": "1139", "endLine": 148, "endColumn": 39}, {"ruleId": "1416", "severity": 1, "message": "1419", "line": 173, "column": 82, "nodeType": "1418", "messageId": "1139", "endLine": 173, "endColumn": 84}, {"ruleId": "1416", "severity": 1, "message": "1419", "line": 228, "column": 104, "nodeType": "1418", "messageId": "1139", "endLine": 228, "endColumn": 106}, {"ruleId": "1416", "severity": 1, "message": "1419", "line": 257, "column": 44, "nodeType": "1418", "messageId": "1139", "endLine": 257, "endColumn": 46}, {"ruleId": "1416", "severity": 1, "message": "1419", "line": 261, "column": 44, "nodeType": "1418", "messageId": "1139", "endLine": 261, "endColumn": 46}, {"ruleId": "1416", "severity": 1, "message": "1419", "line": 265, "column": 44, "nodeType": "1418", "messageId": "1139", "endLine": 265, "endColumn": 46}, {"ruleId": "1416", "severity": 1, "message": "1419", "line": 271, "column": 44, "nodeType": "1418", "messageId": "1139", "endLine": 271, "endColumn": 46}, {"ruleId": "1420", "severity": 1, "message": "1421", "line": 9, "column": 23, "nodeType": "1422", "messageId": "1139", "endLine": 9, "endColumn": 26}, {"ruleId": "1157", "severity": 1, "message": "1423", "line": 33, "column": 6, "nodeType": "1159", "endLine": 33, "endColumn": 28, "suggestions": "1424"}, {"ruleId": "1127", "severity": 1, "message": "1306", "line": 17, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1425", "line": 23, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 23, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1426", "line": 25, "column": 14, "nodeType": "1129", "messageId": "1130", "endLine": 25, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1427", "line": 28, "column": 14, "nodeType": "1129", "messageId": "1130", "endLine": 28, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1370", "line": 30, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 30, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1428", "line": 31, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 31, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1306", "line": 17, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1370", "line": 27, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 27, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1426", "line": 28, "column": 14, "nodeType": "1129", "messageId": "1130", "endLine": 28, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1306", "line": 15, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 15, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1213", "line": 20, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 20, "endColumn": 8}, {"ruleId": "1127", "severity": 1, "message": "1425", "line": 23, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 23, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1427", "line": 26, "column": 14, "nodeType": "1129", "messageId": "1130", "endLine": 26, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1361", "line": 28, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 28, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1429", "line": 29, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 29, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1278", "line": 19, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 19, "endColumn": 33}, {"ruleId": "1127", "severity": 1, "message": "1425", "line": 33, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 33, "endColumn": 27}, "no-unused-vars", "'meetingIssuesEn' is defined but never used.", "Identifier", "unusedVar", "'meetingIssuesAr' is defined but never used.", "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "no-dupe-keys", "Duplicate key 'details'.", "ObjectExpression", "unexpected", "Duplicate key 'editInfo'.", "Duplicate key 'noTeachersFound'.", "Duplicate key 'booking'.", "Duplicate key 'selectDuration'.", "Duplicate key 'invalidCode'.", "Duplicate key 'verificationFailed'.", "Duplicate key 'updateProfile'.", "Duplicate key 'nativeLanguage'.", "Duplicate key 'teachingLanguages'.", "Duplicate key 'qualifications'.", "Duplicate key 'formHasErrors'.", "Duplicate key 'allowedFormats'.", "Duplicate key 'maxFileSize'.", "Duplicate key 'admin'.", "Duplicate key 'about'.", "Duplicate key 'errorCancelling'.", "Duplicate key 'earnings'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleLogout'. Either include it or remove the dependency array.", "ArrayExpression", ["1430"], "React Hook useEffect has a missing dependency: 'socket'. Either include it or remove the dependency array.", ["1431"], "React Hook useEffect has a missing dependency: 'fetchUnreadCount'. Either include it or remove the dependency array.", ["1432"], "Duplicate key 'gap'.", "'CardMedia' is defined but never used.", "'IconButton' is defined but never used.", "'Slide' is defined but never used.", "'Zoom' is defined but never used.", "'LanguageIcon' is defined but never used.", "'Star' is defined but never used.", "'Timeline' is defined but never used.", "'LocalLibrary' is defined but never used.", "'Language' is defined but never used.", "'TranslateIcon' is defined but never used.", "'useAuth' is defined but never used.", "'isMobile' is assigned a value but never used.", "'handleLanguageChange' is assigned a value but never used.", "no-const-assign", "'isRtl' is constant.", "const", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'PeopleIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'ListItemIcon' is defined but never used.", "'sectionIcons' is assigned a value but never used.", "'TextField' is defined but never used.", "'Drawer' is defined but never used.", "'Divider' is defined but never used.", "'FormControlLabel' is defined but never used.", "'StarIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", ["1433"], "'getPriceRangeText' is assigned a value but never used.", "'theme' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\&.", "unnecessaryEscape", ["1434", "1435"], ["1436", "1437"], "Unnecessary escape character: \\?.", ["1438", "1439"], "'isRtl' is assigned a value but never used.", "'useRef' is defined but never used.", "'GoogleIcon' is defined but never used.", "'Link' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'ArrowUpwardIcon' is defined but never used.", "'ArrowDownwardIcon' is defined but never used.", "'Stack' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1440"], "'CircularProgress' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTeachers'. Either include it or remove the dependency array.", ["1441"], "'Tooltip' is defined but never used.", "'debounce' is defined but never used.", "'openVideoDialog' is assigned a value but never used.", "'handleOpenVideoDialog' is assigned a value but never used.", "'handleCloseVideoDialog' is assigned a value but never used.", ["1442", "1443"], ["1444", "1445"], ["1446", "1447"], "'safeParseJSON' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLanguages'. Either include it or remove the dependency array.", ["1448"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["1449"], "'t' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUpdates'. Either include it or remove the dependency array.", ["1450"], "'recentSessions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["1451"], "React Hook useEffect has missing dependencies: 'fetchBalance' and 'fetchTransactions'. Either include them or remove the dependency array.", ["1452"], "'CheckIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["1453"], "React Hook useEffect has a missing dependency: 'fetchEarnings'. Either include it or remove the dependency array.", ["1454"], "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchWithdrawals'. Either include it or remove the dependency array.", ["1455"], "'updateUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProfileData'. Either include it or remove the dependency array.", ["1456"], ["1457"], ["1458"], ["1459"], "'Grid' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'EventBusyIcon' is defined but never used.", "'selectedDay' is assigned a value but never used.", "'setSelectedDay' is assigned a value but never used.", "'selectedHour' is assigned a value but never used.", "'setSelectedHour' is assigned a value but never used.", "'selectAllForDay' is assigned a value but never used.", "'clearAllForDay' is assigned a value but never used.", "'selectTimeForAllDays' is assigned a value but never used.", "'clearTimeForAllDays' is assigned a value but never used.", "'useLocation' is defined but never used.", "'axios' is defined but never used.", "'Button' is defined but never used.", "'ContentCopyIcon' is defined but never used.", "'convertFromDatabaseTime' is defined but never used.", "'getCurrentTimeInTimezone' is defined but never used.", "'currentUser' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "'calculatePrice' is assigned a value but never used.", "'handleCopyLink' is assigned a value but never used.", "'getMeetingActions' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableHours' and 'fetchWeeklyBreaks'. Either include them or remove the dependency array.", ["1460"], "React Hook useEffect has a missing dependency: 'fetchWeeklyBreaks'. Either include it or remove the dependency array.", ["1461"], "'response' is assigned a value but never used.", ["1462"], "React Hook useEffect has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1463"], "React Hook useCallback has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1464"], "React Hook useCallback has an unnecessary dependency: 'messages'. Either exclude it or remove the dependency array.", ["1465"], ["1466", "1467"], ["1468", "1469"], ["1470", "1471"], "'setIsRtl' is assigned a value but never used.", "'i18n' is defined but never used.", ["1472"], "React Hook useEffect has missing dependencies: 'fetchBalance', 'fetchSettings', and 'fetchWithdrawals'. Either include them or remove the dependency array.", ["1473"], ["1474"], "'Chip' is defined but never used.", "'Collapse' is defined but never used.", "'StarBorderIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'expandedReplies' is assigned a value but never used.", "'toggleReplyExpansion' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'fullScreen' is assigned a value but never used.", ["1475"], ["1476", "1477"], ["1478", "1479"], ["1480", "1481"], "'MuiIconButton' is defined but never used.", "'availableSlots' is assigned a value but never used.", "'loadingSlots' is assigned a value but never used.", "no-undef", "'studentProfile' is not defined.", "undef", "React Hook useEffect has an unnecessary dependency: 'studentProfile.timezone'. Either exclude it or remove the dependency array. Outer scope values like 'studentProfile.timezone' aren't valid dependencies because mutating them doesn't re-render the component.", ["1482"], "React Hook useCallback has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["1483"], "'handleBookSlot' is assigned a value but never used.", ["1484", "1485"], ["1486", "1487"], ["1488", "1489"], "'Typography' is defined but never used.", "'isConnected' is assigned a value but never used.", ["1490"], ["1491"], ["1492"], "'token' is assigned a value but never used.", ["1493"], "'MoneyIcon' is defined but never used.", "'toast' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMeetings'. Either include it or remove the dependency array.", ["1494"], "'getMeetingDateInStudentTimezone' is assigned a value but never used.", "'formatMeetingDateInStudentTimezone' is assigned a value but never used.", "'dateFnsFormat' is assigned a value but never used.", "'getCurrentTimeInStudentTimezone' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", ["1495"], "'CardActions' is defined but never used.", "'Avatar' is defined but never used.", "'isSameDay' is defined but never used.", "'convertBookingDateTime' is defined but never used.", "'isFullHourAvailable' is assigned a value but never used.", "'checkCrossHourAvailability' is assigned a value but never used.", "'checkSecondHalfAvailability' is assigned a value but never used.", "'renderBookingSuccess' is assigned a value but never used.", "'PersonIcon' is defined but never used.", "'datePart' is assigned a value but never used.", "'renderBookings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'teachers'. Either include it or remove the dependency array.", ["1496"], "'ListItemButton' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPending'. Either include it or remove the dependency array.", ["1497"], "'AccessTimeIcon' is defined but never used.", "'moment' is defined but never used.", "'breakUtcDateTime' is assigned a value but never used.", "'CheckCircleIcon' is defined but never used.", "'isPartOfFullHour' is assigned a value but never used.", "'isFullHourStart' is assigned a value but never used.", "'isFullHourSecondSlot' is assigned a value but never used.", "'formattedDate' is defined but never used.", "'formatDistanceToNow' is defined but never used.", "'ar' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'Checkbox' is defined but never used.", "'FormHelperText' is defined but never used.", "'VideoFileIcon' is defined but never used.", "'LinkIcon' is defined but never used.", "'timeSlots' is assigned a value but never used.", "'useMemo' is defined but never used.", "'MeetingConsumer' is defined but never used.", "'useMeeting' is defined but never used.", "'useParticipant' is defined but never used.", "'createMeetingWithCustomId' is defined but never used.", "'validateMeeting' is defined but never used.", "'WaitingToJoinScreen' is defined but never used.", "React Hook useEffect has a missing dependency: 'isExemptPage'. Either include it or remove the dependency array.", ["1498"], "React Hook useEffect has an unnecessary dependency: 'raisedHandsParticipants'. Either exclude it or remove the dependency array. Outer scope values like 'raisedHandsParticipants' aren't valid dependencies because mutating them doesn't re-render the component.", ["1499"], "React Hook useEffect has a missing dependency: 'getCameraDevices'. Either include it or remove the dependency array.", ["1500"], "React Hook useEffect has a missing dependency: 'getAudioDevices'. Either include it or remove the dependency array.", ["1501"], "React Hook useEffect has a missing dependency: 'checkMediaPermission'. Either include it or remove the dependency array.", ["1502"], "React Hook useEffect has a missing dependency: 'onDeviceChanged'. Either include it or remove the dependency array.", ["1503"], ["1504"], "React Hook useEffect has a missing dependency: 'waitingMessages'. Either include it or remove the dependency array.", ["1505"], "React Hook useEffect has missing dependencies: 'leave', 'onClose', and 'setIsMeetingLeft'. Either include them or remove the dependency array. If 'setIsMeetingLeft' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1506"], "React Hook useEffect has a missing dependency: 'updateStats'. Either include it or remove the dependency array.", ["1507"], "React Hook useEffect has a missing dependency: 'setDidDeviceChange'. Either include it or remove the dependency array. If 'setDidDeviceChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1508"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "Expected '===' and instead saw '=='.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "React Hook useEffect has a missing dependency: 'meetingId'. Either include it or remove the dependency array.", ["1509"], "'ScheduleIcon' is defined but never used.", "'PaymentIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'CreditCardIcon' is defined but never used.", "'SchoolIcon' is defined but never used.", {"desc": "1510", "fix": "1511"}, {"desc": "1512", "fix": "1513"}, {"desc": "1514", "fix": "1515"}, {"desc": "1516", "fix": "1517"}, {"messageId": "1518", "fix": "1519", "desc": "1520"}, {"messageId": "1521", "fix": "1522", "desc": "1523"}, {"messageId": "1518", "fix": "1524", "desc": "1520"}, {"messageId": "1521", "fix": "1525", "desc": "1523"}, {"messageId": "1518", "fix": "1526", "desc": "1520"}, {"messageId": "1521", "fix": "1527", "desc": "1523"}, {"desc": "1528", "fix": "1529"}, {"desc": "1530", "fix": "1531"}, {"messageId": "1518", "fix": "1532", "desc": "1520"}, {"messageId": "1521", "fix": "1533", "desc": "1523"}, {"messageId": "1518", "fix": "1534", "desc": "1520"}, {"messageId": "1521", "fix": "1535", "desc": "1523"}, {"messageId": "1518", "fix": "1536", "desc": "1520"}, {"messageId": "1521", "fix": "1537", "desc": "1523"}, {"desc": "1538", "fix": "1539"}, {"desc": "1540", "fix": "1541"}, {"desc": "1542", "fix": "1543"}, {"desc": "1544", "fix": "1545"}, {"desc": "1546", "fix": "1547"}, {"desc": "1548", "fix": "1549"}, {"desc": "1550", "fix": "1551"}, {"desc": "1552", "fix": "1553"}, {"desc": "1554", "fix": "1555"}, {"desc": "1556", "fix": "1557"}, {"desc": "1556", "fix": "1558"}, {"desc": "1556", "fix": "1559"}, {"desc": "1560", "fix": "1561"}, {"desc": "1562", "fix": "1563"}, {"desc": "1564", "fix": "1565"}, {"desc": "1566", "fix": "1567"}, {"desc": "1568", "fix": "1569"}, {"desc": "1570", "fix": "1571"}, {"messageId": "1518", "fix": "1572", "desc": "1520"}, {"messageId": "1521", "fix": "1573", "desc": "1523"}, {"messageId": "1518", "fix": "1574", "desc": "1520"}, {"messageId": "1521", "fix": "1575", "desc": "1523"}, {"messageId": "1518", "fix": "1576", "desc": "1520"}, {"messageId": "1521", "fix": "1577", "desc": "1523"}, {"desc": "1546", "fix": "1578"}, {"desc": "1579", "fix": "1580"}, {"desc": "1581", "fix": "1582"}, {"desc": "1583", "fix": "1584"}, {"messageId": "1518", "fix": "1585", "desc": "1520"}, {"messageId": "1521", "fix": "1586", "desc": "1523"}, {"messageId": "1518", "fix": "1587", "desc": "1520"}, {"messageId": "1521", "fix": "1588", "desc": "1523"}, {"messageId": "1518", "fix": "1589", "desc": "1520"}, {"messageId": "1521", "fix": "1590", "desc": "1523"}, {"desc": "1591", "fix": "1592"}, {"desc": "1593", "fix": "1594"}, {"messageId": "1518", "fix": "1595", "desc": "1520"}, {"messageId": "1521", "fix": "1596", "desc": "1523"}, {"messageId": "1518", "fix": "1597", "desc": "1520"}, {"messageId": "1521", "fix": "1598", "desc": "1523"}, {"messageId": "1518", "fix": "1599", "desc": "1520"}, {"messageId": "1521", "fix": "1600", "desc": "1523"}, {"desc": "1564", "fix": "1601"}, {"desc": "1566", "fix": "1602"}, {"desc": "1568", "fix": "1603"}, {"desc": "1581", "fix": "1604"}, {"desc": "1605", "fix": "1606"}, {"desc": "1546", "fix": "1607"}, {"desc": "1608", "fix": "1609"}, {"desc": "1610", "fix": "1611"}, {"desc": "1612", "fix": "1613"}, {"desc": "1614", "fix": "1615"}, {"desc": "1616", "fix": "1617"}, {"desc": "1618", "fix": "1619"}, {"desc": "1620", "fix": "1621"}, {"desc": "1622", "fix": "1623"}, {"desc": "1624", "fix": "1625"}, {"desc": "1626", "fix": "1627"}, {"desc": "1628", "fix": "1629"}, {"desc": "1630", "fix": "1631"}, {"desc": "1632", "fix": "1633"}, {"desc": "1634", "fix": "1635"}, "Update the dependencies array to be: [handleLogout]", {"range": "1636", "text": "1637"}, "Update the dependencies array to be: [isAuthenticated, token, currentUser, socket]", {"range": "1638", "text": "1639"}, "Update the dependencies array to be: [socket, isConnected, currentUser, fetchUnreadCount]", {"range": "1640", "text": "1641"}, "Update the dependencies array to be: [appliedFilters, page, searchFilters.priceRange, t]", {"range": "1642", "text": "1643"}, "removeEscape", {"range": "1644", "text": "1645"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1646", "text": "1647"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1648", "text": "1645"}, {"range": "1649", "text": "1647"}, {"range": "1650", "text": "1645"}, {"range": "1651", "text": "1647"}, "Update the dependencies array to be: [fetchStudents, page, rowsPerPage, searchQuery, t]", {"range": "1652", "text": "1653"}, "Update the dependencies array to be: [fetchTeachers, page, rowsPerPage, searchQuery, t]", {"range": "1654", "text": "1655"}, {"range": "1656", "text": "1645"}, {"range": "1657", "text": "1647"}, {"range": "1658", "text": "1645"}, {"range": "1659", "text": "1647"}, {"range": "1660", "text": "1645"}, {"range": "1661", "text": "1647"}, "Update the dependencies array to be: [fetchLanguages]", {"range": "1662", "text": "1663"}, "Update the dependencies array to be: [fetchCategories]", {"range": "1664", "text": "1665"}, "Update the dependencies array to be: [fetchUpdates, page, rowsPerPage, statusFilter]", {"range": "1666", "text": "1667"}, "Update the dependencies array to be: [fetchSessions, filters]", {"range": "1668", "text": "1669"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", {"range": "1670", "text": "1671"}, "Update the dependencies array to be: [token, tabValue, page, rowsPerPage, fetchMessages]", {"range": "1672", "text": "1673"}, "Update the dependencies array to be: [fetchEarnings, page, rowsPerPage]", {"range": "1674", "text": "1675"}, "Update the dependencies array to be: [fetchWithdrawals, page, rowsPerPage, statusFilter]", {"range": "1676", "text": "1677"}, "Update the dependencies array to be: [t, currentUser, fetchProfileData]", {"range": "1678", "text": "1679"}, "Update the dependencies array to be: [currentUser, fetchProfileData]", {"range": "1680", "text": "1681"}, {"range": "1682", "text": "1681"}, {"range": "1683", "text": "1681"}, "Update the dependencies array to be: [token, t, fetchAvailableHours, fetchWeeklyBreaks]", {"range": "1684", "text": "1685"}, "Update the dependencies array to be: [currentWeekStart, fetchWeeklyBreaks, token]", {"range": "1686", "text": "1687"}, "Update the dependencies array to be: [socket, selectedChat, t]", {"range": "1688", "text": "1689"}, "Update the dependencies array to be: [socket, selectedChat, decreaseUnreadCount]", {"range": "1690", "text": "1691"}, "Update the dependencies array to be: [decreaseUnreadCount, socket]", {"range": "1692", "text": "1693"}, "Update the dependencies array to be: [socket, isConnected, currentUser, t]", {"range": "1694", "text": "1695"}, {"range": "1696", "text": "1645"}, {"range": "1697", "text": "1647"}, {"range": "1698", "text": "1645"}, {"range": "1699", "text": "1647"}, {"range": "1700", "text": "1645"}, {"range": "1701", "text": "1647"}, {"range": "1702", "text": "1671"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", {"range": "1703", "text": "1704"}, "Update the dependencies array to be: [token, page, rowsPerPage, fetchMessages]", {"range": "1705", "text": "1706"}, "Update the dependencies array to be: [page, currentUser, token, appliedFilters, fetchTeachers]", {"range": "1707", "text": "1708"}, {"range": "1709", "text": "1645"}, {"range": "1710", "text": "1647"}, {"range": "1711", "text": "1645"}, {"range": "1712", "text": "1647"}, {"range": "1713", "text": "1645"}, {"range": "1714", "text": "1647"}, "Update the dependencies array to be: [id, token, t]", {"range": "1715", "text": "1716"}, "Update the dependencies array to be: [socket, chatId, currentUser]", {"range": "1717", "text": "1718"}, {"range": "1719", "text": "1645"}, {"range": "1720", "text": "1647"}, {"range": "1721", "text": "1645"}, {"range": "1722", "text": "1647"}, {"range": "1723", "text": "1645"}, {"range": "1724", "text": "1647"}, {"range": "1725", "text": "1689"}, {"range": "1726", "text": "1691"}, {"range": "1727", "text": "1693"}, {"range": "1728", "text": "1706"}, "Update the dependencies array to be: [currentUser, fetchMeetings]", {"range": "1729", "text": "1730"}, {"range": "1731", "text": "1671"}, "Update the dependencies array to be: [success, editingReviewId, teachers]", {"range": "1732", "text": "1733"}, "Update the dependencies array to be: [currentUser, fetchPending, location.pathname]", {"range": "1734", "text": "1735"}, "Update the dependencies array to be: [t, exemptPages, isExemptPage]", {"range": "1736", "text": "1737"}, "Update the dependencies array to be: []", {"range": "1738", "text": "1739"}, "Update the dependencies array to be: [getCameraDevices, isCameraPermissionAllowed]", {"range": "1740", "text": "1741"}, "Update the dependencies array to be: [getAudioDevices, isMicrophonePermissionAllowed]", {"range": "1742", "text": "1743"}, "Update the dependencies array to be: [checkMediaPermission]", {"range": "1744", "text": "1745"}, "Update the dependencies array to be: [onDeviceChanged]", {"range": "1746", "text": "1747"}, "Update the dependencies array to be: [getAudioDevices]", {"range": "1748", "text": "1749"}, "Update the dependencies array to be: [waitingMessages]", {"range": "1750", "text": "1751"}, "Update the dependencies array to be: [leave, meetingData, onClose, participantTz, setIsMeetingLeft]", {"range": "1752", "text": "1753"}, "Update the dependencies array to be: [webcamStream, micStream, screenShareStream, updateStats]", {"range": "1754", "text": "1755"}, "Update the dependencies array to be: [didDeviceChange, setDidDeviceChange]", {"range": "1756", "text": "1757"}, "Update the dependencies array to be: [teacherId, studentId, meetingId]", {"range": "1758", "text": "1759"}, [1993, 1995], "[handleLogout]", [3199, 3236], "[isAuthenticated, token, currentUser, socket]", [3029, 3063], "[socket, isConnected, currentUser, fetchUnreadCount]", [9275, 9323], "[appliedFilters, page, searchFilters.priceRange, t]", [2348, 2349], "", [2348, 2348], "\\", [2357, 2358], [2357, 2357], [2359, 2360], [2359, 2359], [2116, 2151], "[fetchStudents, page, rowsPerPage, searchQuery, t]", [2095, 2130], "[fetchTeachers, page, rowsPerPage, searchQuery, t]", [5698, 5699], [5698, 5698], [5707, 5708], [5707, 5707], [5709, 5710], [5709, 5709], [1057, 1059], "[fetchLanguages]", [1069, 1071], "[fetchCategories]", [2347, 2380], "[fetchUpdates, page, rowsPerPage, statusFilter]", [2831, 2840], "[fetchSessions, filters]", [1299, 1338], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", [2708, 2744], "[token, tabValue, page, rowsPerPage, fetchMessages]", [1259, 1278], "[fetchEarnings, page, rowsPerPage]", [1628, 1661], "[fetchWithdrawals, page, rowsPerPage, statusFilter]", [3445, 3461], "[t, currentUser, fetchProfileData]", [4176, 4189], "[currentUser, fetchProfileData]", [4567, 4580], [5070, 5083], [5022, 5032], "[token, t, fetchAvailableHours, fetchWeeklyBreaks]", [5154, 5179], "[currentWeekStart, fetchWeeklyBreaks, token]", [6755, 6777], "[socket, selectedChat, t]", [8233, 8255], "[socket, selectedChat, decreaseUnreadCount]", [9760, 9768], "[decreaseUnreadCount, socket]", [11815, 11862], "[socket, isConnected, currentUser, t]", [1795, 1796], [1795, 1795], [1804, 1805], [1804, 1804], [1806, 1807], [1806, 1806], [1366, 1405], [1858, 1897], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", [2027, 2053], "[token, page, rowsPerPage, fetchMessages]", [8622, 8664], "[page, currentUser, token, appliedFilters, fetchTeachers]", [12227, 12228], [12227, 12227], [12236, 12237], [12236, 12236], [12238, 12239], [12238, 12238], [3518, 3558], "[id, token, t]", [5529, 5562], "[socket, chatId, currentUser]", [10567, 10568], [10567, 10567], [10576, 10577], [10576, 10576], [10578, 10579], [10578, 10578], [7027, 7049], [8505, 8527], [10032, 10040], [2113, 2139], [1892, 1905], "[current<PERSON><PERSON>, fetchMeetings]", [2364, 2403], [20708, 20734], "[success, editing<PERSON><PERSON><PERSON>wId, teachers]", [3072, 3104], "[currentUser, fetchPending, location.pathname]", [1680, 1696], "[t, exemptPages, isExemptPage]", [1678, 1703], "[]", [4052, 4079], "[getCameraDevices, isCameraPermissionAllowed]", [4135, 4166], "[getAudioDevices, isMicrophonePermissionAllowed]", [4250, 4252], "[checkMediaPermission]", [9249, 9251], "[onDevice<PERSON>hanged]", [12542, 12544], "[getAudioDevices]", [845, 847], "[waitingMessages]", [6972, 7000], "[leave, meeting<PERSON><PERSON>, onClose, participantTz, setIsMeetingLeft]", [6514, 6558], "[webcamStream, micStream, screenShareStream, updateStats]", [1859, 1876], "[didD<PERSON><PERSON><PERSON><PERSON><PERSON>, setDidDeviceChange]", [1269, 1291], "[teacherId, studentId, meetingId]"]