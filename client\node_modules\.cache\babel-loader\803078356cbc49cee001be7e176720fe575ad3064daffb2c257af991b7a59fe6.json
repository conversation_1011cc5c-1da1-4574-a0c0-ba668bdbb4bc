{"ast": null, "code": "function _check_private_redeclaration(obj, privateCollection) {\n  if (privateCollection.has(obj)) {\n    throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n  }\n}\nexport { _check_private_redeclaration as _ };", "map": {"version": 3, "names": ["_check_private_redeclaration", "obj", "privateCollection", "has", "TypeError", "_"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@swc/helpers/esm/_check_private_redeclaration.js"], "sourcesContent": ["function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n"], "mappings": "AAAA,SAASA,4BAA4BA,CAACC,GAAG,EAAEC,iBAAiB,EAAE;EAC1D,IAAIA,iBAAiB,CAACC,GAAG,CAACF,GAAG,CAAC,EAAE;IAC5B,MAAM,IAAIG,SAAS,CAAC,gEAAgE,CAAC;EACzF;AACJ;AACA,SAASJ,4BAA4B,IAAIK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}