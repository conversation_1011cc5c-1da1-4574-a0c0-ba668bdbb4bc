{"ast": null, "code": "var g = (f => (f[f.Left = 0] = \"Left\", f[f.Right = 2] = \"Right\", f))(g || {});\nexport { g as MouseButton };", "map": {"version": 3, "names": ["g", "f", "Left", "Right", "MouseB<PERSON>on"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/components/mouse.js"], "sourcesContent": ["var g=(f=>(f[f.Left=0]=\"Left\",f[f.Right=2]=\"Right\",f))(g||{});export{g as MouseButton};\n"], "mappings": "AAAA,IAAIA,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACD,CAAC,CAACA,CAAC,CAACE,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACF,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAAOA,CAAC,IAAII,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}