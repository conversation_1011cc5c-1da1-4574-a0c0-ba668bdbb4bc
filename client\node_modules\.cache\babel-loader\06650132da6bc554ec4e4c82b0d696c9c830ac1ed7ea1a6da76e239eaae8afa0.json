{"ast": null, "code": "import o, { createContext as H, useContext as E, useEffect as m, useState as u } from \"react\";\nimport { createPortal as g } from \"react-dom\";\nimport { useDisposables as h } from '../hooks/use-disposables.js';\nimport { objectToFormEntries as x } from '../utils/form.js';\nimport { compact as y } from '../utils/render.js';\nimport { Hidden as l, HiddenFeatures as d } from './hidden.js';\nlet f = H(null);\nfunction W(t) {\n  let [e, r] = u(null);\n  return o.createElement(f.Provider, {\n    value: {\n      target: e\n    }\n  }, t.children, o.createElement(l, {\n    features: d.Hidden,\n    ref: r\n  }));\n}\nfunction c({\n  children: t\n}) {\n  let e = E(f);\n  if (!e) return o.createElement(o.Fragment, null, t);\n  let {\n    target: r\n  } = e;\n  return r ? g(o.createElement(o.Fragment, null, t), r) : null;\n}\nfunction j({\n  data: t,\n  form: e,\n  disabled: r,\n  onReset: n,\n  overrides: F\n}) {\n  let [i, a] = u(null),\n    p = h();\n  return m(() => {\n    if (n && i) return p.addEventListener(i, \"reset\", n);\n  }, [i, e, n]), o.createElement(c, null, o.createElement(C, {\n    setForm: a,\n    formId: e\n  }), x(t).map(([s, v]) => o.createElement(l, {\n    features: d.Hidden,\n    ...y({\n      key: s,\n      as: \"input\",\n      type: \"hidden\",\n      hidden: !0,\n      readOnly: !0,\n      form: e,\n      disabled: r,\n      name: s,\n      value: v,\n      ...F\n    })\n  })));\n}\nfunction C({\n  setForm: t,\n  formId: e\n}) {\n  return m(() => {\n    if (e) {\n      let r = document.getElementById(e);\n      r && t(r);\n    }\n  }, [t, e]), e ? null : o.createElement(l, {\n    features: d.Hidden,\n    as: \"input\",\n    type: \"hidden\",\n    hidden: !0,\n    readOnly: !0,\n    ref: r => {\n      if (!r) return;\n      let n = r.closest(\"form\");\n      n && t(n);\n    }\n  });\n}\nexport { j as FormFields, W as FormFieldsProvider, c as HoistFormFields };", "map": {"version": 3, "names": ["o", "createContext", "H", "useContext", "E", "useEffect", "m", "useState", "u", "createPortal", "g", "useDisposables", "h", "objectToFormEntries", "x", "compact", "y", "Hidden", "l", "HiddenFeatures", "d", "f", "W", "t", "e", "r", "createElement", "Provider", "value", "target", "children", "features", "ref", "c", "Fragment", "j", "data", "form", "disabled", "onReset", "n", "overrides", "F", "i", "a", "p", "addEventListener", "C", "setForm", "formId", "map", "s", "v", "key", "as", "type", "hidden", "readOnly", "name", "document", "getElementById", "closest", "<PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Ho<PERSON><PERSON><PERSON><PERSON><PERSON>s"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/internal/form-fields.js"], "sourcesContent": ["import o,{createContext as H,useContext as E,useEffect as m,useState as u}from\"react\";import{createPortal as g}from\"react-dom\";import{useDisposables as h}from'../hooks/use-disposables.js';import{objectToFormEntries as x}from'../utils/form.js';import{compact as y}from'../utils/render.js';import{Hidden as l,HiddenFeatures as d}from'./hidden.js';let f=H(null);function W(t){let[e,r]=u(null);return o.createElement(f.Provider,{value:{target:e}},t.children,o.createElement(l,{features:d.Hidden,ref:r}))}function c({children:t}){let e=E(f);if(!e)return o.createElement(o.Fragment,null,t);let{target:r}=e;return r?g(o.createElement(o.Fragment,null,t),r):null}function j({data:t,form:e,disabled:r,onReset:n,overrides:F}){let[i,a]=u(null),p=h();return m(()=>{if(n&&i)return p.addEventListener(i,\"reset\",n)},[i,e,n]),o.createElement(c,null,o.createElement(C,{setForm:a,formId:e}),x(t).map(([s,v])=>o.createElement(l,{features:d.Hidden,...y({key:s,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:e,disabled:r,name:s,value:v,...F})})))}function C({setForm:t,formId:e}){return m(()=>{if(e){let r=document.getElementById(e);r&&t(r)}},[t,e]),e?null:o.createElement(l,{features:d.Hidden,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,ref:r=>{if(!r)return;let n=r.closest(\"form\");n&&t(n)}})}export{j as FormFields,W as FormFieldsProvider,c as HoistFormFields};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,WAAW;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,MAAM,IAAIC,CAAC,EAACC,cAAc,IAAIC,CAAC,QAAK,aAAa;AAAC,IAAIC,CAAC,GAACnB,CAAC,CAAC,IAAI,CAAC;AAAC,SAASoB,CAACA,CAACC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAACjB,CAAC,CAAC,IAAI,CAAC;EAAC,OAAOR,CAAC,CAAC0B,aAAa,CAACL,CAAC,CAACM,QAAQ,EAAC;IAACC,KAAK,EAAC;MAACC,MAAM,EAACL;IAAC;EAAC,CAAC,EAACD,CAAC,CAACO,QAAQ,EAAC9B,CAAC,CAAC0B,aAAa,CAACR,CAAC,EAAC;IAACa,QAAQ,EAACX,CAAC,CAACH,MAAM;IAACe,GAAG,EAACP;EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAASQ,CAACA,CAAC;EAACH,QAAQ,EAACP;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACpB,CAAC,CAACiB,CAAC,CAAC;EAAC,IAAG,CAACG,CAAC,EAAC,OAAOxB,CAAC,CAAC0B,aAAa,CAAC1B,CAAC,CAACkC,QAAQ,EAAC,IAAI,EAACX,CAAC,CAAC;EAAC,IAAG;IAACM,MAAM,EAACJ;EAAC,CAAC,GAACD,CAAC;EAAC,OAAOC,CAAC,GAACf,CAAC,CAACV,CAAC,CAAC0B,aAAa,CAAC1B,CAAC,CAACkC,QAAQ,EAAC,IAAI,EAACX,CAAC,CAAC,EAACE,CAAC,CAAC,GAAC,IAAI;AAAA;AAAC,SAASU,CAACA,CAAC;EAACC,IAAI,EAACb,CAAC;EAACc,IAAI,EAACb,CAAC;EAACc,QAAQ,EAACb,CAAC;EAACc,OAAO,EAACC,CAAC;EAACC,SAAS,EAACC;AAAC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAACpC,CAAC,CAAC,IAAI,CAAC;IAACqC,CAAC,GAACjC,CAAC,CAAC,CAAC;EAAC,OAAON,CAAC,CAAC,MAAI;IAAC,IAAGkC,CAAC,IAAEG,CAAC,EAAC,OAAOE,CAAC,CAACC,gBAAgB,CAACH,CAAC,EAAC,OAAO,EAACH,CAAC,CAAC;EAAA,CAAC,EAAC,CAACG,CAAC,EAACnB,CAAC,EAACgB,CAAC,CAAC,CAAC,EAACxC,CAAC,CAAC0B,aAAa,CAACO,CAAC,EAAC,IAAI,EAACjC,CAAC,CAAC0B,aAAa,CAACqB,CAAC,EAAC;IAACC,OAAO,EAACJ,CAAC;IAACK,MAAM,EAACzB;EAAC,CAAC,CAAC,EAACV,CAAC,CAACS,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAAC,CAACC,CAAC,EAACC,CAAC,CAAC,KAAGpD,CAAC,CAAC0B,aAAa,CAACR,CAAC,EAAC;IAACa,QAAQ,EAACX,CAAC,CAACH,MAAM;IAAC,GAAGD,CAAC,CAAC;MAACqC,GAAG,EAACF,CAAC;MAACG,EAAE,EAAC,OAAO;MAACC,IAAI,EAAC,QAAQ;MAACC,MAAM,EAAC,CAAC,CAAC;MAACC,QAAQ,EAAC,CAAC,CAAC;MAACpB,IAAI,EAACb,CAAC;MAACc,QAAQ,EAACb,CAAC;MAACiC,IAAI,EAACP,CAAC;MAACvB,KAAK,EAACwB,CAAC;MAAC,GAAGV;IAAC,CAAC;EAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAASK,CAACA,CAAC;EAACC,OAAO,EAACzB,CAAC;EAAC0B,MAAM,EAACzB;AAAC,CAAC,EAAC;EAAC,OAAOlB,CAAC,CAAC,MAAI;IAAC,IAAGkB,CAAC,EAAC;MAAC,IAAIC,CAAC,GAACkC,QAAQ,CAACC,cAAc,CAACpC,CAAC,CAAC;MAACC,CAAC,IAAEF,CAAC,CAACE,CAAC,CAAC;IAAA;EAAC,CAAC,EAAC,CAACF,CAAC,EAACC,CAAC,CAAC,CAAC,EAACA,CAAC,GAAC,IAAI,GAACxB,CAAC,CAAC0B,aAAa,CAACR,CAAC,EAAC;IAACa,QAAQ,EAACX,CAAC,CAACH,MAAM;IAACqC,EAAE,EAAC,OAAO;IAACC,IAAI,EAAC,QAAQ;IAACC,MAAM,EAAC,CAAC,CAAC;IAACC,QAAQ,EAAC,CAAC,CAAC;IAACzB,GAAG,EAACP,CAAC,IAAE;MAAC,IAAG,CAACA,CAAC,EAAC;MAAO,IAAIe,CAAC,GAACf,CAAC,CAACoC,OAAO,CAAC,MAAM,CAAC;MAACrB,CAAC,IAAEjB,CAAC,CAACiB,CAAC,CAAC;IAAA;EAAC,CAAC,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAI2B,UAAU,EAACxC,CAAC,IAAIyC,kBAAkB,EAAC9B,CAAC,IAAI+B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}