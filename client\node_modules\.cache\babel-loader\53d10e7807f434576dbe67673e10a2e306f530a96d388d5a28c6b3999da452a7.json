{"ast": null, "code": "import { useEffectEvent as $8ae05eaa5c114e9c$export$7f54fc3180508a52 } from \"./useEffectEvent.mjs\";\nimport { useEffect as $ceQd6$useEffect } from \"react\";\n\n/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $e9faafb641e167db$export$90fc3a17d93f704c(ref, event, handler, options) {\n  let handleEvent = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)(handler);\n  let isDisabled = handler == null;\n  (0, $ceQd6$useEffect)(() => {\n    if (isDisabled || !ref.current) return;\n    let element = ref.current;\n    element.addEventListener(event, handleEvent, options);\n    return () => {\n      element.removeEventListener(event, handleEvent, options);\n    };\n  }, [ref, event, options, isDisabled, handleEvent]);\n}\nexport { $e9faafb641e167db$export$90fc3a17d93f704c as useEvent };", "map": {"version": 3, "names": ["$e9faafb641e167db$export$90fc3a17d93f704c", "ref", "event", "handler", "options", "handleEvent", "$8ae05eaa5c114e9c$export$7f54fc3180508a52", "isDisabled", "$ceQd6$useEffect", "current", "element", "addEventListener", "removeEventListener"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useEvent.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useEvent<K extends keyof GlobalEventHandlersEventMap>(\n  ref: RefObject<EventTarget | null>,\n  event: K | (string & {}),\n  handler?: (this: Document, ev: GlobalEventHandlersEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions\n): void {\n  let handleEvent = useEffectEvent(handler);\n  let isDisabled = handler == null;\n\n  useEffect(() => {\n    if (isDisabled || !ref.current) {\n      return;\n    }\n\n    let element = ref.current;\n    element.addEventListener(event, handleEvent as EventListener, options);\n    return () => {\n      element.removeEventListener(event, handleEvent as EventListener, options);\n    };\n  }, [ref, event, options, isDisabled, handleEvent]);\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAgBO,SAASA,0CACdC,GAAkC,EAClCC,KAAwB,EACxBC,OAAqE,EACrEC,OAA2C;EAE3C,IAAIC,WAAA,GAAc,IAAAC,yCAAa,EAAEH,OAAA;EACjC,IAAII,UAAA,GAAaJ,OAAA,IAAW;EAE5B,IAAAK,gBAAQ,EAAE;IACR,IAAID,UAAA,IAAc,CAACN,GAAA,CAAIQ,OAAO,EAC5B;IAGF,IAAIC,OAAA,GAAUT,GAAA,CAAIQ,OAAO;IACzBC,OAAA,CAAQC,gBAAgB,CAACT,KAAA,EAAOG,WAAA,EAA8BD,OAAA;IAC9D,OAAO;MACLM,OAAA,CAAQE,mBAAmB,CAACV,KAAA,EAAOG,WAAA,EAA8BD,OAAA;IACnE;EACF,GAAG,CAACH,GAAA,EAAKC,KAAA,EAAOE,OAAA,EAASG,UAAA,EAAYF,WAAA,CAAY;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}