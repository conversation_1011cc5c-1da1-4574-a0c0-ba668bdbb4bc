{"ast": null, "code": "import { createContext as t, useContext as n, use<PERSON>emo as i } from \"react\";\nimport { useOnUnmount as c } from '../../hooks/use-on-unmount.js';\nimport { PopoverMachine as p } from './popover-machine.js';\nconst a = t(null);\nfunction u(r) {\n  let o = n(a);\n  if (o === null) {\n    let e = new Error(`<${r} /> is missing a parent <Popover /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(e, u), e;\n  }\n  return o;\n}\nfunction f({\n  id: r,\n  __demoMode: o = !1\n}) {\n  let e = i(() => p.new({\n    id: r,\n    __demoMode: o\n  }), []);\n  return c(() => e.dispose()), e;\n}\nexport { a as PopoverContext, f as usePopoverMachine, u as usePopoverMachineContext };", "map": {"version": 3, "names": ["createContext", "t", "useContext", "n", "useMemo", "i", "useOnUnmount", "c", "PopoverMachine", "p", "a", "u", "r", "o", "e", "Error", "captureStackTrace", "f", "id", "__demoMode", "new", "dispose", "PopoverContext", "usePopoverMachine", "usePopoverMachineContext"], "sources": ["D:/xampp/htdocs/allemnionline/client/node_modules/@headlessui/react/dist/components/popover/popover-machine-glue.js"], "sourcesContent": ["import{createContext as t,useContext as n,use<PERSON>emo as i}from\"react\";import{useOnUnmount as c}from'../../hooks/use-on-unmount.js';import{PopoverMachine as p}from'./popover-machine.js';const a=t(null);function u(r){let o=n(a);if(o===null){let e=new Error(`<${r} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return o}function f({id:r,__demoMode:o=!1}){let e=i(()=>p.new({id:r,__demoMode:o}),[]);return c(()=>e.dispose()),e}export{a as PopoverContext,f as usePopoverMachine,u as usePopoverMachineContext};\n"], "mappings": "AAAA,SAAOA,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,MAAMC,CAAC,GAACT,CAAC,CAAC,IAAI,CAAC;AAAC,SAASU,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACV,CAAC,CAACO,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,IAAIH,CAAC,gDAAgD,CAAC;IAAC,MAAMG,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACF,CAAC,EAACH,CAAC,CAAC,EAACG,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASI,CAACA,CAAC;EAACC,EAAE,EAACN,CAAC;EAACO,UAAU,EAACN,CAAC,GAAC,CAAC;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACT,CAAC,CAAC,MAAII,CAAC,CAACW,GAAG,CAAC;IAACF,EAAE,EAACN,CAAC;IAACO,UAAU,EAACN;EAAC,CAAC,CAAC,EAAC,EAAE,CAAC;EAAC,OAAON,CAAC,CAAC,MAAIO,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,EAACP,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIY,cAAc,EAACL,CAAC,IAAIM,iBAAiB,EAACZ,CAAC,IAAIa,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}