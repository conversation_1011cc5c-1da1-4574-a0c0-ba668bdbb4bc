{"ast": null, "code": "import { useRef as $9vW05$useRef, useEffect as $9vW05$useEffect } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $4f58c5f72bcf79f7$export$496315a1608d9602(effect, dependencies) {\n  const isInitialMount = (0, $9vW05$useRef)(true);\n  const lastDeps = (0, $9vW05$useRef)(null);\n  (0, $9vW05$useEffect)(() => {\n    isInitialMount.current = true;\n    return () => {\n      isInitialMount.current = false;\n    };\n  }, []);\n  (0, $9vW05$useEffect)(() => {\n    let prevDeps = lastDeps.current;\n    if (isInitialMount.current) isInitialMount.current = false;else if (!prevDeps || dependencies.some((dep, i) => !Object.is(dep, prevDeps[i]))) effect();\n    lastDeps.current = dependencies;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n}\nexport { $4f58c5f72bcf79f7$export$496315a1608d9602 as useUpdateEffect };", "map": {"version": 3, "names": ["$4f58c5f72bcf79f7$export$496315a1608d9602", "effect", "dependencies", "isInitialMount", "$9vW05$useRef", "lastDeps", "$9vW05$useEffect", "current", "prevDeps", "some", "dep", "i", "Object", "is"], "sources": ["D:\\xampp\\htdocs\\allemnionline\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useUpdateEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {EffectCallback, useEffect, useRef} from 'react';\n\n// Like useEffect, but only called for updates after the initial render.\nexport function useUpdateEffect(effect: EffectCallback, dependencies: any[]): void {\n  const isInitialMount = useRef(true);\n  const lastDeps = useRef<any[] | null>(null);\n\n  useEffect(() => {\n    isInitialMount.current = true;\n    return () => {\n      isInitialMount.current = false;\n    };\n  }, []);\n\n  useEffect(() => {\n    let prevDeps = lastDeps.current;\n    if (isInitialMount.current) {\n      isInitialMount.current = false;\n    } else if (!prevDeps || dependencies.some((dep, i) => !Object.is(dep, prevDeps[i]))) {\n      effect();\n    }\n    lastDeps.current = dependencies;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAeO,SAASA,0CAAgBC,MAAsB,EAAEC,YAAmB;EACzE,MAAMC,cAAA,GAAiB,IAAAC,aAAK,EAAE;EAC9B,MAAMC,QAAA,GAAW,IAAAD,aAAK,EAAgB;EAEtC,IAAAE,gBAAQ,EAAE;IACRH,cAAA,CAAeI,OAAO,GAAG;IACzB,OAAO;MACLJ,cAAA,CAAeI,OAAO,GAAG;IAC3B;EACF,GAAG,EAAE;EAEL,IAAAD,gBAAQ,EAAE;IACR,IAAIE,QAAA,GAAWH,QAAA,CAASE,OAAO;IAC/B,IAAIJ,cAAA,CAAeI,OAAO,EACxBJ,cAAA,CAAeI,OAAO,GAAG,WACpB,IAAI,CAACC,QAAA,IAAYN,YAAA,CAAaO,IAAI,CAAC,CAACC,GAAA,EAAKC,CAAA,KAAM,CAACC,MAAA,CAAOC,EAAE,CAACH,GAAA,EAAKF,QAAQ,CAACG,CAAA,CAAE,IAC/EV,MAAA;IAEFI,QAAA,CAASE,OAAO,GAAGL,YAAA;IACnB;EACF,GAAGA,YAAA;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}